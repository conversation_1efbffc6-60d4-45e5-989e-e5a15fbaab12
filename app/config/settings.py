"""
应用设置配置
"""

import os
from functools import lru_cache
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # Flask 配置
    flask_env: str = Field(default="development", env="FLASK_ENV")
    flask_debug: bool = Field(default=True, env="FLASK_DEBUG")
    flask_secret_key: str = Field(default="dev-secret-key", env="FLASK_SECRET_KEY")
    
    # API 配置
    api_prefix: str = Field(default="/api/v1", env="API_PREFIX")
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    
    # 数据加密配置
    encryption_key: str = Field(default="dev-encryption-key", env="ENCRYPTION_KEY")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/app.log", env="LOG_FILE")
    
    # 数据库配置
    impala_host: str = Field(default="*************", env="IMPALA_HOST")
    impala_port: int = Field(default=21050, env="IMPALA_PORT")
    impala_database: str = Field(default="default", env="IMPALA_DATABASE")
    impala_query_timeout: int = Field(default=300, env="IMPALA_QUERY_TIMEOUT")
    impala_auth_type: str = Field(default="NONE", env="IMPALA_AUTH_TYPE")
    impala_username: Optional[str] = Field(default=None, env="IMPALA_USERNAME")
    impala_password: Optional[str] = Field(default=None, env="IMPALA_PASSWORD")
    impala_principal: Optional[str] = Field(default=None, env="IMPALA_PRINCIPAL")
    impala_use_ssl: bool = Field(default=False, env="IMPALA_USE_SSL")
    impala_ssl_truststore: Optional[str] = Field(default=None, env="IMPALA_SSL_TRUSTSTORE")
    
    # Neo4j 配置
    neo4j_uri: str = Field(default="bolt://*************:7687", env="NEO4J_URI")
    neo4j_username: str = Field(default="neo4j", env="NEO4J_USERNAME")
    neo4j_password: str = Field(default="bzn@1234", env="NEO4J_PASSWORD")
    neo4j_database: str = Field(default="neo4j", env="NEO4J_DATABASE")
    
    # 数据处理分页配置
    data_page_size: int = Field(default=2000, env="DATA_PAGE_SIZE", description="从Hive查询的分页大小")
    data_batch_size: int = Field(default=500, env="DATA_BATCH_SIZE", description="存储到Neo4j的批次大小")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置单例"""
    return Settings() 