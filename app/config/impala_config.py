"""
Impala数据库配置模块
"""

from dataclasses import dataclass
from typing import Optional

from .settings import get_settings


@dataclass
class AuthConfig:
    """认证配置"""
    auth_type: str = "NONE"  # NONE, KERBEROS, LDAP
    username: Optional[str] = None
    password: Optional[str] = None
    principal: Optional[str] = None
    use_ssl: bool = False
    ssl_truststore: Optional[str] = None


@dataclass
class ImpalaConfig:
    """Impala数据库配置"""
    host: str
    port: int
    database: str
    query_timeout: int
    auth_config: AuthConfig


def load_impala_config() -> ImpalaConfig:
    """从设置中加载Impala配置"""
    settings = get_settings()
    
    auth_config = AuthConfig(
        auth_type=settings.impala_auth_type,
        username=settings.impala_username,
        password=settings.impala_password,
        principal=settings.impala_principal,
        use_ssl=settings.impala_use_ssl,
        ssl_truststore=settings.impala_ssl_truststore
    )
    
    return ImpalaConfig(
        host=settings.impala_host,
        port=settings.impala_port,
        database=settings.impala_database,
        query_timeout=settings.impala_query_timeout,
        auth_config=auth_config
    ) 