"""
Neo4j数据库配置模块
"""

from dataclasses import dataclass

from .settings import get_settings


@dataclass
class Neo4jConfig:
    """Neo4j数据库配置"""
    uri: str
    username: str
    password: str
    database: str


def load_neo4j_config() -> Neo4jConfig:
    """从设置中加载Neo4j配置"""
    settings = get_settings()
    
    return Neo4jConfig(
        uri=settings.neo4j_uri,
        username=settings.neo4j_username,
        password=settings.neo4j_password,
        database=settings.neo4j_database
    ) 