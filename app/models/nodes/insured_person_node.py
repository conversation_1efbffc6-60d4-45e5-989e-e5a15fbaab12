"""
被保险人节点模型
"""
from typing import List, Optional
from pydantic import Field
from .person_node import PersonNode


class InsuredPersonNode(PersonNode):
    """被保险人节点"""
    
    # 被保险人目前没有特有的额外属性
    # 所有属性都从PersonNode继承
    
    def get_label(self) -> str:
        return "InsuredPerson"
    
    @property
    def labels(self) -> List[str]:
        """获取多标签列表，支持Person和InsuredPerson双标签"""
        return ["Person", "InsuredPerson"]
