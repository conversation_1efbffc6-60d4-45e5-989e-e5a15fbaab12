"""
保险商务拓展人员节点模型
"""
from typing import List, Optional
from pydantic import Field
from .person_node import PersonNode


class InsuranceBDPersonNode(PersonNode):
    """保险商务拓展人员节点"""
    
    # 联系方式（子类特有）
    phone: Optional[str] = Field(default=None, description="手机号码")
    email: Optional[str] = Field(default=None, description="邮箱地址")
    
    # 地理位置信息（子类特有）
    province_code: Optional[str] = Field(default=None, description="省份编码")
    province_name: Optional[str] = Field(default=None, description="省份名称")
    city_code: Optional[str] = Field(default=None, description="城市编码")
    city_name: Optional[str] = Field(default=None, description="城市名称")
    county_code: Optional[str] = Field(default=None, description="县区编码")
    county_name: Optional[str] = Field(default=None, description="县区名称")
    
    # 业务关联信息（子类特有）
    referrer_user_id: Optional[str] = Field(default=None, description="推荐人用户ID")
    bdm_user_id: Optional[str] = Field(default=None, description="BDM用户ID")
    channel_id: Optional[str] = Field(default=None, description="渠道ID")
    team_id: Optional[str] = Field(default=None, description="团队ID")
    status: Optional[str] = Field(default=None, description="状态")
    
    def get_label(self) -> str:
        return "InsuranceBDPerson"
    
    @property
    def labels(self) -> List[str]:
        """获取多标签列表，支持Person和InsuranceBDPerson双标签"""
        return ["Person", "InsuranceBDPerson"]
    

 