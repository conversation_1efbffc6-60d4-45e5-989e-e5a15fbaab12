"""
保单节点模型

定义保单的基础属性和验证规则
"""
from typing import Optional
from datetime import date, datetime
from decimal import Decimal

from pydantic import Field, field_validator
from ..base.base_model import BaseNode


class PolicyNode(BaseNode):
    """
    保单节点模型
    
    表示保险合同的基础信息
    """
    # 基础保单信息
    policy_code: str = Field(
        ...,
        max_length=100,
        description="保险公司系统生成的保单编号"
    )
    
    # 保单时间信息
    policy_start_date: Optional[date] = Field(
        default=None,
        description="保单起始日期"
    )
    
    policy_end_date: Optional[date] = Field(
        default=None,
        description="保单结束日期"
    )
    
    assure_time: Optional[datetime] = Field(
        default=None,
        description="承保时间"
    )
    
    # 保单状态
    policy_status: Optional[str] = Field(
        default=None,
        max_length=50,
        description="保单状态"
    )
    
    # 保险公司信息
    insurance_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="保险公司编码"
    )
    
    insurance_name: Optional[str] = Field(
        default=None,
        max_length=200,
        description="保险公司名称"
    )
    
    # 投保人信息
    insure_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="投保人编码"
    )
    
    insure_name: Optional[str] = Field(
        default=None,
        max_length=100,
        description="投保人姓名"
    )
    
    # 被保险人信息
    insured_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="被保险人编码"
    )
    
    insured_name: Optional[str] = Field(
        default=None,
        max_length=100,
        description="被保险人姓名"
    )
    
    # 产品信息
    product_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="产品编码"
    )
    
    product_name: Optional[str] = Field(
        default=None,
        max_length=200,
        description="产品名称"
    )
    
    # 渠道信息
    channel_id: Optional[str] = Field(
        default=None,
        max_length=50,
        description="渠道ID"
    )
    
    channel_name: Optional[str] = Field(
        default=None,
        max_length=200,
        description="渠道名称"
    )
    
    # 销售人员信息
    sale_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="销售人员编码"
    )
    
    sale_name: Optional[str] = Field(
        default=None,
        max_length=100,
        description="销售人员姓名"
    )
    
    sale_mobile: Optional[str] = Field(
        default=None,
        max_length=20,
        description="销售人员手机号"
    )
    
    # 业务数据
    sku_ratio: Optional[Decimal] = Field(
        default=None,
        description="SKU比例"
    )
    
    policy_coverage: Optional[Decimal] = Field(
        default=None,
        description="保单覆盖金额"
    )
    
    # 行业信息
    industry: Optional[str] = Field(
        default=None,
        max_length=100,
        description="行业分类"
    )
    
    @field_validator('policy_code')
    @classmethod
    def validate_policy_code(cls, v):
        """验证保司保单号格式"""
        if not v or v.strip() == "":
            raise ValueError("保司保单号不能为空")
        
        policy_code = v.strip()
        # 保单号通常包含字母和数字
        if len(policy_code) < 8:
            raise ValueError("保单号长度不能少于8位")
        
        return policy_code
    
    def get_label(self) -> str:
        """获取节点标签"""
        return "Policy" 