"""
职业节点模型
"""
from typing import Optional, List
from pydantic import Field
from ..base.base_model import BaseNode


class OccupationNode(BaseNode):
    """职业节点基类"""
    
    # 职业特有字段
    occupation_name: Optional[str] = Field(default=None, description="职业名称")
    occupation_code: Optional[str] = Field(default=None, description="职业代码")
    
    # 行业分类信息
    industry_large: Optional[str] = Field(default=None, description="行业大类")
    industry_medium: Optional[str] = Field(default=None, description="行业中类")
    industry_small: Optional[str] = Field(default=None, description="行业小类")
    
    # 职业等级
    work_type_level: Optional[str] = Field(default=None, description="职业类型等级")
    
    @property
    def labels(self) -> List[str]:
        """获取多标签列表"""
        return ["Occupation"]
        
    def get_label(self) -> str:
        """获取主要节点标签"""
        return "Occupation"