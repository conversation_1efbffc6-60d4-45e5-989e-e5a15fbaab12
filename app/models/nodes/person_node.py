"""
人员节点模型

定义人员的基础属性和验证规则
"""
import re
from typing import Optional, Dict, Any

from pydantic import Field, field_validator
from ..base.base_model import BaseNode


class PersonNode(BaseNode):
    """
    人员节点模型
    
    表示各类人员的基础信息
    """
    
    # 人员特有属性
    gender: Optional[str] = Field(
        default=None,
        description="人员性别信息"
    )
    id_card_number: Optional[str] = Field(
        default=None,
        description="身份证号码"
    )
    tianyancha_person_id: Optional[str] = Field(
        default=None,
        max_length=100,
        description="天眼查平台的人员唯一标识"
    )
    data_source_node: Optional[str] = Field(
        default=None,
        max_length=50,
        description="数据来源节点类型"
    )
    data_source_id: Optional[str] = Field(
        default=None,
        max_length=100,
        description="数据来源ID（如保单号）"
    )
    data_source_organization_name: Optional[str] = Field(
        default=None,
        max_length=200,
        description="数据来源组织名称"
    )
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "name": "张三",
                "gender": "男",
                "id_card_number": "110101199001011234",
                "tianyancha_person_id": "TYC123456789",
                "data_source_node": "Policy",
                "data_source_id": "POL123456789",
                "data_source_organization_name": "某某保险公司"
            }
        }
    
    @field_validator('gender')
    @classmethod
    def validate_gender(cls, v):
        """验证性别格式"""
        if v is None:
            return v
        
        gender = v.strip() if v else ""
        if gender == "":
            return None
        
        # 由于性别转换已在Hive SQL中统一处理，这里只做基本的清理
        return gender
    
    @field_validator('id_card_number')
    @classmethod
    def validate_id_card(cls, v):
        """验证身份证号码格式"""
        if v is None:
            return v
        
        # 移除空格
        id_card = v.strip().replace(" ", "")
        if id_card == "":
            return None
        
        # 18位身份证号码格式验证
        if not re.match(r'^\d{17}[\dXx]$', id_card):
            raise ValueError("身份证号码格式不正确")
        
        return id_card.upper()
    
    @field_validator('tianyancha_person_id')
    @classmethod
    def validate_tianyancha_id(cls, v):
        """验证天眼查ID格式"""
        if v is None:
            return v
        
        tianyancha_id = v.strip() if v else ""
        if tianyancha_id == "":
            return None
        
        return tianyancha_id
    
    @field_validator('data_source_node')
    @classmethod
    def validate_data_source_node(cls, v):
        """验证数据来源节点格式"""
        if v is None:
            return v
        
        source_node = v.strip() if v else ""
        if source_node == "":
            return None
        
        return source_node
    
    @field_validator('data_source_id')
    @classmethod
    def validate_data_source_id(cls, v):
        """验证数据来源ID格式"""
        if v is None:
            return v
        
        source_id = v.strip() if v else ""
        if source_id == "":
            return None
        
        return source_id
    
    @field_validator('data_source_organization_name')
    @classmethod
    def validate_data_source_organization_name(cls, v):
        """验证数据来源组织名称格式"""
        if v is None:
            return v
        
        org_name = v.strip() if v else ""
        if org_name == "":
            return None
        
        return org_name
    
    def get_label(self) -> str:
        """获取节点标签"""
        return "Person" 