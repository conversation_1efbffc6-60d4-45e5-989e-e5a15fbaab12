"""
组织节点模型
"""
from typing import Optional
from pydantic import Field
from ..base.base_model import BaseNode


class OrganizationNode(BaseNode):
    """组织节点"""
    
    # 组织特有字段
    short_name: Optional[str] = Field(default=None, description="机构简称")
    unified_social_credit_code: Optional[str] = Field(default=None, description="统一社会信用代码")
    company_address: Optional[str] = Field(default=None, description="机构地址")
    
    # 法定代表人信息
    legal_representative: Optional[str] = Field(default=None, description="法定代表人")
    
    # 联系方式
    phone: Optional[str] = Field(default=None, description="电话")
    additional_phones: Optional[str] = Field(default=None, description="附加电话")
    email: Optional[str] = Field(default=None, description="邮箱")
    additional_emails: Optional[str] = Field(default=None, description="附加邮箱")
    
    # 地理位置信息
    province_code: Optional[str] = Field(default=None, description="省份代码")
    province: Optional[str] = Field(default=None, description="省份")
    city_code: Optional[str] = Field(default=None, description="城市代码")
    city: Optional[str] = Field(default=None, description="城市")
    district_code: Optional[str] = Field(default=None, description="区县代码")
    district: Optional[str] = Field(default=None, description="区县")
    
    # 行业分类
    industry_category: Optional[str] = Field(default=None, description="行业分类")
    industry_major: Optional[str] = Field(default=None, description="行业大类")
    industry_medium: Optional[str] = Field(default=None, description="行业中类")
    industry_minor: Optional[str] = Field(default=None, description="行业小类")
    
    # 数据来源信息
    data_source_node: Optional[str] = Field(default=None, description="数据来源节点类型")
    
    def get_label(self) -> str:
        return "Organization" 