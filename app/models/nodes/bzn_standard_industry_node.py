"""
BZN标准行业节点模型
"""
from typing import Optional, List
from pydantic import Field
from .industry_node import IndustryNode


class BznStandardIndustryNode(IndustryNode):
    """BZN标准行业节点"""
    
    # 行业特有字段
    keywords: Optional[str] = Field(default=None, description="关键词")
    
    @property
    def labels(self) -> List[str]:
        """获取多标签列表"""
        return ["Industry", "BznStandardIndustry"]
        
    def get_label(self) -> str:
        """获取主要节点标签"""
        return "BznStandardIndustry" 