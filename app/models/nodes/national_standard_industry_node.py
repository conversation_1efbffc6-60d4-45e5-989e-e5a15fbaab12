"""
国家标准行业节点模型
"""
from typing import Optional, List
from pydantic import Field
from .industry_node import IndustryNode


class NationalStandardIndustryNode(IndustryNode):
    """国家标准行业节点"""
    
    # 国家标准行业特有字段
    parent_code: Optional[str] = Field(default=None, description="父级行业编码")
    level: Optional[int] = Field(default=None, description="行业层级 (1:门类, 2:大类, 3:中类, 4:小类)")
    
    @property
    def labels(self) -> List[str]:
        """获取多标签列表"""
        return ["Industry", "NationalStandardIndustry"]
    
    def get_label(self) -> str:
        """获取主要节点标签"""
        return "NationalStandardIndustry" 