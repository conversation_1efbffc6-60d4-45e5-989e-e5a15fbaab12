"""
保险理赔节点模型
"""
from typing import Optional
from datetime import datetime
from pydantic import Field
from ..base.base_model import BaseNode


class InsuranceClaimsNode(BaseNode):
    """保险理赔节点"""
    
    # 基础标识字段
    case_no: Optional[str] = Field(default=None, description="案件号/报案号")
    policy_code: Optional[str] = Field(default=None, description="保单号")
    
    # 被保险人信息
    insured_user_id: Optional[str] = Field(default=None, description="被保险人员ID")
    insured_user_cert_no: Optional[str] = Field(default=None, description="被保险人员证件号码")
    insured_user_name: Optional[str] = Field(default=None, description="被保险人姓名")
    
    # 投保公司信息
    insure_code: Optional[str] = Field(default=None, description="投保公司代码")
    insure_name: Optional[str] = Field(default=None, description="投保公司名称")
    
    # 被保公司信息
    insured_code: Optional[str] = Field(default=None, description="被保公司代码")
    insured_name: Optional[str] = Field(default=None, description="被保公司名称")
    
    # 保险公司信息
    insurance_code: Optional[str] = Field(default=None, description="保险公司统一社会信用代码")
    insurance_name: Optional[str] = Field(default=None, description="保险公司名称")
    
    # 报案人信息
    report_name: Optional[str] = Field(default=None, description="报案人姓名")
    report_mobile: Optional[str] = Field(default=None, description="报案人电话")
    report_is_agent: Optional[str] = Field(default=None, description="报案人是否代理")
    
    # 时间信息
    report_date: Optional[datetime] = Field(default=None, description="报案时间")
    risk_date: Optional[datetime] = Field(default=None, description="出险时间")
    register_date: Optional[datetime] = Field(default=None, description="立案时间")
    case_close_date: Optional[datetime] = Field(default=None, description="结案日期")
    
    # 工种信息
    work_type_name: Optional[str] = Field(default=None, description="投保工种或运动项目")
    work_type_level: Optional[str] = Field(default=None, description="工种或场馆等级")
    
    # 出险地理位置信息
    risk_province_code: Optional[str] = Field(default=None, description="出险省code")
    risk_province: Optional[str] = Field(default=None, description="出险省")
    risk_city_code: Optional[str] = Field(default=None, description="出险市code")
    risk_city: Optional[str] = Field(default=None, description="出险市")
    risk_district_code: Optional[str] = Field(default=None, description="出险区code")
    risk_district: Optional[str] = Field(default=None, description="出险区")
    
    # 出险场景信息
    risk_scene: Optional[str] = Field(default=None, description="出险场景")
    risk_scene_one: Optional[str] = Field(default=None, description="出险场景大类")
    risk_scene_two: Optional[str] = Field(default=None, description="出险场景中类")
    risk_scene_three: Optional[str] = Field(default=None, description="出险场景小类")
    
    # 伤情信息
    injured_category: Optional[str] = Field(default=None, description="伤情类别")
    disable_level: Optional[str] = Field(default=None, description="伤残等级")
    injured_part: Optional[str] = Field(default=None, description="受伤部位")
    visit_hospitals: Optional[str] = Field(default=None, description="就诊医院")
    closed_injured_category: Optional[str] = Field(default=None, description="结案伤情类型")
    closed_disable_level: Optional[str] = Field(default=None, description="结案伤残等级")
    
    # 案件状态和金额
    case_status: Optional[str] = Field(default=None, description="案件状态[已决、未决]")
    compensate_result: Optional[str] = Field(default=None, description="案件结论")
    register_amt: Optional[float] = Field(default=None, description="立案金额")
    compensate_amt: Optional[float] = Field(default=None, description="赔付金额")
    
    # 数据来源信息
    data_source_node: Optional[str] = Field(default=None, description="数据来源节点类型")
    
    def get_label(self) -> str:
        return "InsuranceClaims" 