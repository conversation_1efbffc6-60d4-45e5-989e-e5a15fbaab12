"""
区域节点模型

包含区域的基本信息，如城市编码、名称、父级编码和层级等
"""

from typing import Optional
from pydantic import Field, field_validator
from ..base.base_model import BaseNode


class AreaNode(BaseNode):
    """区域节点模型"""
    
    # 区域特有属性
    parent_code: Optional[str] = Field(
        default=None,
        max_length=50,
        description="父级区域编码 (city_parent_code)"
    )
    level: Optional[int] = Field(
        default=None,
        description="区域层级 (city_level)"
    )
    
    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "code": "110000",
                "name": "北京市",
                "parent_code": "100000",
                "level": 2
            }
        }
    
    @field_validator('code', 'name')
    @classmethod
    def validate_required_fields(cls, v):
        """验证必填字段不能为空"""
        if not v or v.strip() == "":
            raise ValueError("区域编码和名称不能为空")
        return v.strip()
    
    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        """验证区域层级"""
        if v is not None and (v < 0 or v > 10):
            raise ValueError("区域层级必须在0-10之间")
        return v
    
    def get_label(self) -> str:
        """获取节点标签"""
        return "Area"
    

    
    def get_display_name(self) -> str:
        """获取显示名称，包含编码信息"""
        return f"{self.name} ({self.code})"
    
    def get_hierarchy_info(self) -> str:
        """获取层级信息描述"""
        level_names = {
            0: "国家级",
            1: "省级",
            2: "市级", 
            3: "区县级"
        }
        level_name = level_names.get(self.level, f"第{self.level}级") if self.level is not None else "未知级别"
        return f"{level_name}区域"