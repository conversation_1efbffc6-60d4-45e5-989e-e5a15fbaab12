"""
保险产品节点模型
"""
from typing import Optional
from pydantic import Field
from ..base.base_model import BaseNode


class InsuranceProductNode(BaseNode):
    """保险产品节点"""
    
    # 基础产品信息
    product_id: Optional[str] = Field(default=None, description="产品ID")
    product_name: Optional[str] = Field(default=None, description="产品名称")
    short_name: Optional[str] = Field(default=None, description="产品简称")
    product_code: Optional[str] = Field(default=None, description="产品代码")
    product_desc: Optional[str] = Field(default=None, description="产品描述")
    
    # 保险公司信息
    insurance_name: Optional[str] = Field(default=None, description="保险公司名称")
    insurance_code: Optional[str] = Field(default=None, description="保险公司代码")
    insurance_unified_social_credit_code: Optional[str] = Field(default=None, description="保险公司统一社会信用代码")
    
    # 产品分类信息
    one_level_pdt_cate: Optional[str] = Field(default=None, description="一级产品分类")
    two_level_pdt_cate: Optional[str] = Field(default=None, description="二级产品分类")
    product_type: Optional[str] = Field(default=None, description="产品类型")
    business_line: Optional[str] = Field(default=None, description="业务线")
    sale_category: Optional[str] = Field(default=None, description="销售类别")
    
    # 系统信息
    system_source: Optional[str] = Field(default=None, description="系统来源")
    business_source: Optional[str] = Field(default=None, description="业务来源")
    
    def get_label(self) -> str:
        return "InsuranceProduct" 