"""
特殊名单节点模型

定义特殊名单的基础属性和验证规则
"""
from typing import Optional
from datetime import date, datetime

from pydantic import Field
from ..base.base_model import BaseNode


class SpecialListNode(BaseNode):
    """
    特殊名单节点模型
    
    存储各种特殊名单信息
    """
    
    # 基础标识字段
    list_id: str = Field(
        ...,
        max_length=100,
        description="特殊名单ID"
    )
    
    # 名单分类信息
    list_type: Optional[int] = Field(
        default=None,
        description="名单类型[1-黑名单,2-灰名单,3-白名单]"
    )
    
    list_category: Optional[int] = Field(
        default=None,
        description="名单种类[1-个人,2-企业,3-工种,4-地区,5-医疗机构,6-行业,7-运动项目,8-场馆类别]"
    )
    
    # 数据来源信息
    data_source: Optional[str] = Field(
        default=None,
        max_length=200,
        description="数据来源"
    )
    
    data_source_type: Optional[str] = Field(
        default=None,
        max_length=100,
        description="数据来源类型"
    )
    
    # 风险信息
    risk_level: Optional[str] = Field(
        default=None,
        max_length=50,
        description="风险等级"
    )
    
    # 状态信息
    status: Optional[str] = Field(
        default=None,
        max_length=50,
        description="状态"
    )
    
    # 时间信息
    effective_day: Optional[date] = Field(
        default=None,
        description="生效日期"
    )
    
    # 其他信息
    reason: Optional[str] = Field(
        default=None,
        max_length=500,
        description="列入原因"
    )
    
    remark: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="备注信息"
    )
    
    def get_label(self) -> str:
        """获取节点标签"""
        return "SpecialList" 