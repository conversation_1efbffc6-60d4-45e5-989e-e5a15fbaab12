"""
数据模型定义模块 - 统一的节点模型结构

所有模型统一放在 nodes 目录下，实现清晰的模型组织结构
"""

# 节点模型 - 从 nodes 目录导入所有模型
from .nodes import (
    # 基础节点
    OrganizationNode,           # 组织
    PersonNode,                 # 人员
    AreaNode,                   # 区域
    PolicyNode,                 # 保单
    
    # 专业节点
    InsuranceProductNode,       # 保险产品
    InsuranceCompanyNode,       # 保险公司
    InsuranceBrokerageCompanyNode,  # 保险经纪公司
    PolicyCompanyNode,          # 保单企业
    InsuranceClaimsNode,        # 保险理赔

    
    # 人员专业节点
    InsuranceAgentPersonNode,   # 保险代理人
    InsuranceBDPersonNode,      # 保险商务拓展人员
    InsuranceMOPersonNode,      # 保险市场运营人员
    InsuredPersonNode,          # 被保险人
    
    # 销售渠道节点
    InsuranceSalesChannelNode,  # 保险销售渠道
    
    # 标准节点
    BznStandardIndustryNode,    # BZN标准行业
    BznStandardOccupationNode,  # BZN标准职业
    OccupationNode              # 职业
)

# 基础模型
from .base.base_model import BaseNode, BaseRelationship
from .base import RelationshipType

# 关系模型
# from .relationships import  # 暂时为空

__all__ = [
    # 基础节点
    "OrganizationNode",           # 组织
    "PersonNode",                 # 人员
    "AreaNode",                   # 区域
    "PolicyNode",                 # 保单
    
    # 专业节点
    "InsuranceProductNode",       # 保险产品
    "InsuranceCompanyNode",       # 保险公司
    "InsuranceBrokerageCompanyNode",  # 保险经纪公司
    "PolicyCompanyNode",          # 保单企业
    "InsuranceClaimsNode",        # 保险理赔

    
    # 人员专业节点
    "InsuranceAgentPersonNode",   # 保险代理人
    "InsuranceBDPersonNode",      # 保险商务拓展人员
    "InsuranceMOPersonNode",      # 保险市场运营人员
    "InsuredPersonNode",          # 被保险人
    
    # 销售渠道节点
    "InsuranceSalesChannelNode",  # 保险销售渠道
    
    # 标准节点
    "BznStandardIndustryNode",    # BZN标准行业
    "BznStandardOccupationNode",  # BZN标准职业
    "OccupationNode",             # 职业
    
    # 基础模型
    "BaseNode",
    "BaseRelationship", 
    "RelationshipType"
] 