"""
基础模型定义

定义了所有节点和关系的基础属性和验证规则
"""
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, field_validator, ValidationInfo
from loguru import logger


class RelationshipStatus(str, Enum):
    """关系状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class BaseNode(BaseModel):
    """
    基础节点模型
    
    所有图谱节点都继承此基础类，包含通用属性和方法
    """
    # 核心标识属性
    code: Optional[str] = Field(
        default=None,
        max_length=100,
        description="节点编码/标识符"
    )
    name: Optional[str] = Field(
        default=None,
        max_length=200,
        description="节点名称"
    )
    
    # 时间戳属性
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="节点创建时间戳"
    )
    updated_at: datetime = Field(
        default_factory=datetime.now,
        description="节点最后更新时间戳"
    )
    
    @field_validator('code', 'name')
    @classmethod
    def validate_string_fields(cls, v):
        """验证字符串字段，去除空白字符"""
        if v is not None:
            v = v.strip()
            return v if v else None
        return v
    
    class Config:
        """Pydantic配置"""
        # 允许任意类型，用于支持自定义字段
        arbitrary_types_allowed = True
        # JSON序列化时使用enum值
        use_enum_values = True
        # 日期时间格式
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def get_label(self) -> str:
        """
        获取节点标签（用于Neo4j存储）
        
        默认返回类名，子类可以重写此方法
        
        Returns:
            节点标签
        """
        return self.__class__.__name__
    
    def get_unique_key(self) -> str:
        """
        获取唯一标识键
        
        统一使用节点标签和business_id组成全局唯一键，用于内存去重和跨类型唯一性保证
        
        Returns:
            唯一标识键，格式: "{label}:{business_id}"
        """
        return f"{self.get_label().lower()}:{self.business_id}"
    
    @property
    def node_label(self) -> str:
        """
        Neo4j节点标签属性
        
        与get_label()方法保持一致，提供属性访问方式
        
        Returns:
            节点标签
        """
        return self.get_label()
    
    @property
    def business_id(self) -> str:
        """
        业务唯一标识
        
        默认使用code，子类可以重写此属性
        
        Returns:
            业务标识
        """
        return self.code or str(id(self))
    
    def get_display_name(self) -> str:
        """
        获取显示名称
        
        优先使用name，如果没有则使用code
        
        Returns:
            显示名称
        """
        if self.name:
            return self.name
        elif self.code:
            return self.code
        else:
            return f"{self.get_label()}_{id(self)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            节点属性字典
        """
        return self.dict_for_neo4j()
    
    def dict_for_neo4j(self) -> Dict[str, Any]:
        """
        转换为Neo4j存储格式的字典
        
        Returns:
            适合Neo4j存储的属性字典
        """
        from decimal import Decimal
        from datetime import date
        
        data = self.model_dump()
        
        # 添加统一的code和name属性（如果存在）
        if hasattr(self, 'code'):
            try:
                data['code'] = self.code
            except:
                pass
        
        if hasattr(self, 'name'):
            try:
                data['name'] = self.name
            except:
                pass
        
        # 转换Neo4j不支持的数据类型
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(value, date):
                data[key] = value.strftime("%Y-%m-%d")
            elif isinstance(value, Decimal):
                # Neo4j不支持Decimal，转换为float
                data[key] = float(value)
        return data
    
    @field_validator('updated_at', mode='before')
    @classmethod
    def set_updated_at(cls, v):
        """更新时间自动设置为当前时间"""
        return datetime.now()


class BaseRelationship(BaseModel):
    """
    基础关系模型
    
    所有图谱边关系都继承此基础类，包含通用属性
    """
    relationship_type: str = Field(
        ...,
        description="边关系的具体类型代码"
    )
    relationship_strength: int = Field(
        default=5,
        ge=1, le=10,
        description="关系强度权重（1-10）"
    )
    relationship_status: RelationshipStatus = Field(
        default=RelationshipStatus.ACTIVE,
        description="关系状态"
    )
    effective_date: Optional[datetime] = Field(
        default_factory=datetime.now,
        description="关系生效的日期"
    )
    expiry_date: Optional[datetime] = Field(
        default=None,
        description="关系失效的日期（可为空）"
    )
    data_source_node: Optional[str] = Field(
        default=None,
        description="关系数据的来源节点类型"
    )
    remarks: Optional[str] = Field(
        default=None,
        max_length=500,
        description="关系的补充说明信息"
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="关系创建时间戳"
    )
    updated_at: datetime = Field(
        default_factory=datetime.now,
        description="关系最后更新时间戳"
    )
    
    class Config:
        """Pydantic配置"""
        arbitrary_types_allowed = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")
        }
    
    @field_validator('expiry_date')
    @classmethod
    def validate_expiry_date(cls, v, info: ValidationInfo):
        """验证失效时间必须晚于生效时间"""
        if v and info.data and 'effective_date' in info.data and info.data['effective_date']:
            if v <= info.data['effective_date']:
                raise ValueError("失效时间必须晚于生效时间")
        return v
    
    @field_validator('updated_at', mode='before')
    @classmethod
    def set_updated_at(cls, v):
        """更新时间自动设置为当前时间"""
        return datetime.now()
    
    def get_type(self) -> str:
        """
        获取关系类型
        
        默认返回类名，子类可以重写此方法
        
        Returns:
            关系类型
        """
        return self.__class__.__name__
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            关系属性字典
        """
        return self.dict_for_neo4j()
    
    def dict_for_neo4j(self) -> Dict[str, Any]:
        """
        转换为Neo4j存储格式的字典
        
        Returns:
            适合Neo4j存储的属性字典
        """
        from decimal import Decimal
        from datetime import date
        
        data = self.model_dump(exclude_none=True)
        # 转换Neo4j不支持的数据类型
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(value, date):
                data[key] = value.strftime("%Y-%m-%d")
            elif isinstance(value, Decimal):
                # Neo4j不支持Decimal，转换为float
                data[key] = float(value)
        return data
    
    def is_active(self) -> bool:
        """
        检查关系是否处于有效状态
        
        Returns:
            关系是否有效
        """
        if self.relationship_status != RelationshipStatus.ACTIVE:
            return False
        
        now = datetime.now()
        
        # 检查生效时间
        if self.effective_date and now < self.effective_date:
            return False
        
        # 检查失效时间
        if self.expiry_date and now > self.expiry_date:
            return False
        
        return True


class EncryptedField(BaseModel):
    """
    加密字段包装器
    
    用于标识需要加密处理的敏感字段
    """
    encrypted_value: str = Field(..., description="加密后的值")
    encryption_algorithm: str = Field(default="AES-256", description="加密算法")
    
    class Config:
        arbitrary_types_allowed = True 