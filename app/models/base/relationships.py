"""
关系类型定义

定义所有边关系的类型枚举，基于图谱关系规范文档
"""
from enum import Enum


class RelationshipType(str, Enum):
    """图谱边关系类型枚举 - 仅包含实际使用的关系类型"""
    
    # 地理位置关系 (Geographic Relationships)
    PARENT_OF = "PARENT_OF"  # 父级关系（地区层级、销售渠道层级、行业层级）
    OCCURRED_IN = "OCCURRED_IN"  # 理赔发生地区
    LINKS_TO_REGION = "LINKS_TO_REGION"  # 销售渠道关联地区
    LOCATED_IN = "LOCATED_IN"  # 组织所在地区
    
    # 联系方式关系 (Contact Relationships)
    HAS_EMAIL = "HAS_EMAIL"  # 拥有电子邮箱
    HAS_PHONE = "HAS_PHONE"  # 拥有联系电话
    HAS_ADDITIONAL_EMAIL = "HAS_ADDITIONAL_EMAIL"  # 拥有附加电子邮箱
    HAS_ADDITIONAL_PHONE = "HAS_ADDITIONAL_PHONE"  # 拥有附加联系电话
    
    # 行业关系 (Industry Relationships)
    BELONGS_TO_INDUSTRY = "BELONGS_TO_INDUSTRY"  # 属于行业
    
    # 产品关系 (Product Relationships)
    ISSUED_BY = "ISSUED_BY"  # 产品由公司发行/理赔由公司处理
    COVERS_PRODUCT = "COVERS_PRODUCT"  # 保单承保产品
    
    # 保单业务关系 (Policy Business Relationships)
    COVERS = "COVERS"  # 保单承保关系
    COVERS_INSURED = "COVERS_INSURED"  # 保单承保被保险人
    UNDERWRITTEN_BY = "UNDERWRITTEN_BY"  # 保单由保险公司承保
    INSURED_BY = "INSURED_BY"  # 投保关系
    SOLD_BY = "SOLD_BY"  # 保单销售人员
    DISTRIBUTED_BY = "DISTRIBUTED_BY"  # 保单销售渠道
    
    # 理赔关系 (Claims Relationships)
    INVOLVES_INSURED_PERSON = "INVOLVES_INSURED_PERSON"  # 理赔涉及被保险人
    BELONGS_TO_POLICY = "BELONGS_TO_POLICY"  # 理赔属于保单
    
    # 销售渠道关系 (Sales Channel Relationships)
    MANAGED_BY = "MANAGED_BY"  # 渠道由代理人管理
    SUPERVISED_BY = "SUPERVISED_BY"  # 渠道由BD/MO监督
    BELONGS_TO_CHANNEL = "BELONGS_TO_CHANNEL"  # 代理人员所属销售渠道
    BELONGS_TO_TEAM = "BELONGS_TO_TEAM"  # 代理人员所属团队
    
    # 特殊名单关系 (Special List Relationships)
    BLACKLISTED = "BLACKLISTED"  # 黑名单关系


class RelationshipCategory(str, Enum):
    """关系分类枚举"""
    GEOGRAPHIC = "geographic"  # 地理位置关系
    ORGANIZATIONAL = "organizational"  # 组织机构关系
    POLICY_BUSINESS = "policy_business"  # 保单业务关系
    CLAIMS_PROCESS = "claims_process"  # 理赔流程关系
    CERTIFICATE_DOCUMENT = "certificate_document"  # 证明文件关系
    PERSONAL_INFO = "personal_info"  # 个人信息关系
    FINANCIAL_TRANSACTION = "financial_transaction"  # 金融交易关系 