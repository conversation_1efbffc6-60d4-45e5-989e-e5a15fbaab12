"""
业务关系管理控制器

提供保单、产品、人员等业务关系的标准化REST API接口
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, Dict, Any, List
from enum import Enum

from app.services.relationship_service.policy_product_relationship_service import PolicyProductRelationshipService
from app.services.relationship_service.insured_person_policy_relationship_service import InsuredPersonPolicyRelationshipService
from app.services.relationship_service.policy_sale_person_relationship_service import PolicySalePersonRelationshipService
from app.utils.logger import get_logger
from app.utils.controller_base import ApiResponse, ServiceMapper, api_handler

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/relationships/business", tags=["业务关系管理"])


class BusinessRelationType(str, Enum):
    """业务关系类型枚举"""
    POLICY_PRODUCT = "policy_product"              # 保单-产品关系
    INSURED_POLICY = "insured_policy"              # 被保险人-保单关系
    POLICY_SALESPERSON = "policy_salesperson"      # 保单-销售人员关系


# 服务实例映射 - 统一命名
SERVICE_MAP = {
    BusinessRelationType.POLICY_PRODUCT: PolicyProductRelationshipService(),
    BusinessRelationType.INSURED_POLICY: InsuredPersonPolicyRelationshipService(),
    BusinessRelationType.POLICY_SALESPERSON: PolicySalePersonRelationshipService(),
}


@router.get("/", summary="搜索业务关系")
async def search_business_relationships(
    relation_type: BusinessRelationType = Query(..., description="关系类型"),
    policy_code: Optional[str] = Query(None, description="保单编码关键词"),
    product_code: Optional[str] = Query(None, description="产品编码关键词"),
    person_id: Optional[str] = Query(None, description="人员ID关键词"),
    include_node_details: bool = Query(True, description="是否包含节点详情"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """
    搜索业务关系
    
    Args:
        relation_type: 关系类型
        policy_code: 保单编码关键词
        product_code: 产品编码关键词
        person_id: 人员ID关键词
        include_node_details: 是否包含节点详情
        limit: 返回记录数限制
        
    Returns:
        Dict[str, Any]: 包含关系列表和元数据的响应
    """
    try:
        logger.info(f"搜索业务关系: type={relation_type}, policy={policy_code}")
        
        service = SERVICE_MAP.get(relation_type)
        if not service:
            raise HTTPException(status_code=400, detail=f"不支持的关系类型: {relation_type}")
        
        # 根据关系类型调用相应的搜索方法
        if relation_type == BusinessRelationType.POLICY_PRODUCT:
            relationships = service.search_product_relationships(
                policy_code=policy_code,
                product_code=product_code,
                include_node_details=include_node_details,
                limit=limit
            )
        elif relation_type == BusinessRelationType.INSURED_POLICY:
            relationships = service.search_insured_policy_relationships(
                policy_code=policy_code,
                person_id=person_id,
                include_node_details=include_node_details,
                limit=limit
            )
        elif relation_type == BusinessRelationType.POLICY_SALESPERSON:
            relationships = service.search_sale_person_relationships(
                policy_code=policy_code,
                person_id=person_id,
                include_node_details=include_node_details,
                limit=limit
            )
        else:
            relationships = []
        
        return {
            "success": True,
            "data": relationships,
            "total": len(relationships),
            "relation_type": relation_type,
            "message": "搜索完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索业务关系失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/statistics", summary="获取业务关系统计信息")
async def get_business_statistics(
    relation_type: Optional[BusinessRelationType] = Query(None, description="关系类型")
) -> Dict[str, Any]:
    """
    获取业务关系统计信息
    
    Args:
        relation_type: 关系类型，不指定则返回所有类型的统计
        
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        if relation_type:
            # 获取单个类型的统计
            service = SERVICE_MAP.get(relation_type)
            if not service:
                raise HTTPException(status_code=400, detail=f"不支持的关系类型: {relation_type}")
            
            stats = service.get_relationship_statistics()
            return {
                "success": True,
                "data": {relation_type: stats},
                "message": "统计信息获取成功"
            }
        else:
            # 获取所有类型的统计
            all_stats = {}
            for rel_type, service in SERVICE_MAP.items():
                try:
                    stats = service.get_relationship_statistics()
                    all_stats[rel_type] = stats
                except Exception as e:
                    logger.warning(f"获取{rel_type}统计失败: {str(e)}")
                    all_stats[rel_type] = {"error": str(e)}
            
            return {
                "success": True,
                "data": all_stats,
                "message": "统计信息获取成功"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取业务关系统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/import", summary="导入业务关系数据")
async def import_business_relationships(
    relation_type: BusinessRelationType = Query(..., description="关系类型"),
    clear_existing: bool = Query(False, description="是否清空现有数据"),
    limit: Optional[int] = Query(None, description="导入记录数限制"),
    where_conditions: Optional[str] = Query(None, description="额外的WHERE条件"),
    custom_day: Optional[str] = Query(None, description="自定义日期(YYYY-MM-DD)")
) -> Dict[str, Any]:
    """
    从数据源导入业务关系数据
    
    Args:
        relation_type: 关系类型
        clear_existing: 是否清空现有数据
        limit: 导入记录数限制
        where_conditions: 额外的WHERE条件
        custom_day: 自定义日期
        
    Returns:
        Dict[str, Any]: 导入结果
    """
    try:
        logger.info(f"开始导入业务关系数据: {relation_type}")
        
        service = SERVICE_MAP.get(relation_type)
        if not service:
            raise HTTPException(status_code=400, detail=f"不支持的关系类型: {relation_type}")
        
        # 如果需要清空现有数据
        if clear_existing:
            success = service.delete_all_relationships()
            if not success:
                raise HTTPException(status_code=500, detail="清空现有数据失败")
        
        # 执行数据导入
        success = service.create_relationships(
            limit=limit,
            where_conditions=where_conditions,
            custom_day=custom_day
        )
        
        if success:
            # 获取导入后的统计信息
            stats = service.get_relationship_statistics()
            logger.info(f"业务关系数据导入完成: {relation_type}")
            return {
                "success": True,
                "data": {
                    "relation_type": relation_type,
                    "statistics": stats
                },
                "message": f"{relation_type}关系数据导入成功"
            }
        else:
            raise HTTPException(status_code=500, detail="数据导入失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入业务关系数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.delete("/", summary="删除业务关系数据")
async def delete_business_relationships(
    relation_type: BusinessRelationType = Query(..., description="关系类型")
) -> Dict[str, Any]:
    """
    删除指定类型的业务关系数据
    
    Args:
        relation_type: 关系类型
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        logger.info(f"开始删除业务关系数据: {relation_type}")
        
        service = SERVICE_MAP.get(relation_type)
        if not service:
            raise HTTPException(status_code=400, detail=f"不支持的关系类型: {relation_type}")
        
        success = service.delete_all_relationships()
        
        if success:
            logger.info(f"业务关系数据删除完成: {relation_type}")
            return {
                "success": True,
                "message": f"{relation_type}关系数据已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除业务关系数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """
    业务关系服务健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        # 检查所有服务的健康状态
        service_status = {}
        all_healthy = True
        
        for rel_type, service in SERVICE_MAP.items():
            try:
                stats = service.get_relationship_statistics()
                total_count = stats.get("total_relationships", 0)
                service_status[rel_type] = {
                    "status": "healthy",
                    "total_relationships": total_count
                }
            except Exception as e:
                logger.warning(f"{rel_type}服务健康检查失败: {str(e)}")
                service_status[rel_type] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                all_healthy = False
        
        return {
            "success": True,
            "service": "BusinessRelationshipService",
            "status": "healthy" if all_healthy else "partially_healthy",
            "services": service_status,
            "message": "服务状态检查完成"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "success": False,
            "service": "BusinessRelationshipService", 
            "status": "unhealthy",
            "error": str(e),
            "message": "服务异常"
        } 