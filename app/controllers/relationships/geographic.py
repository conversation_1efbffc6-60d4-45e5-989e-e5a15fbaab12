"""
地理位置关系管理控制器

提供地理位置相关关系的统一管理API接口
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, Dict, Any, List
from enum import Enum

from app.services.relationship_service.area_hierarchy_relationship_service import AreaHierarchyRelationshipService
from app.services.relationship_service.insurance_brokerage_company_area_relationship_service import InsuranceBrokerageCompanyAreaRelationshipService
from app.services.relationship_service.insurance_claims_area_relationship_service import InsuranceClaimsAreaRelationshipService
from app.services.relationship_service.policy_company_area_relationship_service import PolicyCompanyAreaRelationshipService
from app.services.relationship_service.insurance_sales_channel_area_relationship_service import InsuranceSalesChannelAreaRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/relationships/geographic", tags=["地理位置关系管理"])


class GeographicRelationType(str, Enum):
    """地理位置关系类型枚举"""
    AREA_HIERARCHY = "area-hierarchy"                           # 区域层级关系
    BROKERAGE_COMPANY_AREA = "brokerage-company-area"          # 经纪公司-区域关系
    CLAIMS_AREA = "claims-area"                                # 理赔-区域关系
    POLICY_COMPANY_AREA = "policy-company-area"                # 保单公司-区域关系
    SALES_CHANNEL_AREA = "sales-channel-area"                  # 销售渠道-区域关系


# 服务实例映射
SERVICE_MAP = {
    GeographicRelationType.AREA_HIERARCHY: AreaHierarchyRelationshipService,
    GeographicRelationType.BROKERAGE_COMPANY_AREA: InsuranceBrokerageCompanyAreaRelationshipService,
    GeographicRelationType.CLAIMS_AREA: InsuranceClaimsAreaRelationshipService,
    GeographicRelationType.POLICY_COMPANY_AREA: PolicyCompanyAreaRelationshipService,
    GeographicRelationType.SALES_CHANNEL_AREA: InsuranceSalesChannelAreaRelationshipService
}


@router.get("/{relation_type}/", summary="搜索地理位置关系")
async def search_geographic_relationships(
    relation_type: GeographicRelationType = Path(..., description="地理位置关系类型"),
    source_code: Optional[str] = Query(None, description="源节点编码"),
    target_code: Optional[str] = Query(None, description="目标节点编码"),
    area_name: Optional[str] = Query(None, description="区域名称关键词"),
    province: Optional[str] = Query(None, description="省份"),
    city: Optional[str] = Query(None, description="城市"),
    include_details: bool = Query(True, description="是否包含节点详细信息"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """
    搜索指定类型的地理位置关系
    
    Args:
        relation_type: 地理位置关系类型
        source_code: 源节点编码
        target_code: 目标节点编码
        area_name: 区域名称关键词
        province: 省份
        city: 城市
        include_details: 是否包含节点详细信息
        limit: 返回记录数限制
        
    Returns:
        Dict[str, Any]: 包含关系列表和元数据的响应
    """
    try:
        service_class = SERVICE_MAP.get(relation_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的地理位置关系类型: {relation_type}")
        
        service = service_class()
        
        # 根据不同的关系类型调用对应的搜索方法
        if relation_type == GeographicRelationType.AREA_HIERARCHY:
            relationships = service.search_hierarchy_relationships(
                parent_code=source_code,
                child_code=target_code,
                include_node_details=include_details,
                limit=limit
            )
        elif relation_type == GeographicRelationType.BROKERAGE_COMPANY_AREA:
            relationships = service.search_location_relationships(
                company_name=None,
                area_name=area_name,
                province=province,
                city=city,
                include_node_details=include_details,
                limit=limit
            )
        elif relation_type == GeographicRelationType.CLAIMS_AREA:
            relationships = service.search_claims_area_relationships(
                claims_code=source_code,
                area_code=target_code,
                area_name=area_name,
                province=province,
                city=city,
                include_node_details=include_details,
                limit=limit
            )
        elif relation_type == GeographicRelationType.POLICY_COMPANY_AREA:
            relationships = service.search_location_relationships(
                company_name=None,
                area_name=area_name,
                province=province,
                city=city,
                include_node_details=include_details,
                limit=limit
            )
        elif relation_type == GeographicRelationType.SALES_CHANNEL_AREA:
            relationships = service.search_geographic_relationships(
                channel_code=source_code,
                area_code=target_code,
                area_name=area_name,
                include_node_details=include_details,
                limit=limit
            )
        
        return {
            "success": True,
            "data": relationships,
            "total": len(relationships),
            "relation_type": relation_type,
            "message": "搜索完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索{relation_type}关系失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/{relation_type}/statistics", summary="获取地理位置关系统计信息")
async def get_geographic_relationship_statistics(
    relation_type: GeographicRelationType = Path(..., description="地理位置关系类型")
) -> Dict[str, Any]:
    """
    获取指定类型地理位置关系的统计信息
    
    Args:
        relation_type: 地理位置关系类型
        
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        service_class = SERVICE_MAP.get(relation_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的地理位置关系类型: {relation_type}")
        
        service = service_class()
        stats = service.get_relationship_statistics()
        
        return {
            "success": True,
            "data": stats,
            "relation_type": relation_type,
            "message": "统计信息获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{relation_type}关系统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/{relation_type}/extract", summary="从数据源提取地理位置关系")
async def extract_geographic_relationships(
    relation_type: GeographicRelationType = Path(..., description="地理位置关系类型"),
    clear_existing: bool = Query(False, description="是否清空现有关系"),
    limit: Optional[int] = Query(None, description="提取记录数限制")
) -> Dict[str, Any]:
    """
    从数据源提取指定类型的地理位置关系
    
    Args:
        relation_type: 地理位置关系类型
        clear_existing: 是否清空现有关系
        limit: 提取记录数限制
        
    Returns:
        Dict[str, Any]: 提取结果
    """
    try:
        service_class = SERVICE_MAP.get(relation_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的地理位置关系类型: {relation_type}")
        
        service = service_class()
        
        # 如果需要清空现有关系
        if clear_existing:
            success = service.delete_all_relationships()
            if not success:
                raise HTTPException(status_code=500, detail="清空现有关系失败")
        
        # 执行关系提取
        success = service.create_relationships(limit=limit)
        
        if success:
            # 获取最新统计信息
            stats = service.get_relationship_statistics()
            return {
                "success": True,
                "data": {
                    "extraction_params": {
                        "relation_type": relation_type,
                        "clear_existing": clear_existing,
                        "limit": limit
                    },
                    "statistics": stats
                },
                "message": f"{relation_type}关系提取成功"
            }
        else:
            raise HTTPException(status_code=500, detail="关系提取失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取{relation_type}关系失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提取失败: {str(e)}")


@router.delete("/{relation_type}/", summary="删除所有指定类型的地理位置关系")
async def delete_all_geographic_relationships(
    relation_type: GeographicRelationType = Path(..., description="地理位置关系类型")
) -> Dict[str, Any]:
    """
    删除所有指定类型的地理位置关系
    
    Args:
        relation_type: 地理位置关系类型
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        service_class = SERVICE_MAP.get(relation_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的地理位置关系类型: {relation_type}")
        
        service = service_class()
        success = service.delete_all_relationships()
        
        if success:
            return {
                "success": True,
                "relation_type": relation_type,
                "message": f"所有{relation_type}关系已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除{relation_type}关系失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/", summary="获取所有地理位置关系类型的统计概览")
async def get_all_geographic_relationships_overview() -> Dict[str, Any]:
    """
    获取所有地理位置关系类型的统计概览
    
    Returns:
        Dict[str, Any]: 所有地理位置关系类型的统计信息
    """
    try:
        overview = {}
        
        for relation_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_relationship_statistics()
                overview[relation_type] = stats
            except Exception as e:
                logger.warning(f"获取{relation_type}统计失败: {str(e)}")
                overview[relation_type] = {"error": str(e)}
        
        return {
            "success": True,
            "data": overview,
            "message": "地理位置关系统计概览获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取地理位置关系统计概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取概览失败: {str(e)}")


@router.get("/health", summary="地理位置关系服务健康检查")
async def health_check() -> Dict[str, Any]:
    """
    地理位置关系服务健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        health_status = {}
        overall_healthy = True
        
        for relation_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_relationship_statistics()
                health_status[relation_type] = {
                    "status": "healthy",
                    "total_count": stats.get("total_count", 0)
                }
            except Exception as e:
                health_status[relation_type] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "success": overall_healthy,
            "service": "GeographicRelationshipService",
            "status": "healthy" if overall_healthy else "partially_healthy",
            "details": health_status,
            "message": "地理位置关系服务健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"地理位置关系服务健康检查失败: {str(e)}")
        return {
            "success": False,
            "service": "GeographicRelationshipService",
            "status": "unhealthy",
            "error": str(e),
            "message": "地理位置关系服务异常"
        } 