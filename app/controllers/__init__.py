"""
重构后的控制器模块

采用最佳实践的API架构设计：
- 统一使用FastAPI框架
- 标准化的API路径结构 (/api/v1/...)
- 按功能域分组的控制器
- 统一的响应格式和错误处理
"""

from fastapi import APIRouter

# 导入节点控制器
from .nodes import (
    area_router,
    person_router,
    organization_router,
)
from .nodes.policy import router as policy_router
from .nodes.product import router as product_router

# 导入关系控制器
from .relationships import (
    geographic_router,
)
from .relationships.business import router as business_router

# 导入系统控制器
from .system import (
    health_router,
)

# 创建主路由器
main_router = APIRouter()

# 注册节点管理路由
main_router.include_router(area_router)
main_router.include_router(person_router)
main_router.include_router(organization_router)
main_router.include_router(policy_router)
main_router.include_router(product_router)

# 注册关系管理路由
main_router.include_router(geographic_router)
main_router.include_router(business_router)

# 注册系统管理路由
main_router.include_router(health_router)

__all__ = [
    'main_router',
    # 节点控制器
    'area_router',
    'person_router',
    'organization_router',
    'policy_router',
    'product_router',
    # 关系控制器
    'geographic_router',
    'business_router',
    # 系统控制器
    'health_router'
] 