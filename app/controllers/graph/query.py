"""
Neo4j Browser风格的图谱查询API

完全模拟Neo4j Desktop Browser的功能：
- 直接Cypher查询执行
- 自动节点标签提取
- 多种可视化视图（Graph、Table、Text、Code）
- 查询历史管理
- 数据库统计信息
- 实时查询执行
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from app.services.data_storage.neo4j_simple_client import neo4j_simple_client
from app.utils.logger import get_logger
from app.utils.serializers import Neo4jSerializer

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/neo4j", tags=["Neo4j Browser风格查询"])

# ================================
# Neo4j Browser 数据模型
# ================================

class ViewMode(str, Enum):
    """视图模式 - 完全对应Neo4j Browser"""
    GRAPH = "graph"       # 图形视图
    TABLE = "table"       # 表格视图  
    TEXT = "text"         # 文本视图
    CODE = "code"         # 代码视图

class QueryStatus(str, Enum):
    """查询状态"""
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Neo4jNode(BaseModel):
    """Neo4j节点 - 完全对应Browser中的节点显示"""
    identity: int = Field(..., description="节点内部ID")
    labels: List[str] = Field(default_factory=list, description="节点标签")
    properties: Dict[str, Any] = Field(default_factory=dict, description="节点属性")
    elementId: Optional[str] = Field(None, description="元素ID")

class Neo4jRelationship(BaseModel):
    """Neo4j关系 - 完全对应Browser中的关系显示"""
    identity: int = Field(..., description="关系内部ID")
    start: int = Field(..., description="起始节点ID")
    end: int = Field(..., description="结束节点ID")
    type: str = Field(..., description="关系类型")
    properties: Dict[str, Any] = Field(default_factory=dict, description="关系属性")
    elementId: Optional[str] = Field(None, description="元素ID")

class Neo4jPath(BaseModel):
    """Neo4j路径"""
    start: Neo4jNode = Field(..., description="起始节点")
    end: Neo4jNode = Field(..., description="结束节点")
    segments: List[Dict[str, Any]] = Field(..., description="路径段")
    length: int = Field(..., description="路径长度")

class QueryResult(BaseModel):
    """查询结果 - 完全对应Browser的结果显示"""
    keys: List[str] = Field(..., description="结果列名")
    records: List[Dict[str, Any]] = Field(..., description="结果记录")
    summary: Dict[str, Any] = Field(..., description="查询摘要")
    
class QueryStats(BaseModel):
    """查询统计 - 对应Browser的stats面板"""
    nodes_created: int = Field(default=0)
    nodes_deleted: int = Field(default=0)
    relationships_created: int = Field(default=0)
    relationships_deleted: int = Field(default=0)
    properties_set: int = Field(default=0)
    labels_added: int = Field(default=0)
    labels_removed: int = Field(default=0)
    indexes_added: int = Field(default=0)
    indexes_removed: int = Field(default=0)
    constraints_added: int = Field(default=0)
    constraints_removed: int = Field(default=0)
    total_time: float = Field(..., description="总执行时间(毫秒)")
    
class QueryExecution(BaseModel):
    """查询执行结果 - 完全对应Browser的执行面板"""
    query: str = Field(..., description="执行的查询")
    status: QueryStatus = Field(..., description="执行状态")
    result: Optional[QueryResult] = Field(None, description="查询结果")
    stats: Optional[QueryStats] = Field(None, description="执行统计")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间")
    result_count: int = Field(default=0, description="结果数量")

# ================================
# 请求模型
# ================================

class CypherQueryRequest(BaseModel):
    """Cypher查询请求 - 对应Browser的查询输入"""
    query: str = Field(..., description="Cypher查询语句")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="查询参数")
    view_mode: ViewMode = Field(default=ViewMode.GRAPH, description="视图模式")
    include_stats: bool = Field(default=True, description="是否包含统计信息")
    timeout: Optional[int] = Field(default=30, description="查询超时时间(秒)")

class DatabaseInfoRequest(BaseModel):
    """数据库信息请求"""
    include_schema: bool = Field(default=True, description="是否包含模式信息")
    include_constraints: bool = Field(default=True, description="是否包含约束信息")
    include_indexes: bool = Field(default=True, description="是否包含索引信息")

# ================================
# Neo4j Browser风格的核心服务
# ================================

class Neo4jBrowserService:
    """Neo4j Browser风格的查询服务"""
    
    def __init__(self):
        self.serializer = Neo4jSerializer()
        self.query_history = []
    
    def extract_neo4j_node(self, node_obj: Any) -> Neo4jNode:
        """提取Neo4j节点 - 完全对应Browser的节点处理"""
        if not node_obj:
            return None
            
        try:
            # 提取节点标签 - 直接从Neo4j对象获取
            labels = []
            if hasattr(node_obj, 'labels'):
                labels = list(node_obj.labels)
            elif hasattr(node_obj, '_labels'):
                labels = list(node_obj._labels)
            
            # 提取节点属性
            properties = {}
            if hasattr(node_obj, '_properties'):
                properties = dict(node_obj._properties)
            elif isinstance(node_obj, dict):
                properties = node_obj.copy()
            
            # 序列化属性
            properties = self.serializer.serialize(properties)
            
            # 提取节点ID
            identity = 0
            element_id = None
            if hasattr(node_obj, 'id'):
                identity = node_obj.id
            if hasattr(node_obj, 'element_id'):
                element_id = node_obj.element_id
            elif hasattr(node_obj, 'elementId'):
                element_id = node_obj.elementId
                
            return Neo4jNode(
                identity=identity,
                labels=labels,
                properties=properties,
                elementId=element_id
            )
            
        except Exception as e:
            logger.warning(f"提取节点信息失败: {str(e)}")
            return None
    
    def extract_neo4j_relationship(self, rel_obj: Any) -> Neo4jRelationship:
        """提取Neo4j关系 - 完全对应Browser的关系处理"""
        if not rel_obj:
            return None
            
        try:
            # 提取关系类型
            rel_type = "UNKNOWN"
            if hasattr(rel_obj, 'type'):
                rel_type = rel_obj.type
            elif isinstance(rel_obj, dict) and 'type' in rel_obj:
                rel_type = rel_obj['type']
            
            # 提取关系属性
            properties = {}
            if hasattr(rel_obj, '_properties'):
                properties = dict(rel_obj._properties)
            elif isinstance(rel_obj, dict):
                properties = {k: v for k, v in rel_obj.items() if k != 'type'}
            
            # 序列化属性
            properties = self.serializer.serialize(properties)
            
            # 提取关系ID和节点引用
            identity = 0
            start_id = 0
            end_id = 0
            element_id = None
            
            if hasattr(rel_obj, 'id'):
                identity = rel_obj.id
            if hasattr(rel_obj, 'start_node'):
                if hasattr(rel_obj.start_node, 'id'):
                    start_id = rel_obj.start_node.id
            if hasattr(rel_obj, 'end_node'):
                if hasattr(rel_obj.end_node, 'id'):
                    end_id = rel_obj.end_node.id
            if hasattr(rel_obj, 'element_id'):
                element_id = rel_obj.element_id
            elif hasattr(rel_obj, 'elementId'):
                element_id = rel_obj.elementId
                
            return Neo4jRelationship(
                identity=identity,
                start=start_id,
                end=end_id,
                type=rel_type,
                properties=properties,
                elementId=element_id
            )
            
        except Exception as e:
            logger.warning(f"提取关系信息失败: {str(e)}")
            return None
    
    def process_query_result(self, raw_results: List[Dict[str, Any]], query: str) -> QueryResult:
        """处理查询结果 - 完全对应Browser的结果处理"""
        if not raw_results:
            return QueryResult(
                keys=[],
                records=[],
                summary={"result_count": 0}
            )
        
        # 提取列名
        keys = list(raw_results[0].keys()) if raw_results else []
        
        # 处理每条记录
        processed_records = []
        for record in raw_results:
            processed_record = {}
            
            for key, value in record.items():
                # 根据数据类型进行智能处理
                if self._is_neo4j_node(value):
                    # 节点对象
                    processed_record[key] = self.extract_neo4j_node(value)
                elif self._is_neo4j_relationship(value):
                    # 关系对象
                    processed_record[key] = self.extract_neo4j_relationship(value)
                elif self._is_neo4j_path(value):
                    # 路径对象
                    processed_record[key] = self._extract_neo4j_path(value)
                else:
                    # 普通值
                    processed_record[key] = self.serializer.serialize(value)
            
            processed_records.append(processed_record)
        
        # 创建结果摘要
        summary = {
            "result_count": len(processed_records),
            "keys": keys,
            "query": query
        }
        
        return QueryResult(
            keys=keys,
            records=processed_records,
            summary=summary
        )
    
    def _is_neo4j_node(self, value: Any) -> bool:
        """判断是否为Neo4j节点"""
        return (hasattr(value, 'labels') or 
                hasattr(value, '_labels') or
                (isinstance(value, dict) and 
                 any(key in value for key in ['business_id', 'name', 'code', 'created_at'])))
    
    def _is_neo4j_relationship(self, value: Any) -> bool:
        """判断是否为Neo4j关系"""
        return (hasattr(value, 'type') and 
                (hasattr(value, 'start_node') or hasattr(value, 'end_node')))
    
    def _is_neo4j_path(self, value: Any) -> bool:
        """判断是否为Neo4j路径"""
        return hasattr(value, 'start') and hasattr(value, 'end') and hasattr(value, 'relationships')
    
    def _extract_neo4j_path(self, path_obj: Any) -> Neo4jPath:
        """提取Neo4j路径"""
        try:
            start_node = self.extract_neo4j_node(path_obj.start)
            end_node = self.extract_neo4j_node(path_obj.end)
            
            segments = []
            if hasattr(path_obj, 'relationships'):
                for rel in path_obj.relationships:
                    segments.append(self.extract_neo4j_relationship(rel))
            
            return Neo4jPath(
                start=start_node,
                end=end_node,
                segments=segments,
                length=len(segments)
            )
        except Exception as e:
            logger.warning(f"提取路径信息失败: {str(e)}")
            return None
    
    def execute_cypher(self, request: CypherQueryRequest) -> QueryExecution:
        """执行Cypher查询 - 完全对应Browser的查询执行"""
        start_time = datetime.now()
        
        try:
            # 执行查询
            results = neo4j_simple_client.execute_query(request.query, request.parameters)
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # 处理结果
            query_result = self.process_query_result(results, request.query)
            
            # 创建统计信息（模拟Browser的stats）
            stats = QueryStats(
                total_time=execution_time,
                # TODO: 可以从Neo4j的查询统计中获取更详细的信息
            ) if request.include_stats else None
            
            # 添加到查询历史
            execution = QueryExecution(
                query=request.query,
                status=QueryStatus.COMPLETED,
                result=query_result,
                stats=stats,
                execution_time=execution_time,
                result_count=len(query_result.records)
            )
            
            self.query_history.append(execution)
            return execution
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            error_execution = QueryExecution(
                query=request.query,
                status=QueryStatus.FAILED,
                error=str(e),
                execution_time=execution_time
            )
            
            self.query_history.append(error_execution)
            return error_execution

# 实例化服务
browser_service = Neo4jBrowserService()

# ================================
# Neo4j Browser风格的API接口
# ================================

@router.post("/query", summary="执行Cypher查询", response_model=QueryExecution)
async def execute_cypher_query(request: CypherQueryRequest) -> QueryExecution:
    """
    执行Cypher查询 - 完全对应Neo4j Browser的查询功能
    
    支持：
    - 任意Cypher查询
    - 自动节点标签提取
    - 多种视图模式
    - 执行统计信息
    - 查询历史记录
    """
    return browser_service.execute_cypher(request)

@router.get("/schema", summary="获取数据库模式信息")
async def get_database_schema() -> Dict[str, Any]:
    """获取数据库模式信息 - 对应Browser的Schema面板"""
    try:
        # 获取所有标签
        labels_query = "CALL db.labels() YIELD label RETURN label ORDER BY label"
        labels_result = neo4j_simple_client.execute_query(labels_query)
        labels = [record['label'] for record in labels_result]
        
        # 获取所有关系类型
        rel_types_query = "CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType"
        rel_types_result = neo4j_simple_client.execute_query(rel_types_query)
        relationship_types = [record['relationshipType'] for record in rel_types_result]
        
        # 获取属性键
        prop_keys_query = "CALL db.propertyKeys() YIELD propertyKey RETURN propertyKey ORDER BY propertyKey"
        prop_keys_result = neo4j_simple_client.execute_query(prop_keys_query)
        property_keys = [record['propertyKey'] for record in prop_keys_result]
        
        # 获取索引信息
        indexes_query = "SHOW INDEXES YIELD name, type, entityType, labelsOrTypes, properties RETURN name, type, entityType, labelsOrTypes, properties"
        try:
            indexes_result = neo4j_simple_client.execute_query(indexes_query)
            indexes = indexes_result
        except:
            indexes = []
        
        # 获取约束信息
        constraints_query = "SHOW CONSTRAINTS YIELD name, type, entityType, labelsOrTypes, properties RETURN name, type, entityType, labelsOrTypes, properties"
        try:
            constraints_result = neo4j_simple_client.execute_query(constraints_query)
            constraints = constraints_result
        except:
            constraints = []
        
        return {
            "labels": labels,
            "relationship_types": relationship_types,
            "property_keys": property_keys,
            "indexes": indexes,
            "constraints": constraints
        }
        
    except Exception as e:
        logger.error(f"获取数据库模式失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics", summary="获取数据库统计信息")
async def get_database_statistics() -> Dict[str, Any]:
    """获取数据库统计信息 - 对应Browser的Database Information"""
    try:
        stats = {}
        
        # 节点统计
        nodes_query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
        nodes_result = neo4j_simple_client.execute_query(nodes_query)
        
        node_stats = {}
        total_nodes = 0
        for record in nodes_result:
            labels = record['labels']
            count = record['count']
            total_nodes += count
            
            if labels:
                label_key = ':'.join(sorted(labels))
                node_stats[label_key] = count
        
        # 关系统计
        rels_query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
        rels_result = neo4j_simple_client.execute_query(rels_query)
        
        relationship_stats = {}
        total_relationships = 0
        for record in rels_result:
            rel_type = record['type']
            count = record['count']
            total_relationships += count
            relationship_stats[rel_type] = count
        
        return {
            "total_nodes": total_nodes,
            "total_relationships": total_relationships,
            "nodes_by_label": node_stats,
            "relationships_by_type": relationship_stats
        }
        
    except Exception as e:
        logger.error(f"获取数据库统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", summary="获取查询历史")
async def get_query_history(limit: int = Query(default=50, le=100)) -> List[QueryExecution]:
    """获取查询历史 - 对应Browser的History面板"""
    return browser_service.query_history[-limit:]

@router.delete("/history", summary="清空查询历史")
async def clear_query_history() -> Dict[str, str]:
    """清空查询历史"""
    browser_service.query_history.clear()
    return {"message": "查询历史已清空"}

@router.get("/version", summary="获取Neo4j版本信息")
async def get_neo4j_version() -> Dict[str, Any]:
    """获取Neo4j版本信息 - 对应Browser的系统信息"""
    try:
        version_query = "CALL dbms.components() YIELD name, versions, edition RETURN name, versions, edition"
        result = neo4j_simple_client.execute_query(version_query)
        
        return {
            "components": result
        }
        
    except Exception as e:
        logger.error(f"获取版本信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/explain", summary="解释查询执行计划")
async def explain_query(query: str = Body(...)) -> Dict[str, Any]:
    """解释查询执行计划 - 对应Browser的Explain功能"""
    try:
        explain_query = f"EXPLAIN {query}"
        result = neo4j_simple_client.execute_query(explain_query)
        
        return {
            "query": query,
            "plan": result
        }
        
    except Exception as e:
        logger.error(f"解释查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/profile", summary="分析查询性能")
async def profile_query(query: str = Body(...)) -> Dict[str, Any]:
    """分析查询性能 - 对应Browser的Profile功能"""
    try:
        profile_query = f"PROFILE {query}"
        result = neo4j_simple_client.execute_query(profile_query)
        
        return {
            "query": query,
            "profile": result
        }
        
    except Exception as e:
        logger.error(f"分析查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 快捷查询接口 - 对应Browser的常用操作
# ================================

@router.get("/quick/nodes/{label}", summary="快速查询指定标签的节点")
async def quick_query_nodes_by_label(
    label: str, 
    limit: int = Query(default=100, le=1000)
) -> QueryExecution:
    """快速查询指定标签的节点"""
    request = CypherQueryRequest(
        query=f"MATCH (n:{label}) RETURN n LIMIT {limit}",
        view_mode=ViewMode.GRAPH
    )
    return browser_service.execute_cypher(request)

@router.get("/quick/relationships/{rel_type}", summary="快速查询指定类型的关系")
async def quick_query_relationships_by_type(
    rel_type: str,
    limit: int = Query(default=100, le=1000)
) -> QueryExecution:
    """快速查询指定类型的关系"""
    request = CypherQueryRequest(
        query=f"MATCH (a)-[r:{rel_type}]->(b) RETURN a, r, b LIMIT {limit}",
        view_mode=ViewMode.GRAPH
    )
    return browser_service.execute_cypher(request)

@router.get("/quick/sample", summary="获取数据库样本")
async def quick_sample_data(limit: int = Query(default=50, le=200)) -> QueryExecution:
    """获取数据库样本 - 对应Browser的Sample功能"""
    request = CypherQueryRequest(
        query=f"MATCH (n)-[r]-(m) RETURN n, r, m LIMIT {limit}",
        view_mode=ViewMode.GRAPH
    )
    return browser_service.execute_cypher(request) 