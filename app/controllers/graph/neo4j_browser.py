"""
Neo4j Browser风格的图谱查询API

使用business_id作为节点唯一ID，动态标签检测，纯净架构设计
"""

from fastapi import APIRouter, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import re

from app.services.data_storage.neo4j_simple_client import neo4j_simple_client
from app.utils.logger import get_logger
from app.utils.serializers import Neo4jSerializer

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/neo4j", tags=["Neo4j Browser"])

# ================================
# 数据模型
# ================================

class ResponseFormat(str, Enum):
    STANDARD = "standard"  # 标准格式：原始记录结构
    REFERENCE = "reference"  # 引用格式：拆分nodes和relationships

class QueryStatus(str, Enum):
    COMPLETED = "completed"
    FAILED = "failed"

class Neo4jNode(BaseModel):
    identity: str = Field(..., description="节点business_id")
    labels: List[str] = Field(..., description="节点标签")
    properties: Dict[str, Any] = Field(..., description="节点属性")

class Neo4jRelationship(BaseModel):
    identity: str = Field(..., description="关系标识")
    start: str = Field(..., description="起始节点business_id")
    end: str = Field(..., description="结束节点business_id")
    start_variable: str = Field(..., description="起始节点变量名")
    end_variable: str = Field(..., description="结束节点变量名")
    type: str = Field(..., description="关系类型")
    properties: Dict[str, Any] = Field(..., description="关系属性")

class QueryResult(BaseModel):
    keys: List[str] = Field(..., description="结果列名")
    records: List[Dict[str, Any]] = Field(..., description="结果记录")
    summary: Dict[str, Any] = Field(..., description="查询摘要")

class ReferenceQueryResult(BaseModel):
    keys: List[str] = Field(..., description="结果列名")
    nodes: Dict[str, List[Neo4jNode]] = Field(..., description="所有节点，按变量名分组")
    relationships: List[Neo4jRelationship] = Field(..., description="所有关系")
    summary: Dict[str, Any] = Field(..., description="查询摘要")

class QueryExecution(BaseModel):
    query: str = Field(..., description="执行的查询")
    status: QueryStatus = Field(..., description="执行状态")
    result: Optional[QueryResult] = Field(None, description="查询结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间(ms)")
    result_count: int = Field(..., description="结果数量")

class ReferenceQueryExecution(BaseModel):
    query: str = Field(..., description="执行的查询")
    status: QueryStatus = Field(..., description="执行状态")
    result: Optional[ReferenceQueryResult] = Field(None, description="查询结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间(ms)")
    result_count: int = Field(..., description="结果数量")

class CypherQueryRequest(BaseModel):
    query: str = Field(..., description="Cypher查询语句")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="查询参数")
    response_format: ResponseFormat = Field(default=ResponseFormat.REFERENCE, description="返回格式")

# ================================
# 核心服务
# ================================

class CypherAnalyzer:
    """Cypher语句分析器"""
    
    @staticmethod
    def extract_node_patterns(cypher_query: str) -> Dict[str, List[str]]:
        """从Cypher语句中提取节点模式和标签"""
        patterns = {}
        node_pattern = r'\((\w+)(?::(\w+(?::\w+)*))?\)'
        matches = re.findall(node_pattern, cypher_query, re.IGNORECASE)
        
        for variable, label_part in matches:
            if label_part:
                labels = label_part.split(':')
                patterns[variable] = labels
            else:
                patterns[variable] = []
        
        return patterns

class Neo4jBrowserService:
    """Neo4j Browser服务"""
    
    def __init__(self):
        self.serializer = Neo4jSerializer()
        self._schema_labels = None
        self.neo4j_client = neo4j_simple_client
        self.analyzer = CypherAnalyzer()
    
    def get_schema_labels(self) -> List[str]:
        """获取数据库中所有标签"""
        if self._schema_labels is None:
            result = neo4j_simple_client.execute_query("CALL db.labels() YIELD label RETURN label")
            self._schema_labels = [record['label'] for record in result]
        return self._schema_labels
    
    def extract_node(self, node_data: Any, variable_name: str, explicit_labels: List[str], cypher_patterns: Dict[str, List[str]]) -> Neo4jNode:
        """提取Neo4j节点"""
        properties = self.serializer.serialize(node_data)
        
        # 确定节点标签 - 纯净优先级
        if explicit_labels:
            # 1. 优先使用显式标签 labels(n) as n_labels
            labels = explicit_labels
        elif variable_name in cypher_patterns and cypher_patterns[variable_name]:
            # 2. 使用Cypher语句中的标签模式
            labels = cypher_patterns[variable_name]
        else:
            # 3. 无法确定时返回空标签
            labels = []
        
        # 使用business_id作为唯一标识
        identity = properties.get('business_id', f'unknown_{variable_name}')
        
        return Neo4jNode(
            identity=str(identity),
            labels=labels,
            properties=properties
        )
    
    def extract_relationship(self, rel_data: Any, start_node_id: str, end_node_id: str) -> Neo4jRelationship:
        """提取Neo4j关系"""
        properties = self.serializer.serialize(rel_data)
        rel_type = properties.pop('type', getattr(rel_data, 'type', 'RELATED'))
        
        identity = f"{start_node_id}_{rel_type}_{end_node_id}"
        
        return Neo4jRelationship(
            identity=identity,
            start=start_node_id,
            end=end_node_id,
            start_variable="unknown_start",
            end_variable="unknown_end",
            type=rel_type,
            properties=properties
        )
    
    def process_query_result(self, raw_results: List[Dict[str, Any]], cypher_query: str, response_format: ResponseFormat) -> QueryResult:
        """处理查询结果"""
        import time
        start_time = time.time()

        if not raw_results:
            empty_result = QueryResult(keys=[], records=[], summary={"result_count": 0}) if response_format == ResponseFormat.STANDARD else ReferenceQueryResult(keys=[], nodes=[], relationships=[], summary={"result_count": 0})
            return empty_result

        cypher_patterns = CypherAnalyzer.extract_node_patterns(cypher_query)
        keys = list(raw_results[0].keys())
        processed_records = []

        # 用于reference格式的收集器
        nodes_by_variable = {}  # variable_name -> List[Neo4jNode]
        relationships_map = {}  # identity -> Neo4jRelationship (去重用)

        # 性能监控计数器
        relationship_count = 0
        node_count = 0
        record_count = len(raw_results)
        
        for record in raw_results:
            processed_record = {}
            
            for key, value in record.items():
                # 首先检查是否为关系（优先级更高，避免被节点检测覆盖）
                if self._is_relationship(value):
                    logger.debug(f"检测到关系: key={key}, type={type(value)}")
                    relationships = []

                    # 1. 处理真正的Neo4j关系对象（最常见的情况）
                    if hasattr(value, 'type') and hasattr(value, 'start_node') and hasattr(value, 'end_node'):
                        logger.debug(f"处理Neo4j关系对象")
                        relationship = self.extract_neo4j_relationship(value, key, record)
                        if relationship:
                            relationships.append(relationship)

                    # 2. 处理关系字典格式
                    elif isinstance(value, dict) and 'type' in value:
                        logger.debug(f"处理关系字典格式")
                        relationship = self.extract_relationship_from_dict(value, key, record)
                        if relationship:
                            relationships.append(relationship)

                    # 3. 处理变长路径格式
                    elif isinstance(value, list) and len(value) > 0:
                        first_item = value[0]

                        # 3a. 处理Neo4j关系对象列表（变长路径查询结果）
                        if hasattr(first_item, 'type') and hasattr(first_item, 'start_node') and hasattr(first_item, 'end_node'):
                            logger.debug(f"进入Neo4j关系对象列表处理: {len(value)}个关系")
                            for rel_obj in value:  # 遍历关系对象列表
                                relationship = self.extract_neo4j_relationship(rel_obj, key, record)
                                if relationship:
                                    logger.debug(f"提取关系成功: {relationship.identity}")
                                    relationships.append(relationship)
                                else:
                                    logger.warning(f"提取关系失败: {rel_obj}")

                        # 3b. 处理tuple列表格式: [[start, type, end], ...] 或 [(start, type, end), ...]
                        elif isinstance(first_item, (list, tuple)):
                            logger.debug(f"进入变长路径tuple处理: {len(value)}个关系")
                            for rel_tuple in value:  # 遍历路径中的每个关系
                                if len(rel_tuple) == 3:
                                    relationship = self.extract_relationship_from_tuple(rel_tuple, key, record)
                                    if relationship:
                                        logger.debug(f"提取关系成功: {relationship.identity}")
                                        relationships.append(relationship)
                                    else:
                                        logger.warning(f"提取关系失败: {rel_tuple}")
                                else:
                                    logger.warning(f"关系tuple长度不对: {len(rel_tuple)}")

                        else:
                            logger.warning(f"未识别的列表格式: 第一个元素类型={type(first_item)}")

                    # 4. 处理简单tuple格式: (start, type, end) - 兼容性处理
                    elif isinstance(value, (list, tuple)) and len(value) == 3:
                        logger.debug(f"进入简单tuple处理")
                        relationship = self.extract_relationship_from_tuple(value, key, record)
                        if relationship:
                            relationships.append(relationship)

                    else:
                        logger.warning(f"未匹配任何关系格式: type={type(value)}, len={len(value) if hasattr(value, '__len__') else 'N/A'}")

                    # 处理关系数据
                    if relationships:
                        logger.debug(f"成功提取{len(relationships)}个关系")
                        if len(relationships) == 1:
                            # 单个关系，直接存储
                            processed_record[key] = relationships[0].model_dump()
                        else:
                            # 多个关系（路径），存储为数组
                            processed_record[key] = [rel.model_dump() for rel in relationships]

                        # 收集关系用于reference格式（去重）
                        if response_format == ResponseFormat.REFERENCE:
                            for relationship in relationships:
                                relationships_map[relationship.identity] = relationship
                                logger.debug(f"添加关系到map: {relationship.identity}")

                        # 更新性能计数器
                        relationship_count += len(relationships)
                    else:
                        logger.warning(f"未能提取任何关系: key={key}")
                        # 如果无法提取关系，保留原始值
                        processed_record[key] = self.serializer.serialize(value)

                elif self._is_node(value):
                    labels_key = f"{key}_labels"
                    explicit_labels = record.get(labels_key, [])

                    node = self.extract_node(value, key, explicit_labels, cypher_patterns)
                    processed_record[key] = node.model_dump()

                    # 收集节点用于reference格式，按变量名分组
                    if response_format == ResponseFormat.REFERENCE:
                        if key not in nodes_by_variable:
                            nodes_by_variable[key] = []
                        # 检查是否已存在相同的节点（去重）
                        existing_identities = [n.identity for n in nodes_by_variable[key]]
                        if node.identity not in existing_identities:
                            nodes_by_variable[key].append(node)

                    # 更新性能计数器
                    node_count += 1

                else:
                    processed_record[key] = self.serializer.serialize(value)

            processed_records.append(processed_record)
        
        # 计算处理时间
        processing_time = (time.time() - start_time) * 1000  # 转换为毫秒

        # 性能监控日志
        logger.info(f"查询结果处理完成 - 记录数: {record_count}, 节点数: {node_count}, "
                   f"关系数: {relationship_count}, 处理时间: {processing_time:.2f}ms")

        summary = {
            "result_count": len(processed_records),
            "query": cypher_query,
            "patterns": cypher_patterns,
            "performance": {
                "processing_time_ms": round(processing_time, 2),
                "record_count": record_count,
                "node_count": node_count,
                "relationship_count": relationship_count
            }
        }

        if response_format == ResponseFormat.STANDARD:
            return QueryResult(
                keys=keys,
                records=processed_records,
                summary=summary
            )
        else:  # REFERENCE
            return ReferenceQueryResult(
                keys=keys,
                nodes=nodes_by_variable,  # 按变量名分组的节点
                relationships=list(relationships_map.values()),  # 使用去重后的关系
                summary=summary
            )
    
    def _is_node(self, value: Any) -> bool:
        """判断是否为节点"""
        return isinstance(value, dict) and 'business_id' in value
    
    def _is_relationship(self, value: Any) -> bool:
        """判断是否为关系"""
        # 1. Neo4j关系对象检测（最常见的情况）- 优先级最高
        if hasattr(value, 'type') and hasattr(value, 'start_node') and hasattr(value, 'end_node'):
            return True

        # 2. 关系字典格式 - 但要排除节点字典
        if isinstance(value, dict) and 'type' in value:
            # 确保不是节点（节点通常有business_id而没有type作为关系类型）
            if 'business_id' not in value:
                return True

        # 3. 变长路径格式检测
        if isinstance(value, list) and len(value) > 0:
            first_item = value[0]

            # 3a. 检查是否为Neo4j关系对象的列表（变长路径查询结果）
            if hasattr(first_item, 'type') and hasattr(first_item, 'start_node') and hasattr(first_item, 'end_node'):
                return True

            # 3b. 检查是否为路径数组 - 每个元素都是长度为3的数组/元组
            elif isinstance(first_item, (list, tuple)) and len(first_item) == 3:
                # 进一步检查中间元素是否为关系类型字符串
                if isinstance(first_item[1], str) and len(first_item[1]) > 0:
                    # 确保第一个和第三个元素看起来像节点（有属性字典）
                    if isinstance(first_item[0], dict) and isinstance(first_item[2], dict):
                        return True

        # 4. Neo4j关系tuple格式: (start_node, relationship_object, end_node) - 新格式
        if isinstance(value, (list, tuple)) and len(value) == 3:
            start_item, middle_item, end_item = value

            # 检查中间元素是否为Neo4j关系对象
            if hasattr(middle_item, 'type') and hasattr(middle_item, 'start_node') and hasattr(middle_item, 'end_node'):
                # 确保第一个和第三个元素看起来像节点（有属性字典）
                if isinstance(start_item, dict) and isinstance(end_item, dict):
                    return True

            # 兼容旧格式：检查中间元素是否为字符串（关系类型）
            elif isinstance(middle_item, str) and len(middle_item) > 0:
                # 确保第一个和第三个元素看起来像节点（有属性字典）
                if (isinstance(start_item, dict) and isinstance(end_item, dict) and
                    not hasattr(start_item, 'type') and not hasattr(end_item, 'type')):
                    return True

        return False

    def extract_neo4j_relationship(self, rel_obj: Any, rel_key: str, record: Dict[str, Any]) -> Neo4jRelationship:
        """从真正的Neo4j关系对象中提取关系信息 - 性能最优的方法"""
        try:
            # 提取关系类型
            rel_type = rel_obj.type

            # 提取关系属性 - 尝试多种方式获取属性
            properties = {}

            # 调试信息
            logger.debug(f"处理关系对象: type={type(rel_obj)}")
            if hasattr(rel_obj, '_properties'):
                logger.debug(f"_properties存在: {bool(rel_obj._properties)}")
            if hasattr(rel_obj, '__dict__'):
                logger.debug(f"__dict__存在: {bool(rel_obj.__dict__)}")

            # 方法1: 尝试 _properties 属性
            if hasattr(rel_obj, '_properties') and rel_obj._properties:
                properties = dict(rel_obj._properties)
                logger.debug(f"通过_properties提取到关系属性: {properties}")

            # 方法2: 尝试直接访问属性字典
            elif hasattr(rel_obj, '__dict__'):
                # 过滤掉内部属性，只保留用户定义的属性
                obj_dict = rel_obj.__dict__
                properties = {k: v for k, v in obj_dict.items()
                            if not k.startswith('_') and k not in ['type', 'start_node', 'end_node', 'id', 'element_id']}
                logger.debug(f"通过__dict__提取到关系属性: {properties}")

            # 方法3: 尝试使用 dict() 转换
            elif hasattr(rel_obj, '__iter__'):
                try:
                    properties = dict(rel_obj)
                    # 移除系统属性
                    properties.pop('type', None)
                    logger.debug(f"通过dict()转换提取到关系属性: {properties}")
                except:
                    pass

            # 方法4: 检查是否有其他常见的属性访问方式
            if not properties:
                # 尝试检查对象的所有属性
                for attr_name in dir(rel_obj):
                    if (not attr_name.startswith('_') and
                        attr_name not in ['type', 'start_node', 'end_node', 'id', 'element_id'] and
                        not callable(getattr(rel_obj, attr_name, None))):
                        try:
                            attr_value = getattr(rel_obj, attr_name)
                            if attr_value is not None:
                                properties[attr_name] = attr_value
                        except:
                            pass
                logger.debug(f"通过dir()遍历提取到关系属性: {properties}")

            # 序列化属性
            if properties:
                properties = self.serializer.serialize(properties)
                logger.debug(f"序列化后的关系属性: {properties}")
            else:
                logger.info(f"未能提取到关系属性，关系对象类型: {type(rel_obj)}")

            # 提取起始和结束节点ID
            start_id = "unknown_start"
            end_id = "unknown_end"

            # 从关系对象的节点引用中获取business_id
            if hasattr(rel_obj, 'start_node') and hasattr(rel_obj.start_node, '_properties'):
                start_props = dict(rel_obj.start_node._properties)
                start_id = start_props.get('business_id', str(rel_obj.start_node.id))

            if hasattr(rel_obj, 'end_node') and hasattr(rel_obj.end_node, '_properties'):
                end_props = dict(rel_obj.end_node._properties)
                end_id = end_props.get('business_id', str(rel_obj.end_node.id))

            # 推断起始和结束节点的变量名
            start_variable = "unknown_start"
            end_variable = "unknown_end"

            # 通过匹配business_id来找到对应的变量名
            for key, value in record.items():
                if key != rel_key:
                    # 检查是否是字典格式的节点（已处理的节点）
                    if isinstance(value, dict) and 'business_id' in value:
                        if value['business_id'] == start_id:
                            start_variable = key
                        if value['business_id'] == end_id:
                            end_variable = key
                    # 检查是否是Neo4j节点对象（原始节点）
                    elif self._is_node(value):
                        try:
                            # 提取节点的business_id
                            if hasattr(value, '_properties'):
                                node_props = dict(value._properties)
                                node_business_id = node_props.get('business_id', str(value.id))
                                if node_business_id == start_id:
                                    start_variable = key
                                if node_business_id == end_id:
                                    end_variable = key
                        except Exception as e:
                            logger.debug(f"提取节点business_id失败: {e}")
                            continue

            # 生成关系唯一标识
            identity = f"{start_id}_{rel_type}_{end_id}"

            return Neo4jRelationship(
                identity=identity,
                start=str(start_id),
                end=str(end_id),
                start_variable=start_variable,
                end_variable=end_variable,
                type=rel_type,
                properties=properties
            )

        except Exception as e:
            logger.warning(f"提取Neo4j关系对象失败: {e}")
            return None

    def extract_relationship_from_dict(self, rel_dict: Dict[str, Any], rel_key: str, record: Dict[str, Any]) -> Neo4jRelationship:
        """从关系字典中提取关系信息"""
        try:
            rel_type = rel_dict.get('type', 'UNKNOWN')
            properties = {k: v for k, v in rel_dict.items() if k != 'type'}
            properties = self.serializer.serialize(properties)

            # 尝试从字典中获取节点ID信息
            start_id = rel_dict.get('start_id', 'unknown_start')
            end_id = rel_dict.get('end_id', 'unknown_end')

            # 推断变量名
            start_variable = "unknown_start"
            end_variable = "unknown_end"

            # 通过匹配business_id来找到对应的变量名
            for key, value in record.items():
                if key != rel_key:
                    # 检查是否是字典格式的节点（已处理的节点）
                    if isinstance(value, dict) and 'business_id' in value:
                        if value['business_id'] == start_id:
                            start_variable = key
                        if value['business_id'] == end_id:
                            end_variable = key
                    # 检查是否是Neo4j节点对象（原始节点）
                    elif self._is_node(value):
                        try:
                            # 提取节点的business_id
                            if hasattr(value, '_properties'):
                                node_props = dict(value._properties)
                                node_business_id = node_props.get('business_id', str(value.id))
                                if node_business_id == start_id:
                                    start_variable = key
                                if node_business_id == end_id:
                                    end_variable = key
                        except Exception as e:
                            logger.debug(f"提取节点business_id失败: {e}")
                            continue

            identity = f"{start_id}_{rel_type}_{end_id}"

            return Neo4jRelationship(
                identity=identity,
                start=str(start_id),
                end=str(end_id),
                start_variable=start_variable,
                end_variable=end_variable,
                type=rel_type,
                properties=properties
            )

        except Exception as e:
            logger.warning(f"提取关系字典失败: {e}")
            return None

    def extract_relationship_from_tuple(self, rel_tuple: List[Any], rel_key: str, record: Dict[str, Any]) -> Neo4jRelationship:
        """从Neo4j关系元组中提取关系信息"""
        if len(rel_tuple) != 3:
            return None

        start_node_data, rel_data, end_node_data = rel_tuple

        # 调试信息
        logger.debug(f"处理关系tuple: start_type={type(start_node_data)}, rel_type={type(rel_data)}, end_type={type(end_node_data)}")

        # 提取关系类型和属性
        rel_type = "UNKNOWN"
        properties = {}

        # 如果中间元素是字符串，说明是简化格式 (node, "TYPE", node)
        if isinstance(rel_data, str):
            rel_type = rel_data
            logger.debug(f"简化格式关系，类型: {rel_type}")

        # 如果中间元素是关系对象，提取完整信息
        elif hasattr(rel_data, 'type') and hasattr(rel_data, 'start_node') and hasattr(rel_data, 'end_node'):
            rel_type = rel_data.type
            logger.debug(f"关系对象格式，类型: {rel_type}")

            # 提取关系属性 - 尝试多种方式
            if hasattr(rel_data, '_properties') and rel_data._properties:
                properties = dict(rel_data._properties)
                logger.debug(f"从_properties提取到关系属性: {properties}")
            elif hasattr(rel_data, '__dict__'):
                # 过滤掉内部属性
                obj_dict = rel_data.__dict__
                properties = {k: v for k, v in obj_dict.items()
                            if not k.startswith('_') and k not in ['type', 'start_node', 'end_node', 'id', 'element_id']}
                logger.debug(f"从__dict__提取到关系属性: {properties}")
            else:
                # 尝试直接迭代关系对象
                try:
                    properties = dict(rel_data)
                    # 移除系统属性
                    properties.pop('type', None)
                    logger.debug(f"从dict()转换提取到关系属性: {properties}")
                except:
                    logger.debug(f"无法从关系对象提取属性")

            # 序列化属性
            if properties:
                properties = self.serializer.serialize(properties)
                logger.debug(f"序列化后的关系属性: {properties}")

        # 如果中间元素是字典，提取类型和属性
        elif isinstance(rel_data, dict):
            rel_type = rel_data.get('type', 'UNKNOWN')
            properties = {k: v for k, v in rel_data.items() if k != 'type'}
            properties = self.serializer.serialize(properties)
            logger.debug(f"字典格式关系，类型: {rel_type}, 属性: {properties}")

        # 提取起始和结束节点的business_id
        start_id = "unknown_start"
        end_id = "unknown_end"

        if isinstance(start_node_data, dict) and 'business_id' in start_node_data:
            start_id = start_node_data['business_id']

        if isinstance(end_node_data, dict) and 'business_id' in end_node_data:
            end_id = end_node_data['business_id']

        # 推断起始和结束节点的变量名
        start_variable = "unknown_start"
        end_variable = "unknown_end"

        # 通过匹配business_id来找到对应的变量名
        for key, value in record.items():
            if key != rel_key:
                # 检查是否是字典格式的节点（已处理的节点）
                if isinstance(value, dict) and 'business_id' in value:
                    if value['business_id'] == start_id:
                        start_variable = key
                    if value['business_id'] == end_id:
                        end_variable = key
                # 检查是否是Neo4j节点对象（原始节点）
                elif self._is_node(value):
                    try:
                        # 提取节点的business_id
                        if hasattr(value, '_properties'):
                            node_props = dict(value._properties)
                            node_business_id = node_props.get('business_id', str(value.id))
                            if node_business_id == start_id:
                                start_variable = key
                            if node_business_id == end_id:
                                end_variable = key
                    except Exception as e:
                        logger.debug(f"提取节点business_id失败: {e}")
                        continue

        # 生成关系唯一标识
        identity = f"{start_id}_{rel_type}_{end_id}"

        logger.debug(f"最终关系信息: identity={identity}, type={rel_type}, properties={properties}")

        return Neo4jRelationship(
            identity=identity,
            start=str(start_id),
            end=str(end_id),
            start_variable=start_variable,
            end_variable=end_variable,
            type=rel_type,
            properties=properties
        )
    

    def execute_cypher(self, request: CypherQueryRequest):
        """执行Cypher查询"""
        start_time = datetime.now()
        
        try:
            results = neo4j_simple_client.execute_query(request.query, request.parameters)
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            query_result = self.process_query_result(results, request.query, request.response_format)
            
            if request.response_format == ResponseFormat.STANDARD:
                return QueryExecution(
                    query=request.query,
                    status=QueryStatus.COMPLETED,
                    result=query_result,
                    execution_time=execution_time,
                    result_count=len(query_result.records)
                )
            else:  # REFERENCE
                return ReferenceQueryExecution(
                    query=request.query,
                    status=QueryStatus.COMPLETED,
                    result=query_result,
                    execution_time=execution_time,
                    result_count=len(query_result.nodes) + len(query_result.relationships)
                )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            if request.response_format == ResponseFormat.STANDARD:
                return QueryExecution(
                    query=request.query,
                    status=QueryStatus.FAILED,
                    error=str(e),
                    execution_time=execution_time,
                    result_count=0
                )
            else:  # REFERENCE
                return ReferenceQueryExecution(
                    query=request.query,
                    status=QueryStatus.FAILED,
                    error=str(e),
                    execution_time=execution_time,
                    result_count=0
                )

# 实例化服务
browser_service = Neo4jBrowserService()

# ================================
# API接口
# ================================

@router.post("/query", summary="执行Cypher查询")
async def execute_cypher_query(request: CypherQueryRequest):
    """执行Cypher查询"""
    return browser_service.execute_cypher(request)

@router.get("/schema", summary="获取数据库模式信息")
async def get_database_schema() -> Dict[str, Any]:
    """获取数据库模式信息"""
    labels_result = neo4j_simple_client.execute_query("CALL db.labels() YIELD label RETURN label ORDER BY label")
    labels = [record['label'] for record in labels_result]
    
    rel_types_result = neo4j_simple_client.execute_query("CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType")
    relationship_types = [record['relationshipType'] for record in rel_types_result]
    
    prop_keys_result = neo4j_simple_client.execute_query("CALL db.propertyKeys() YIELD propertyKey RETURN propertyKey ORDER BY propertyKey")
    property_keys = [record['propertyKey'] for record in prop_keys_result]
    
    return {
        "labels": labels,
        "relationship_types": relationship_types,
        "property_keys": property_keys
    }

@router.get("/statistics", summary="获取数据库统计信息")
async def get_database_statistics() -> Dict[str, Any]:
    """获取数据库统计信息"""
    nodes_result = neo4j_simple_client.execute_query("MATCH (n) RETURN labels(n) as labels, count(n) as count")
    
    node_stats = {}
    total_nodes = 0
    for record in nodes_result:
        labels = record['labels']
        count = record['count']
        total_nodes += count
        
        if labels:
            label_key = ':'.join(sorted(labels))
            node_stats[label_key] = count
    
    rels_result = neo4j_simple_client.execute_query("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count")
    
    relationship_stats = {}
    total_relationships = 0
    for record in rels_result:
        rel_type = record['type']
        count = record['count']
        total_relationships += count
        relationship_stats[rel_type] = count
    
    return {
        "total_nodes": total_nodes,
        "total_relationships": total_relationships,
        "nodes_by_label": node_stats,
        "relationships_by_type": relationship_stats
    }

@router.get("/quick/sample", summary="获取数据库样本")
async def quick_sample_data(limit: int = Query(default=50, le=200)):
    """获取数据库样本"""
    request = CypherQueryRequest(
        query=f"MATCH (n)-[r]-(m) RETURN n, labels(n) as n_labels, r, m, labels(m) as m_labels LIMIT {limit}",
        response_format=ResponseFormat.STANDARD  # 明确指定使用STANDARD格式
    )
    return browser_service.execute_cypher(request) 