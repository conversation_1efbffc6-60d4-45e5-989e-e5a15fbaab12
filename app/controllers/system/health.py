"""
系统健康检查控制器

提供整个系统的健康状态监控API接口
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import asyncio
from datetime import datetime

from app.services.data_storage.neo4j_client import Neo4jClient
from app.utils.logger import get_logger
from app.utils.serializers import serialize_neo4j_record, serialize_neo4j_value

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/system/health", tags=["系统健康检查"])


@router.get("/", summary="系统整体健康检查")
async def system_health_check() -> Dict[str, Any]:
    """
    系统整体健康检查
    
    Returns:
        Dict[str, Any]: 系统健康状态
    """
    try:
        health_results = {}
        overall_healthy = True
        
        # 检查Neo4j数据库连接
        try:
            neo4j_client = Neo4jClient()
            # 执行简单查询测试连接
            result = neo4j_client.execute_query("RETURN 1 as test")
            if result and len(result) > 0:
                health_results["neo4j"] = {
                    "status": "healthy",
                    "message": "数据库连接正常",
                    "response_time": "< 100ms"
                }
            else:
                raise Exception("查询返回为空")
        except Exception as e:
            health_results["neo4j"] = {
                "status": "unhealthy",
                "message": f"数据库连接异常: {str(e)}"
            }
            overall_healthy = False
        
        # 检查数据库统计信息
        try:
            stats_query = """
            CALL apoc.meta.stats() YIELD labels, relTypesCount, nodeCount, relCount
            RETURN labels, relTypesCount, nodeCount, relCount
            """
            stats_result = neo4j_client.execute_query(stats_query)
            if stats_result and len(stats_result) > 0:
                stats = stats_result[0]
                health_results["database_stats"] = {
                    "status": "healthy",
                    "node_count": stats.get("nodeCount", 0),
                    "relationship_count": stats.get("relCount", 0),
                    "label_count": len(stats.get("labels", {})),
                    "relationship_type_count": stats.get("relTypesCount", 0)
                }
            else:
                health_results["database_stats"] = {
                    "status": "unknown",
                    "message": "无法获取数据库统计信息"
                }
        except Exception as e:
            health_results["database_stats"] = {
                "status": "error",
                "message": f"获取统计信息失败: {str(e)}"
            }
        
        return {
            "success": overall_healthy,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy" if overall_healthy else "unhealthy",
            "components": health_results,
            "message": "系统健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"系统健康检查失败: {str(e)}")
        return {
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e),
            "message": "系统健康检查异常"
        }


@router.get("/database", summary="数据库健康检查")
async def database_health_check() -> Dict[str, Any]:
    """
    数据库专项健康检查
    
    Returns:
        Dict[str, Any]: 数据库健康状态
    """
    try:
        neo4j_client = Neo4jClient()
        
        # 基础连接测试
        start_time = datetime.now()
        result = neo4j_client.execute_query("RETURN 1 as test")
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds() * 1000
        
        if not result:
            raise Exception("数据库查询无响应")
        
        # 数据库版本信息
        version_result = neo4j_client.execute_query("CALL dbms.components()")
        version_info = version_result[0] if version_result else {}
        
        # 数据库统计信息
        stats_query = """
        MATCH (n) 
        RETURN count(n) as total_nodes
        UNION ALL
        MATCH ()-[r]->()
        RETURN count(r) as total_relationships
        """
        stats_result = neo4j_client.execute_query(stats_query)
        
        # 内存使用情况
        memory_query = "CALL dbms.queryJmx('java.lang:type=Memory') YIELD attributes RETURN attributes.HeapMemoryUsage as heap"
        memory_result = neo4j_client.execute_query(memory_query)
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "status": "healthy",
            "performance": {
                "response_time_ms": round(response_time, 2),
                "status": "good" if response_time < 100 else "slow" if response_time < 1000 else "poor"
            },
            "version": {
                "name": version_info.get("name", "Unknown"),
                "version": version_info.get("version", "Unknown"),
                "edition": version_info.get("edition", "Unknown")
            },
            "statistics": {
                "total_nodes": stats_result[0].get("total_nodes", 0) if stats_result else 0,
                "total_relationships": stats_result[1].get("total_relationships", 0) if len(stats_result) > 1 else 0
            },
            "memory": memory_result[0].get("heap", {}) if memory_result else {},
            "message": "数据库健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"数据库健康检查失败: {str(e)}")
        return {
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "status": "unhealthy",
            "error": str(e),
            "message": "数据库健康检查异常"
        }


def _serialize_service_stats(stats: Dict[str, Any]) -> Dict[str, Any]:
    """
    序列化服务统计数据，处理Neo4j DateTime对象
    
    Args:
        stats: 原始统计数据
        
    Returns:
        Dict[str, Any]: 序列化后的统计数据
    """
    if not stats:
        return {}
    
    try:
        serialized_stats = {}
        for key, value in stats.items():
            if isinstance(value, dict):
                # 递归处理嵌套字典
                serialized_stats[key] = _serialize_service_stats(value)
            elif isinstance(value, list):
                # 处理列表中的每个元素
                serialized_stats[key] = [serialize_neo4j_value(item) for item in value]
            else:
                # 处理单个值
                serialized_stats[key] = serialize_neo4j_value(value)
        
        return serialized_stats
    except Exception as e:
        logger.warning(f"序列化服务统计数据失败: {str(e)}")
        return {"error": "序列化失败", "original_keys": list(stats.keys())}


@router.get("/services", summary="服务组件健康检查")
async def services_health_check() -> Dict[str, Any]:
    """
    服务组件健康检查
    
    Returns:
        Dict[str, Any]: 服务组件健康状态
    """
    try:
        services_status = {}
        overall_healthy = True
        
        # 检查各个服务组件
        service_modules = [
            ("area_service", "app.services.node_service.area_service", "AreaService"),
            ("person_service", "app.services.node_service.insurance_agent_person_service", "InsuranceAgentPersonService"),
            ("organization_service", "app.services.node_service.insurance_company_service", "InsuranceCompanyService"),
            ("relationship_service", "app.services.relationship_service.area_hierarchy_relationship_service", "AreaHierarchyRelationshipService")
        ]
        
        for service_name, module_path, class_name in service_modules:
            try:
                # 动态导入和实例化服务
                module = __import__(module_path, fromlist=[class_name])
                service_class = getattr(module, class_name)
                service = service_class()
                
                # 尝试获取统计信息来测试服务
                stats = service.get_statistics()
                
                # 序列化统计数据，处理Neo4j DateTime对象
                serialized_stats = _serialize_service_stats(stats)
                
                services_status[service_name] = {
                    "status": "healthy",
                    "module": module_path,
                    "class": class_name,
                    "stats": serialized_stats
                }
                
            except Exception as e:
                services_status[service_name] = {
                    "status": "unhealthy",
                    "module": module_path,
                    "class": class_name,
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "success": overall_healthy,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy" if overall_healthy else "partially_healthy",
            "services": services_status,
            "message": "服务组件健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"服务组件健康检查失败: {str(e)}")
        return {
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e),
            "message": "服务组件健康检查异常"
        }


@router.get("/detailed", summary="详细健康检查报告")
async def detailed_health_check() -> Dict[str, Any]:
    """
    生成详细的健康检查报告
    
    Returns:
        Dict[str, Any]: 详细健康检查报告
    """
    try:
        # 并行执行多个健康检查
        async def run_checks():
            system_check = await system_health_check()
            db_check = await database_health_check()
            services_check = await services_health_check()
            return system_check, db_check, services_check
        
        system_result, db_result, services_result = await run_checks()
        
        # 综合评估
        overall_healthy = (
            system_result.get("success", False) and
            db_result.get("success", False) and
            services_result.get("success", False)
        )
        
        return {
            "success": overall_healthy,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy" if overall_healthy else "unhealthy",
            "summary": {
                "system_status": system_result.get("overall_status", "unknown"),
                "database_status": db_result.get("status", "unknown"),
                "services_status": services_result.get("overall_status", "unknown")
            },
            "details": {
                "system": system_result,
                "database": db_result,
                "services": services_result
            },
            "recommendations": _generate_health_recommendations(system_result, db_result, services_result),
            "message": "详细健康检查报告生成完成"
        }
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {str(e)}")
        return {
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e),
            "message": "详细健康检查异常"
        }


def _generate_health_recommendations(system_result: Dict, db_result: Dict, services_result: Dict) -> List[str]:
    """
    根据健康检查结果生成建议
    
    Args:
        system_result: 系统检查结果
        db_result: 数据库检查结果
        services_result: 服务检查结果
        
    Returns:
        List[str]: 建议列表
    """
    recommendations = []
    
    # 数据库性能建议
    if db_result.get("performance", {}).get("response_time_ms", 0) > 100:
        recommendations.append("数据库响应时间较慢，建议检查查询优化和索引配置")
    
    # 服务状态建议
    services_status = services_result.get("services", {})
    unhealthy_services = [name for name, status in services_status.items() if status.get("status") != "healthy"]
    if unhealthy_services:
        recommendations.append(f"以下服务状态异常，需要检查: {', '.join(unhealthy_services)}")
    
    # 数据库连接建议
    if not system_result.get("components", {}).get("neo4j", {}).get("status") == "healthy":
        recommendations.append("数据库连接异常，请检查Neo4j服务状态和网络连接")
    
    if not recommendations:
        recommendations.append("系统运行状态良好，无需特殊关注")
    
    return recommendations 