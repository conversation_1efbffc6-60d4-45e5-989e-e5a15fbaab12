"""
人员节点管理控制器

提供所有类型人员节点的统一管理API接口
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, Dict, Any, List
from enum import Enum

from app.services.node_service.insurance_agent_person_service import InsuranceAgentPersonService
from app.services.node_service.insurance_bd_person_service import InsuranceBDPersonService
from app.services.node_service.insurance_mo_person_service import InsuranceMOPersonService
from app.services.node_service.insured_person_service import InsuredPersonService
from app.utils.logger import get_logger
from app.utils.controller_base import ControllerBase, ApiResponse, api_handler

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/nodes/persons", tags=["人员节点管理"])


class PersonType(str, Enum):
    """人员类型枚举"""
    AGENT = "agents"        # 代理人
    BD = "bd"              # BD人员
    MO = "mo"              # MO人员
    INSURED = "insured"    # 被保险人


# 服务实例映射
SERVICE_MAP = {
    PersonType.AGENT: InsuranceAgentPersonService,
    PersonType.BD: InsuranceBDPersonService,
    PersonType.MO: InsuranceMOPersonService,
    PersonType.INSURED: InsuredPersonService
}


@router.get("/{person_type}/", summary="搜索指定类型的人员")
@api_handler(operation_name="搜索人员", success_message="搜索完成")
async def search_persons(
    person_type: PersonType = Path(..., description="人员类型"),
    name: Optional[str] = Query(None, description="姓名关键词"),
    code: Optional[str] = Query(None, description="人员编码关键词"),
    phone: Optional[str] = Query(None, description="手机号关键词"),
    city: Optional[str] = Query(None, description="城市关键词"),
    status: Optional[str] = Query(None, description="状态"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """
    搜索指定类型的人员
    
    Args:
        person_type: 人员类型 (agents/bd/mo/insured)
        name: 姓名关键词
        code: 人员编码关键词
        phone: 手机号关键词
        city: 城市关键词
        status: 状态
        limit: 返回记录数限制
        
    Returns:
        Dict[str, Any]: 包含人员列表和元数据的响应
    """
    service_class = SERVICE_MAP.get(person_type)
    if not service_class:
        raise HTTPException(status_code=400, detail=f"不支持的人员类型: {person_type}")
    
    service = service_class()
    
    # 根据不同的人员类型调用对应的搜索方法
    if person_type == PersonType.AGENT:
        persons = service.search_insurance_agent_persons(
            name_filter=name,
            code_filter=code,
            phone_filter=phone,
            city_filter=city,
            status_filter=status,
            limit=limit
        )
    elif person_type == PersonType.BD:
        persons = service.search_insurance_bd_persons(
            name_filter=name,
            code_filter=code,
            phone_filter=phone,
            city_filter=city,
            status_filter=status,
            limit=limit
        )
    elif person_type == PersonType.MO:
        persons = service.search_insurance_mo_persons(
            name_filter=name,
            code_filter=code,
            phone_filter=phone,
            city_filter=city,
            status_filter=status,
            limit=limit
        )
    elif person_type == PersonType.INSURED:
        persons = service.search_insured_persons(
            name_filter=name,
            id_card_filter=code,
            limit=limit
        )
    
    return {
        "data": persons,
        "total": len(persons),
        "person_type": person_type
    }


@router.get("/{person_type}/statistics", summary="获取人员统计信息")
async def get_person_statistics(
    person_type: PersonType = Path(..., description="人员类型")
) -> Dict[str, Any]:
    """
    获取指定类型人员的统计信息
    
    Args:
        person_type: 人员类型
        
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        service_class = SERVICE_MAP.get(person_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的人员类型: {person_type}")
        
        service = service_class()
        stats = service.get_statistics()
        
        return {
            "success": True,
            "data": stats,
            "person_type": person_type,
            "message": "统计信息获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{person_type}人员统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/{person_type}/{code}", summary="根据编码获取人员")
async def get_person_by_code(
    person_type: PersonType = Path(..., description="人员类型"),
    code: str = Path(..., description="人员编码")
) -> Dict[str, Any]:
    """
    根据编码获取单个人员
    
    Args:
        person_type: 人员类型
        code: 人员编码
        
    Returns:
        Dict[str, Any]: 人员详细信息
    """
    try:
        service_class = SERVICE_MAP.get(person_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的人员类型: {person_type}")
        
        service = service_class()
        
        # 根据不同的人员类型调用对应的获取方法
        if person_type == PersonType.AGENT:
            person = service.get_insurance_agent_person_by_code(code)
        elif person_type == PersonType.BD:
            person = service.get_insurance_bd_person_by_code(code)
        elif person_type == PersonType.MO:
            person = service.get_insurance_mo_person_by_code(code)
        elif person_type == PersonType.INSURED:
            person = service.get_insured_person_by_code(code)
        
        if person:
            return {
                "success": True,
                "data": person,
                "person_type": person_type,
                "message": "获取成功"
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到编码为 {code} 的{person_type}人员")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{person_type}人员失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.post("/{person_type}/import", summary="导入人员数据")
async def import_persons(
    person_type: PersonType = Path(..., description="人员类型"),
    clear_existing: bool = Query(False, description="是否清空现有数据")
) -> Dict[str, Any]:
    """
    从数据源导入指定类型的人员数据
    
    Args:
        person_type: 人员类型
        clear_existing: 是否清空现有数据
        
    Returns:
        Dict[str, Any]: 导入结果
    """
    try:
        service_class = SERVICE_MAP.get(person_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的人员类型: {person_type}")
        
        service = service_class()
        
        # 如果需要清空现有数据
        if clear_existing:
            if person_type == PersonType.AGENT:
                success = service.delete_all_insurance_agent_persons()
            elif person_type == PersonType.BD:
                success = service.delete_all_insurance_bd_persons()
            elif person_type == PersonType.MO:
                success = service.delete_all_insurance_mo_persons()
            elif person_type == PersonType.INSURED:
                success = service.delete_all_insured_persons()
            
            if not success:
                raise HTTPException(status_code=500, detail="清空现有数据失败")
        
        # 执行数据导入
        if person_type == PersonType.AGENT:
            result = service.extract_and_store_insurance_agent_persons()
        elif person_type == PersonType.BD:
            result = service.extract_and_store_insurance_bd_persons()
        elif person_type == PersonType.MO:
            result = service.extract_and_store_insurance_mo_persons()
        elif person_type == PersonType.INSURED:
            result = service.extract_and_store_insured_persons()
        
        if result.get("success", True):
            return {
                "success": True,
                "data": result,
                "person_type": person_type,
                "message": f"{person_type}人员数据导入成功"
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail=f"数据导入失败: {result.get('error', '未知错误')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入{person_type}人员数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.delete("/{person_type}/", summary="删除所有指定类型的人员数据")
async def delete_all_persons(
    person_type: PersonType = Path(..., description="人员类型")
) -> Dict[str, Any]:
    """
    删除所有指定类型的人员数据
    
    Args:
        person_type: 人员类型
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        service_class = SERVICE_MAP.get(person_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的人员类型: {person_type}")
        
        service = service_class()
        
        # 根据人员类型调用对应的删除方法
        if person_type == PersonType.AGENT:
            success = service.delete_all_insurance_agent_persons()
        elif person_type == PersonType.BD:
            success = service.delete_all_insurance_bd_persons()
        elif person_type == PersonType.MO:
            success = service.delete_all_insurance_mo_persons()
        elif person_type == PersonType.INSURED:
            success = service.delete_all_insured_persons()
        
        if success:
            return {
                "success": True,
                "person_type": person_type,
                "message": f"所有{person_type}人员数据已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除{person_type}人员数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/", summary="获取所有人员类型的统计概览")
async def get_all_persons_overview() -> Dict[str, Any]:
    """
    获取所有人员类型的统计概览
    
    Returns:
        Dict[str, Any]: 所有人员类型的统计信息
    """
    try:
        overview = {}
        
        for person_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_statistics()
                overview[person_type] = stats
            except Exception as e:
                logger.warning(f"获取{person_type}统计失败: {str(e)}")
                overview[person_type] = {"error": str(e)}
        
        return {
            "success": True,
            "data": overview,
            "message": "人员统计概览获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取人员统计概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取概览失败: {str(e)}")


@router.get("/health", summary="人员服务健康检查")
async def health_check() -> Dict[str, Any]:
    """
    人员节点服务健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        health_status = {}
        overall_healthy = True
        
        for person_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_statistics()
                health_status[person_type] = {
                    "status": "healthy",
                    "total_count": stats.get("total_count", 0)
                }
            except Exception as e:
                health_status[person_type] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "success": overall_healthy,
            "service": "PersonNodeService",
            "status": "healthy" if overall_healthy else "partially_healthy",
            "details": health_status,
            "message": "人员服务健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"人员服务健康检查失败: {str(e)}")
        return {
            "success": False,
            "service": "PersonNodeService",
            "status": "unhealthy",
            "error": str(e),
            "message": "人员服务异常"
        } 