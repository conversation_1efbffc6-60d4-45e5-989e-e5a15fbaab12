"""
保单节点管理控制器

提供保单节点的标准化REST API接口
"""

from fastapi import APIRouter, Query, Path
from typing import Optional, Dict, Any

from app.services.node_service.policy_service import PolicyService
from app.utils.controller_base import ApiResponse, ControllerBase, api_handler

router = APIRouter(prefix="/api/v1/nodes/policies", tags=["保单节点管理"])

# 控制器实例
controller = ControllerBase(PolicyService)


@router.get("/", summary="搜索保单节点")
@api_handler(operation_name="搜索保单")
async def search_policies(
    policy_code: Optional[str] = Query(None, description="保单编码关键词"),
    insured_name: Optional[str] = Query(None, description="被保险人姓名关键词"),
    insurance_code: Optional[str] = Query(None, description="保险公司编码"),
    product_code: Optional[str] = Query(None, description="产品编码"),
    policy_status: Optional[str] = Query(None, description="保单状态"),
    start_date: Optional[str] = Query(None, description="起保日期(YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="终保日期(YYYY-MM-DD)"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """搜索保单节点"""
    policies = controller.service.search_policies(
        policy_code_filter=policy_code,
        insured_name_filter=insured_name,
        insurance_code_filter=insurance_code,
        product_code_filter=product_code,
        policy_status_filter=policy_status,
        start_date_filter=start_date,
        end_date_filter=end_date,
        limit=limit
    )
    
    return ApiResponse.success(data=policies, total=len(policies))


@router.get("/statistics", summary="获取保单统计信息")
@api_handler(operation_name="获取保单统计")
async def get_statistics() -> Dict[str, Any]:
    """获取保单节点统计信息"""
    stats = controller.service.get_statistics()
    return ApiResponse.success(data=stats)


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """保单服务健康检查"""
    try:
        stats = controller.service.get_statistics()
        is_healthy = stats.get('total_count', 0) >= 0
        
        return ApiResponse.success(
            data={
                "service": "policy_service",
                "status": "healthy" if is_healthy else "unhealthy"
            },
            message="保单服务运行正常" if is_healthy else "保单服务异常"
        )
    except Exception as e:
        return ApiResponse.error(f"健康检查失败: {str(e)}", 503)


@router.post("/import", summary="导入保单数据")
@api_handler(operation_name="导入保单数据")
async def import_policies(
    clear_existing: bool = Query(False, description="是否清空现有数据"),
    limit: Optional[int] = Query(None, description="导入记录数限制"),
    where_conditions: Optional[str] = Query(None, description="额外的WHERE条件"),
    custom_day: Optional[str] = Query(None, description="自定义日期(YYYY-MM-DD)")
) -> Dict[str, Any]:
    """从数据源导入保单数据"""
    if clear_existing:
        success = controller.service.delete_all_policies()
        if not success:
            return ApiResponse.error("清空现有数据失败")
    
    result = controller.service.extract_and_store_policies(
        limit=limit,
        where_conditions=where_conditions,
        custom_day=custom_day
    )
    
    if not result.get("success", True):
        return ApiResponse.error(f"数据导入失败: {result.get('error', '未知错误')}")
    
    return ApiResponse.success(data=result, message="保单数据导入成功")


@router.delete("/", summary="删除所有保单数据")
@api_handler(operation_name="删除保单数据")
async def delete_all_policies() -> Dict[str, Any]:
    """删除所有保单数据"""
    success = controller.service.delete_all_policies()
    
    if not success:
        return ApiResponse.error("删除失败")
    
    return ApiResponse.success(message="所有保单数据已删除")


@router.get("/{policy_code}", summary="根据编码获取保单")
@api_handler(operation_name="获取保单详情")
async def get_policy_by_code(
    policy_code: str = Path(..., description="保单编码")
) -> Dict[str, Any]:
    """根据编码获取单个保单"""
    policy = controller.service.get_policy_by_code(policy_code)
    
    if not policy:
        return ApiResponse.error(f"未找到编码为 {policy_code} 的保单", 404)
    
    return ApiResponse.success(data=policy) 