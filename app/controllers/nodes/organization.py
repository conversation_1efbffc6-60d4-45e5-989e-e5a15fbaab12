"""
组织节点管理控制器

提供各类组织节点的统一管理API接口
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, Dict, Any, List
from enum import Enum

from app.services.node_service.insurance_company_service import InsuranceCompanyService
from app.services.node_service.insurance_brokerage_company_service import InsuranceBrokerageCompanyService
from app.services.node_service.insurance_sales_channel_service import InsuranceSalesChannelService
from app.services.node_service.policy_company_service import PolicyCompanyService
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/nodes/organizations", tags=["组织节点管理"])


class OrganizationType(str, Enum):
    """组织类型枚举"""
    INSURANCE_COMPANY = "insurance-companies"           # 保险公司
    BROKERAGE_COMPANY = "brokerage-companies"          # 经纪公司
    SALES_CHANNEL = "sales-channels"                   # 销售渠道
    POLICY_COMPANY = "policy-companies"                # 保单公司


# 服务实例映射
SERVICE_MAP = {
    OrganizationType.INSURANCE_COMPANY: InsuranceCompanyService,
    OrganizationType.BROKERAGE_COMPANY: InsuranceBrokerageCompanyService,
    OrganizationType.SALES_CHANNEL: InsuranceSalesChannelService,
    OrganizationType.POLICY_COMPANY: PolicyCompanyService
}


@router.get("/{org_type}/", summary="搜索指定类型的组织")
async def search_organizations(
    org_type: OrganizationType = Path(..., description="组织类型"),
    name: Optional[str] = Query(None, description="组织名称关键词"),
    code: Optional[str] = Query(None, description="组织编码关键词"),
    province: Optional[str] = Query(None, description="省份"),
    city: Optional[str] = Query(None, description="城市"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """
    搜索指定类型的组织
    
    Args:
        org_type: 组织类型
        name: 组织名称关键词
        code: 组织编码关键词
        province: 省份
        city: 城市
        limit: 返回记录数限制
        
    Returns:
        Dict[str, Any]: 包含组织列表和元数据的响应
    """
    try:
        service_class = SERVICE_MAP.get(org_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的组织类型: {org_type}")
        
        service = service_class()
        
        # 根据不同的组织类型调用对应的搜索方法
        if org_type == OrganizationType.INSURANCE_COMPANY:
            organizations = service.search_companies(
                name_filter=name,
                code_filter=code,
                province_filter=province,
                city_filter=city,
                limit=limit
            )
        elif org_type == OrganizationType.BROKERAGE_COMPANY:
            organizations = service.search_companies(
                name_filter=name,
                code_filter=code,
                province_filter=province,
                city_filter=city,
                limit=limit
            )
        elif org_type == OrganizationType.SALES_CHANNEL:
            organizations = service.search_channels(
                name_filter=name,
                limit=limit
            )
        elif org_type == OrganizationType.POLICY_COMPANY:
            organizations = service.search_companies(
                name_filter=name,
                credit_code_filter=code,
                limit=limit
            )
        
        return {
            "success": True,
            "data": organizations,
            "total": len(organizations),
            "organization_type": org_type,
            "message": "搜索完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索{org_type}组织失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/{org_type}/statistics", summary="获取组织统计信息")
async def get_organization_statistics(
    org_type: OrganizationType = Path(..., description="组织类型")
) -> Dict[str, Any]:
    """
    获取指定类型组织的统计信息
    
    Args:
        org_type: 组织类型
        
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        service_class = SERVICE_MAP.get(org_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的组织类型: {org_type}")
        
        service = service_class()
        stats = service.get_statistics()
        
        return {
            "success": True,
            "data": stats,
            "organization_type": org_type,
            "message": "统计信息获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{org_type}组织统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/{org_type}/import", summary="导入组织数据")
async def import_organizations(
    org_type: OrganizationType = Path(..., description="组织类型"),
    clear_existing: bool = Query(False, description="是否清空现有数据")
) -> Dict[str, Any]:
    """
    从数据源导入指定类型的组织数据
    
    Args:
        org_type: 组织类型
        clear_existing: 是否清空现有数据
        
    Returns:
        Dict[str, Any]: 导入结果
    """
    try:
        service_class = SERVICE_MAP.get(org_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的组织类型: {org_type}")
        
        service = service_class()
        
        # 如果需要清空现有数据
        if clear_existing:
            if org_type == OrganizationType.INSURANCE_COMPANY:
                success = service.delete_all_companies()
            elif org_type == OrganizationType.BROKERAGE_COMPANY:
                success = service.delete_all_companies()
            elif org_type == OrganizationType.SALES_CHANNEL:
                success = service.delete_all_channels()
            elif org_type == OrganizationType.POLICY_COMPANY:
                success = service.delete_all_companies()
            
            if not success:
                raise HTTPException(status_code=500, detail="清空现有数据失败")
        
        # 执行数据导入
        if org_type == OrganizationType.INSURANCE_COMPANY:
            result = service.extract_and_store_companies()
        elif org_type == OrganizationType.BROKERAGE_COMPANY:
            result = service.extract_and_store_companies()
        elif org_type == OrganizationType.SALES_CHANNEL:
            result = service.extract_and_store_channels()
        elif org_type == OrganizationType.POLICY_COMPANY:
            result = service.extract_and_store_companies()
        
        if result.get("success", True):
            return {
                "success": True,
                "data": result,
                "organization_type": org_type,
                "message": f"{org_type}组织数据导入成功"
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail=f"数据导入失败: {result.get('error', '未知错误')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入{org_type}组织数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.delete("/{org_type}/", summary="删除所有指定类型的组织数据")
async def delete_all_organizations(
    org_type: OrganizationType = Path(..., description="组织类型")
) -> Dict[str, Any]:
    """
    删除所有指定类型的组织数据
    
    Args:
        org_type: 组织类型
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        service_class = SERVICE_MAP.get(org_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的组织类型: {org_type}")
        
        service = service_class()
        
        # 根据组织类型调用对应的删除方法
        if org_type == OrganizationType.INSURANCE_COMPANY:
            success = service.delete_all_companies()
        elif org_type == OrganizationType.BROKERAGE_COMPANY:
            success = service.delete_all_companies()
        elif org_type == OrganizationType.SALES_CHANNEL:
            success = service.delete_all_channels()
        elif org_type == OrganizationType.POLICY_COMPANY:
            success = service.delete_all_companies()
        
        if success:
            return {
                "success": True,
                "organization_type": org_type,
                "message": f"所有{org_type}组织数据已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除{org_type}组织数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/{org_type}/{code}", summary="根据编码获取组织")
async def get_organization_by_code(
    org_type: OrganizationType = Path(..., description="组织类型"),
    code: str = Path(..., description="组织编码")
) -> Dict[str, Any]:
    """
    根据编码获取单个组织
    
    Args:
        org_type: 组织类型
        code: 组织编码
        
    Returns:
        Dict[str, Any]: 组织详细信息
    """
    try:
        service_class = SERVICE_MAP.get(org_type)
        if not service_class:
            raise HTTPException(status_code=400, detail=f"不支持的组织类型: {org_type}")
        
        service = service_class()
        
        # 根据不同的组织类型调用对应的获取方法
        if org_type == OrganizationType.INSURANCE_COMPANY:
            organization = service.get_company_by_code(code)
        elif org_type == OrganizationType.BROKERAGE_COMPANY:
            organization = service.get_company_by_code(code)
        elif org_type == OrganizationType.SALES_CHANNEL:
            organization = service.get_channel_by_code(code)
        elif org_type == OrganizationType.POLICY_COMPANY:
            # 策公司服务没有get_by_code方法，使用搜索方法
            results = service.search_companies(credit_code_filter=code, limit=1)
            organization = results[0] if results else None
        
        if organization:
            return {
                "success": True,
                "data": organization,
                "organization_type": org_type,
                "message": "获取成功"
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到编码为 {code} 的{org_type}组织")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{org_type}组织失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/", summary="获取所有组织类型的统计概览")
async def get_all_organizations_overview() -> Dict[str, Any]:
    """
    获取所有组织类型的统计概览
    
    Returns:
        Dict[str, Any]: 所有组织类型的统计信息
    """
    try:
        overview = {}
        
        for org_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_statistics()
                overview[org_type] = stats
            except Exception as e:
                logger.warning(f"获取{org_type}统计失败: {str(e)}")
                overview[org_type] = {"error": str(e)}
        
        return {
            "success": True,
            "data": overview,
            "message": "组织统计概览获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取组织统计概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取概览失败: {str(e)}")


@router.get("/health", summary="组织服务健康检查")
async def health_check() -> Dict[str, Any]:
    """
    组织节点服务健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        health_status = {}
        overall_healthy = True
        
        for org_type, service_class in SERVICE_MAP.items():
            try:
                service = service_class()
                stats = service.get_statistics()
                health_status[org_type] = {
                    "status": "healthy",
                    "total_count": stats.get("total_count", 0)
                }
            except Exception as e:
                health_status[org_type] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "success": overall_healthy,
            "service": "OrganizationNodeService",
            "status": "healthy" if overall_healthy else "partially_healthy",
            "details": health_status,
            "message": "组织服务健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"组织服务健康检查失败: {str(e)}")
        return {
            "success": False,
            "service": "OrganizationNodeService",
            "status": "unhealthy",
            "error": str(e),
            "message": "组织服务异常"
        } 