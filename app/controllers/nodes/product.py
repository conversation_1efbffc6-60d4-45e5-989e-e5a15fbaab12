"""
产品节点管理控制器

提供保险产品节点的标准化REST API接口
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import Optional, Dict, Any, List

from app.services.node_service.insurance_product_service import InsuranceProductService
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/nodes/products", tags=["产品节点管理"])

# 服务实例
product_service = InsuranceProductService()


@router.get("/", summary="搜索产品节点")
async def search_products(
    product_code: Optional[str] = Query(None, description="产品编码关键词"),
    product_name: Optional[str] = Query(None, description="产品名称关键词"),
    insurance_code: Optional[str] = Query(None, description="保险公司编码"),
    product_type: Optional[str] = Query(None, description="产品类型"),
    status: Optional[str] = Query(None, description="产品状态"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """
    搜索产品节点
    
    Returns:
        Dict[str, Any]: 包含产品列表和元数据的响应
    """
    try:
        logger.info(f"搜索产品: product_code={product_code}, product_name={product_name}")
        
        products = product_service.search_products(
            product_code_filter=product_code,
            product_name_filter=product_name,
            insurance_code_filter=insurance_code,
            product_type_filter=product_type,
            status_filter=status,
            limit=limit
        )
        
        return {
            "success": True,
            "data": products,
            "total": len(products),
            "message": "搜索完成"
        }
        
    except Exception as e:
        logger.error(f"搜索产品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/statistics", summary="获取产品统计信息")
async def get_statistics() -> Dict[str, Any]:
    """
    获取产品节点统计信息
    
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        stats = product_service.get_statistics()
        
        return {
            "success": True,
            "data": stats,
            "message": "统计信息获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/{product_code}", summary="根据编码获取产品")
async def get_product_by_code(
    product_code: str = Path(..., description="产品编码")
) -> Dict[str, Any]:
    """
    根据编码获取单个产品
    
    Args:
        product_code: 产品编码
        
    Returns:
        Dict[str, Any]: 产品详细信息
    """
    try:
        product = product_service.get_product_by_code(product_code)
        
        if product:
            return {
                "success": True,
                "data": product,
                "message": "获取成功"
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到编码为 {product_code} 的产品")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取产品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.post("/import", summary="导入产品数据")
async def import_products(
    clear_existing: bool = Query(False, description="是否清空现有数据"),
    limit: Optional[int] = Query(None, description="导入记录数限制"),
    where_conditions: Optional[str] = Query(None, description="额外的WHERE条件"),
    custom_day: Optional[str] = Query(None, description="自定义日期(YYYY-MM-DD)")
) -> Dict[str, Any]:
    """
    从数据源导入产品数据
    
    Args:
        clear_existing: 是否清空现有数据
        limit: 导入记录数限制
        where_conditions: 额外的WHERE条件
        custom_day: 自定义日期
        
    Returns:
        Dict[str, Any]: 导入结果
    """
    try:
        logger.info("开始导入产品数据")
        
        # 如果需要清空现有数据
        if clear_existing:
            success = product_service.delete_all_products()
            if not success:
                raise HTTPException(status_code=500, detail="清空现有数据失败")
        
        # 执行数据导入
        result = product_service.extract_and_store_products(
            limit=limit,
            where_conditions=where_conditions,
            custom_day=custom_day
        )
        
        if result.get("success", True):
            logger.info(f"产品数据导入完成: {result}")
            return {
                "success": True,
                "data": result,
                "message": "产品数据导入成功"
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail=f"数据导入失败: {result.get('error', '未知错误')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入产品数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.delete("/", summary="删除所有产品数据")
async def delete_all_products() -> Dict[str, Any]:
    """
    删除所有产品数据
    
    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        logger.info("开始删除所有产品数据")
        
        success = product_service.delete_all_products()
        
        if success:
            logger.info("所有产品数据删除完成")
            return {
                "success": True,
                "message": "所有产品数据已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除产品数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """
    产品节点服务健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        # 简单的统计查询来验证服务状态
        stats = product_service.get_statistics()
        total_count = stats.get("total_count", 0)
        
        return {
            "success": True,
            "service": "ProductNodeService",
            "status": "healthy",
            "total_products": total_count,
            "message": "服务运行正常"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "success": False,
            "service": "ProductNodeService", 
            "status": "unhealthy",
            "error": str(e),
            "message": "服务异常"
        } 