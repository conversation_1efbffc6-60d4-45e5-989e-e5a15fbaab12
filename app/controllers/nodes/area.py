"""
区域节点管理控制器

提供区域节点的标准化REST API接口
"""

from fastapi import APIRouter, Query, Path
from typing import Optional, Dict, Any
import asyncio

from app.services.node_service.area_service import AreaService
from app.utils.logger import get_logger
from app.utils.controller_base import ApiResponse, ControllerBase, api_handler

router = APIRouter(prefix="/api/v1/nodes/areas", tags=["区域节点管理"])

# 控制器实例
controller = ControllerBase(AreaService)


@router.get("/", summary="搜索区域节点")
@api_handler(operation_name="搜索区域")
async def search_areas(
    name: Optional[str] = Query(None, description="区域名称关键词"),
    code: Optional[str] = Query(None, description="区域编码关键词"),
    level: Optional[int] = Query(None, ge=0, le=10, description="区域层级"),
    parent_code: Optional[str] = Query(None, description="父级区域编码"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制")
) -> Dict[str, Any]:
    """搜索区域节点"""
    def run_search():
        return controller.service.search_areas(
            name_filter=name,
            level=level,
            parent_code=parent_code,
            limit=limit
        )
    
    loop = asyncio.get_event_loop()
    areas = await loop.run_in_executor(None, run_search)
    
    return ApiResponse.success(data=areas, total=len(areas))


@router.get("/statistics", summary="获取区域统计信息")
@api_handler(operation_name="获取区域统计")
async def get_statistics() -> Dict[str, Any]:
    """获取区域节点统计信息"""
    def run_stats():
        return controller.service.get_statistics()
    
    loop = asyncio.get_event_loop()
    stats = await loop.run_in_executor(None, run_stats)
    
    return ApiResponse.success(data=stats)


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """区域服务健康检查"""
    try:
        def run_health_check():
            stats = controller.service.get_statistics()
            return stats.get('total_count', 0) >= 0
        
        loop = asyncio.get_event_loop()
        is_healthy = await loop.run_in_executor(None, run_health_check)
        
        return ApiResponse.success(
            data={
                "service": "area_service",
                "status": "healthy" if is_healthy else "unhealthy"
            },
            message="区域服务运行正常" if is_healthy else "区域服务异常"
        )
        
    except Exception as e:
        return ApiResponse.error(
            message=f"健康检查失败: {str(e)}",
            status_code=503
        )


@router.get("/{code}", summary="根据编码获取区域")
@api_handler(operation_name="获取区域详情")
async def get_area_by_code(
    code: str = Path(..., description="区域编码")
) -> Dict[str, Any]:
    """根据编码获取单个区域"""
    def run_get():
        return controller.service.get_area_by_code(code)
    
    loop = asyncio.get_event_loop()
    area = await loop.run_in_executor(None, run_get)
    
    if not area:
        return ApiResponse.error(f"未找到编码为 {code} 的区域", 404)
    
    return ApiResponse.success(data=area)


@router.post("/import", summary="导入区域数据")
@api_handler(operation_name="导入区域数据")
async def import_areas(
    clear_existing: bool = Query(False, description="是否清空现有数据")
) -> Dict[str, Any]:
    """从数据源导入区域数据"""
    def run_import():
        if clear_existing:
            success = controller.service.delete_all_areas()
            if not success:
                raise Exception("清空现有数据失败")
        
        return controller.service.extract_and_store_areas()
    
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(None, run_import)
    
    return ApiResponse.success(data=result, message="区域数据导入成功")


@router.delete("/", summary="删除所有区域数据")
@api_handler(operation_name="删除区域数据")
async def delete_all_areas() -> Dict[str, Any]:
    """删除所有区域数据"""
    def run_delete():
        return controller.service.delete_all_areas()
    
    loop = asyncio.get_event_loop()
    success = await loop.run_in_executor(None, run_delete)
    
    if not success:
        return ApiResponse.error("删除失败")
    
    return ApiResponse.success(message="所有区域数据删除成功") 