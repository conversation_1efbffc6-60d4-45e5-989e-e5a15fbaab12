"""
Impala数据提取器
"""

import pandas as pd
from typing import Optional, Dict, Any, List
from datetime import datetime

from impala.dbapi import connect
from impala.util import as_pandas

from app.config import ImpalaConfig, load_impala_config
from app.utils.logger import get_logger

logger = get_logger(__name__)


class ImpalaDataExtractor:
    """Impala数据提取器，处理与Impala数据库的连接和数据提取"""
    
    def __init__(self, config: Optional[ImpalaConfig] = None):
        """
        初始化Impala数据提取器
        
        Args:
            config: ImpalaConfig对象，如果不提供将从环境变量加载
        """
        self.config = config if config else load_impala_config()
        self.connection = None
        self.cursor = None
    
    def connect(self) -> bool:
        """
        建立与Impala数据库的连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 构建连接参数
            conn_kwargs = {
                "host": self.config.host,
                "port": self.config.port,
                "database": self.config.database,
                "timeout": self.config.query_timeout
            }
            
            # 添加认证信息
            if self.config.auth_config.auth_type == "KERBEROS":
                conn_kwargs["auth_mechanism"] = "GSSAPI"
                if self.config.auth_config.principal:
                    conn_kwargs["kerberos_service_name"] = "impala"
                    
            elif self.config.auth_config.auth_type == "LDAP":
                conn_kwargs["auth_mechanism"] = "LDAP"
                conn_kwargs["user"] = self.config.auth_config.username
                conn_kwargs["password"] = self.config.auth_config.password
                
            # SSL配置
            if self.config.auth_config.use_ssl:
                conn_kwargs["use_ssl"] = True
                if self.config.auth_config.ssl_truststore:
                    conn_kwargs["ca_cert"] = self.config.auth_config.ssl_truststore
                    
            # 创建连接
            self.connection = connect(**conn_kwargs)
            self.cursor = self.connection.cursor()
            logger.info(f"成功连接到 Impala 数据库: {self.config.host}:{self.config.port}/{self.config.database}")
            return True
            
        except Exception as e:
            logger.error(f"连接 Impala 数据库失败: {str(e)}")
            return False
    
    def disconnect(self):
        """关闭与Impala数据库的连接"""
        if self.cursor:
            self.cursor.close()
            self.cursor = None
            
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("已关闭 Impala 数据库连接")
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[tuple]:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数，用于参数化查询
            
        Returns:
            list: 查询结果的列表，每个元素是一个元组
        """
        if not self.connection or not self.cursor:
            if not self.connect():
                return []
        
        try:
            logger.debug(f"执行查询: {query}")
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            results = self.cursor.fetchall()
            logger.info(f"查询完成，返回 {len(results)} 条记录")
            return results
            
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            return []
    
    def query_to_dataframe(self, query: str, params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        执行SQL查询并将结果转换为DataFrame
        
        Args:
            query: SQL查询语句
            params: 查询参数，用于参数化查询
            
        Returns:
            pandas.DataFrame: 查询结果的DataFrame
        """
        if not self.connection or not self.cursor:
            if not self.connect():
                return pd.DataFrame()
        
        try:
            logger.debug(f"执行查询并转换为DataFrame: {query}")
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            df = as_pandas(self.cursor)
            logger.info(f"查询完成，返回 {len(df)} 行数据")
            return df
            
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            return pd.DataFrame()
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.connect():
                # 执行简单查询测试连接
                test_query = "SELECT 1 as test_value"
                result = self.execute_query(test_query)
                self.disconnect()
                return len(result) > 0
            return False
        except Exception as e:
            logger.error(f"测试连接失败: {str(e)}")
            return False
    
    def extract_data(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[pd.DataFrame]:
        """
        通用数据提取方法
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Optional[pd.DataFrame]: 查询结果DataFrame，失败时返回None
        """
        try:
            df = self.query_to_dataframe(query, params)
            return df if not df.empty else None
        except Exception as e:
            logger.error(f"数据提取失败: {str(e)}")
            return None
    
    @classmethod
    def from_config(cls, config: ImpalaConfig):
        """从配置创建ImpalaDataExtractor实例"""
        return cls(config) 