"""
数据转换服务

本文件已重构为使用新的节点转换器架构，具体实现移至node_transformers子模块
"""

import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date

from app.services.data_storage.neo4j_storage import GraphEntity
from app.models.nodes import (
    OrganizationNode, InsuranceProductNode, InsuranceCompanyNode, 
    InsuranceBrokerageCompanyNode, PolicyCompanyNode, InsuranceClaimsNode
)
from app.models.nodes import InsuredPersonNode, PolicyNode
from app.utils.logger import get_logger
from .node_transformers import (
    CompanyTransformer,
    InsuranceProductTransformer,
    InsuranceCompanyTransformer,
    InsuranceBrokerageCompanyTransformer,
    PolicyCompanyTransformer,
    PolicyTransformer,
    InsuranceClaimsTransformer,
    InsuredPersonTransformer
)

logger = get_logger(__name__)


class DataTransformService:
    """数据转换服务，负责将原始数据转换为图模型"""
    
    def __init__(self):
        # 初始化转换器
        self.company_transformer = CompanyTransformer()
        self.insurance_product_transformer = InsuranceProductTransformer()
        self.insurance_company_transformer = InsuranceCompanyTransformer()
        self.insurance_brokerage_company_transformer = InsuranceBrokerageCompanyTransformer()
        self.policy_company_transformer = PolicyCompanyTransformer()
        self.policy_transformer = PolicyTransformer()
        self.insurance_claims_transformer = InsuranceClaimsTransformer()
        self.insured_person_transformer = InsuredPersonTransformer()

    def transform_company_data(self, df: pd.DataFrame) -> List[InsuranceCompanyNode]:
        """
        转换企业数据
        
        Args:
            df: 企业数据DataFrame
            
        Returns:
            List[InsuranceCompanyNode]: 转换后的企业节点列表
        """
        return self.company_transformer.transform_data(df)
    
    def transform_product_data(self, df: pd.DataFrame) -> List[InsuranceProductNode]:
        """
        转换保险产品数据
        
        Args:
            df: 保险产品数据DataFrame
            
        Returns:
            List[InsuranceProductNode]: 转换后的保险产品节点列表
        """
        return self.insurance_product_transformer.transform_data(df)
    
    def transform_insurance_company_data(self, df: pd.DataFrame) -> List[InsuranceCompanyNode]:
        """
        转换保险公司数据
        
        Args:
            df: 保险公司数据DataFrame
            
        Returns:
            List[InsuranceCompanyNode]: 转换后的保险公司节点列表
        """
        return self.insurance_company_transformer.transform_data(df)
    
    def transform_insurance_brokerage_company_data(self, df: pd.DataFrame) -> List[InsuranceBrokerageCompanyNode]:
        """
        转换保险经纪公司数据
        
        Args:
            df: 保险经纪公司数据DataFrame
            
        Returns:
            List[InsuranceBrokerageCompanyNode]: 转换后的保险经纪公司节点列表
        """
        return self.insurance_brokerage_company_transformer.transform_data(df)
    
    def transform_policy_company_data(self, df: pd.DataFrame) -> List[PolicyCompanyNode]:
        """
        转换政策公司数据
        
        Args:
            df: 政策公司数据DataFrame
            
        Returns:
            List[PolicyCompanyNode]: 转换后的政策公司节点列表
        """
        return self.policy_company_transformer.transform_data(df)
    
    def transform_policy_data(self, df: pd.DataFrame) -> List[PolicyNode]:
        """
        转换保单数据
        
        Args:
            df: 保单数据DataFrame
            
        Returns:
            List[PolicyNode]: 转换后的保单节点列表
        """
        return self.policy_transformer.transform_data(df)
    
    def transform_insurance_claims_data(self, df: pd.DataFrame) -> List[InsuranceClaimsNode]:
        """
        转换保险理赔数据
        
        Args:
            df: 保险理赔数据DataFrame
            
        Returns:
            List[InsuranceClaimsNode]: 转换后的保险理赔节点列表
        """
        return self.insurance_claims_transformer.transform_data(df)
    
    def transform_insured_person_data(self, df: pd.DataFrame) -> List[InsuredPersonNode]:
        """
        转换被保险人数据
        
        Args:
            df: 被保险人数据DataFrame
            
        Returns:
            List[InsuredPersonNode]: 转换后的被保险人节点列表
        """
        return self.insured_person_transformer.transform_data(df)
    
    def create_graph_entity(self, 
                           companies: List[InsuranceCompanyNode] = None,
                           products: List[InsuranceProductNode] = None,
                           insurance_companies: List[InsuranceCompanyNode] = None,
                           insurance_brokerage_companies: List[InsuranceBrokerageCompanyNode] = None,
                           policy_companies: List[PolicyCompanyNode] = None,
                           policies: List[PolicyNode] = None,
                           insurance_claims: List[InsuranceClaimsNode] = None) -> GraphEntity:
        """
        创建图实体
        
        Args:
            companies: 企业节点列表
            products: 保险产品节点列表
            insurance_companies: 保险公司节点列表
            insurance_brokerage_companies: 保险经纪公司节点列表
            policy_companies: 保单企业节点列表
            policies: 保单节点列表
            insurance_claims: 保险理赔节点列表
            
        Returns:
            GraphEntity: 图实体
        """
        entity = GraphEntity()
        
        # 添加节点
        if companies:
            entity.nodes.extend(companies)
        if products:
            entity.nodes.extend(products)
        if insurance_companies:
            entity.nodes.extend(insurance_companies)
        if insurance_brokerage_companies:
            entity.nodes.extend(insurance_brokerage_companies)
        if policy_companies:
            entity.nodes.extend(policy_companies)
        if policies:
            entity.nodes.extend(policies)
        if insurance_claims:
            entity.nodes.extend(insurance_claims)
        
        logger.info(f"创建图实体: 节点 {len(entity.nodes)} 个, 关系 {len(entity.relationships)} 个")
        
        return entity 