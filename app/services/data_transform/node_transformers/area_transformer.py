"""
区域数据转换器

将Hive原始数据转换为区域业务模型
"""

import pandas as pd
from typing import List, Any, Optional

from app.services.data_transform.node_transformers.base_transformer import BaseNodeTransformer
from app.models.nodes.area_node import AreaNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AreaTransformer(BaseNodeTransformer):
    """区域数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[AreaNode]:
        """
        将数据行映射为区域节点
        
        Args:
            row: 单行区域数据
            
        Returns:
            Optional[AreaNode]: 转换后的区域对象
        """
        try:
            return self._transform_single_area(row)
        except Exception as e:
            self.logger.error(f"转换区域记录失败: {str(e)}, 数据: {row.to_dict()}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[AreaNode]:
        """
        转换区域数据
        
        Args:
            df: 包含区域信息的DataFrame
            
        Returns:
            List[AreaNode]: 转换后的区域列表
        """
        try:
            if df.empty:
                logger.warning("输入的区域数据为空")
                return []
            
            areas = []
            for _, row in df.iterrows():
                area = self.map_to_node(row)
                if area:
                    areas.append(area)
            
            logger.info(f"成功转换 {len(areas)} 个区域记录")
            return areas
            
        except Exception as e:
            logger.error(f"区域数据转换失败: {str(e)}")
            return []
    
    def _transform_single_area(self, row: pd.Series) -> AreaNode:
        """
        转换单个区域记录
        
        Args:
            row: 单行区域数据
            
        Returns:
            AreaNode: 转换后的区域对象
        """
        # 提取基本字段（SQL查询中已将city_code别名为code，city_name别名为name）
        code = self._get_string_value(row, 'code')
        name = self._get_string_value(row, 'name')
        
        # 验证必填字段
        if not code:
            raise ValueError("区域编码不能为空")
        if not name:
            raise ValueError("区域名称不能为空")
        
        # 提取可选字段
        parent_code = self._get_string_value(row, 'city_parent_code')
        level = self._get_int_value(row, 'city_level')
        
        return AreaNode(
            code=code,
            name=name,
            parent_code=parent_code,
            level=level
        )
    
 