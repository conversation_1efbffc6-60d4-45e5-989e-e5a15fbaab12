"""
BZN标准职业节点转换器

负责BZN标准职业数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.bzn_standard_occupation_node import BznStandardOccupationNode
from .base_transformer import BaseNodeTransformer


class BznStandardOccupationTransformer(BaseNodeTransformer):
    """BZN标准职业数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[BznStandardOccupationNode]:
        """
        将数据行映射为BZN标准职业节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[BznStandardOccupationNode]: BZN标准职业节点
        """
        try:
            bzn_work_name = self._get_string_value(row, 'bzn_work_name')
            
            # 必须要有职业名称
            if not bzn_work_name:
                return None
            
            occupation = BznStandardOccupationNode(
                name=bzn_work_name,
                code=bzn_work_name,  # code字段设置为bzn_work_name
                industry_large=self._get_string_value(row, 'bzn_industry_large'),
                industry_medium=self._get_string_value(row, 'bzn_industry_medium'),
                industry_small=self._get_string_value(row, 'bzn_industry_small'),
                work_type_level=self._get_string_value(row, 'bzn_work_type_level'),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return occupation
            
        except Exception as e:
            self.logger.error(f"映射BZN标准职业节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[BznStandardOccupationNode]:
        """
        转换BZN标准职业数据
        
        Args:
            df: BZN标准职业数据DataFrame
            
        Returns:
            List[BznStandardOccupationNode]: 转换后的BZN标准职业节点列表
        """
        self.logger.info(f"开始转换BZN标准职业数据，共 {len(df)} 条记录")
        
        occupations = []
        
        for _, row in df.iterrows():
            try:
                occupation = self.map_to_node(row)
                if occupation:
                    occupations.append(occupation)
                    
            except Exception as e:
                self.logger.error(f"转换BZN标准职业数据失败: {row.get('bzn_work_name', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"BZN标准职业数据转换完成: {len(occupations)} 个职业")
        
        return occupations 