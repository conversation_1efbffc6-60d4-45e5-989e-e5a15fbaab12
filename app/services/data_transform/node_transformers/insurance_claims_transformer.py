"""
保险理赔节点转换器

负责保险理赔数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insurance_claims_node import InsuranceClaimsNode
from .base_transformer import BaseNodeTransformer


class InsuranceClaimsTransformer(BaseNodeTransformer):
    """保险理赔数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceClaimsNode]:
        """
        将数据行映射为保险理赔节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceClaimsNode]: 保险理赔节点
        """
        try:
            # 案件号/报案号是必须的
            case_no = self._get_string_value(row, 'case_no')
            if not case_no:
                return None
            
            # 验证案件号格式
            if not self._validate_business_id(case_no, "insurance_claims"):
                self.logger.warning(f"案件号格式不正确: {case_no}")
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "InsuranceClaims"
            
            claims = InsuranceClaimsNode(
                # 基础标识字段
                code=case_no,
                name=case_no,
                case_no=case_no,
                policy_code=self._get_string_value(row, 'policy_code'),
                
                # 被保险人信息
                insured_user_id=self._get_string_value(row, 'insured_user_id'),
                insured_user_cert_no=self._get_string_value(row, 'insured_user_cert_no'),
                insured_user_name=self._get_string_value(row, 'insured_user_name'),
                
                # 投保公司信息
                insure_code=self._get_string_value(row, 'insure_code'),
                insure_name=self._get_string_value(row, 'insure_name'),
                
                # 被保公司信息
                insured_code=self._get_string_value(row, 'insured_code'),
                insured_name=self._get_string_value(row, 'insured_name'),
                
                # 保险公司信息
                insurance_code=self._get_string_value(row, 'insurance_code'),
                insurance_name=self._get_string_value(row, 'insurance_name'),
                
                # 报案人信息
                report_name=self._get_string_value(row, 'report_name'),
                report_mobile=self._get_string_value(row, 'report_mobile'),
                report_is_agent=self._get_string_value(row, 'report_is_agent'),
                
                # 时间信息
                report_date=self._get_datetime_value(row, 'report_date'),
                risk_date=self._get_datetime_value(row, 'risk_date'),
                register_date=self._get_datetime_value(row, 'register_date'),
                case_close_date=self._get_datetime_value(row, 'case_close_date'),
                
                # 工种信息
                work_type_name=self._get_string_value(row, 'work_type_name'),
                work_type_level=self._get_string_value(row, 'work_type_level'),
                
                # 出险地理位置信息
                risk_province_code=self._get_string_value(row, 'risk_province_code'),
                risk_province=self._get_string_value(row, 'risk_province'),
                risk_city_code=self._get_string_value(row, 'risk_city_code'),
                risk_city=self._get_string_value(row, 'risk_city'),
                risk_district_code=self._get_string_value(row, 'risk_district_code'),
                risk_district=self._get_string_value(row, 'risk_district'),
                
                # 出险场景信息
                risk_scene=self._get_string_value(row, 'risk_scene'),
                risk_scene_one=self._get_string_value(row, 'risk_scene_one'),
                risk_scene_two=self._get_string_value(row, 'risk_scene_two'),
                risk_scene_three=self._get_string_value(row, 'risk_scene_three'),
                
                # 伤情信息
                injured_category=self._get_string_value(row, 'injured_category'),
                disable_level=self._get_string_value(row, 'disable_level'),
                injured_part=self._get_string_value(row, 'injured_part'),
                visit_hospitals=self._get_string_value(row, 'visit_hospitals'),
                closed_injured_category=self._get_string_value(row, 'closed_injured_category'),
                closed_disable_level=self._get_string_value(row, 'closed_disable_level'),
                
                # 案件状态和金额
                case_status=self._get_string_value(row, 'case_status'),
                compensate_result=self._get_string_value(row, 'compensate_result'),
                register_amt=self._get_float_value(row, 'register_amt'),
                compensate_amt=self._get_float_value(row, 'compensate_amt'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return claims
            
        except Exception as e:
            self.logger.error(f"转换保险理赔数据失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceClaimsNode]:
        """
        批量转换保险理赔数据
        
        Args:
            df: 保险理赔数据DataFrame
            
        Returns:
            List[InsuranceClaimsNode]: 转换后的保险理赔节点列表
        """
        logger = self.logger
        logger.info(f"开始转换保险理赔数据，共 {len(df)} 条记录")
        
        claims_list = []
        failed_count = 0
        
        for index, row in df.iterrows():
            try:
                claims = self.map_to_node(row)
                if claims:
                    claims_list.append(claims)
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"转换第 {index} 行数据失败: {str(e)}")
                failed_count += 1
        
        logger.info(f"保险理赔数据转换完成: {len(claims_list)} 个理赔记录，失败 {failed_count} 个")
        return claims_list 