"""
被保险人节点转换器

负责被保险人数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insured_person_node import InsuredPersonNode
from .base_transformer import BaseNodeTransformer


class InsuredPersonTransformer(BaseNodeTransformer):
    """被保险人数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuredPersonNode]:
        """
        将数据行映射为被保险人节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuredPersonNode]: 被保险人节点
        """
        try:
            # 姓名是必须的
            name = self._get_string_value(row, 'name')
            if not name:
                return None
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "Policy"
            
            insured_person = InsuredPersonNode(
                # 基础字段（从SQL查询映射而来）
                name=name,  # 来自 SQL 查询的 name 字段
                code=self._get_string_value(row, 'id_card_number'), # 身份证号码作为code
                
                # 个人信息
                gender=self._get_string_value(row, 'gender'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                data_source_id=self._get_string_value(row, 'data_source_id'),
                data_source_organization_name=self._get_string_value(row, 'data_source_organization_name'),
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return insured_person
            
        except Exception as e:
            self.logger.error(f"转换被保险人数据失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuredPersonNode]:
        """
        批量转换被保险人数据
        
        Args:
            df: 被保险人数据DataFrame
            
        Returns:
            List[InsuredPersonNode]: 转换后的被保险人节点列表
        """
        logger = self.logger
        logger.info(f"开始转换被保险人数据，共 {len(df)} 条记录")
        
        insured_person_list = []
        failed_count = 0
        
        for index, row in df.iterrows():
            try:
                insured_person = self.map_to_node(row)
                if insured_person:
                    insured_person_list.append(insured_person)
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"转换第 {index} 行数据失败: {str(e)}")
                failed_count += 1
        
        logger.info(f"被保险人数据转换完成: {len(insured_person_list)} 个被保险人记录，失败 {failed_count} 个")
        return insured_person_list 