"""
保险产品节点转换器

负责保险产品数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insurance_product_node import InsuranceProductNode
from .base_transformer import BaseNodeTransformer


class InsuranceProductTransformer(BaseNodeTransformer):
    """保险产品数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceProductNode]:
        """
        将数据行映射为保险产品节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceProductNode]: 保险产品节点
        """
        try:
            code = self._get_string_value(row, 'product_code')
            name = self._get_string_value(row, 'product_name')
            
            if not code or not name:
                return None
            
            # 验证产品代码格式
            if not self._validate_business_id(code, "product"):
                self.logger.warning(f"产品代码格式不正确: {code}")
            
            # 映射业务来源 - 根据规范只支持online/offline
            business_source = self._get_business_source(row)
            
            insurance_product = InsuranceProductNode(
                # 基础映射
                code=code,
                name=name,
                
                # 基础产品信息
                product_id=self._get_string_value(row, 'product_id'),
                product_name=name,
                short_name=self._get_string_value(row, 'product_short_name'),
                product_code=code,
                product_desc=self._get_string_value(row, 'product_desc'),
                
                # 保险公司信息
                insurance_name=self._get_string_value(row, 'insurance_name'),
                insurance_code=self._get_string_value(row, 'insurance_code'),
                insurance_unified_social_credit_code=self._get_string_value(row, 'insurance_unified_social_credit_code'),
                
                # 产品分类信息
                one_level_pdt_cate=self._get_string_value(row, 'one_level_pdt_cate'),
                two_level_pdt_cate=self._get_string_value(row, 'two_level_pdt_cate'),
                product_type=self._get_string_value(row, 'product_type'),
                business_line=self._get_string_value(row, 'business_line'),
                sale_category=self._get_string_value(row, 'sale_category'),
                
                # 系统信息
                system_source=self._get_string_value(row, 'system_source'),
                business_source=business_source,
                
                # 时间戳
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return insurance_product
            
        except Exception as e:
            self.logger.error(f"映射保险产品节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceProductNode]:
        """
        转换保险产品数据
        
        Args:
            df: 保险产品数据DataFrame
            
        Returns:
            List[InsuranceProductNode]: 转换后的保险产品节点列表
        """
        self.logger.info(f"开始转换保险产品数据，共 {len(df)} 条记录")
        
        insurance_products = []
        
        for _, row in df.iterrows():
            try:
                insurance_product = self.map_to_node(row)
                if insurance_product:
                    insurance_products.append(insurance_product)
                    
            except Exception as e:
                self.logger.error(f"转换保险产品数据失败: {row.get('product_code', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"保险产品数据转换完成: {len(insurance_products)} 个保险产品")
        
        return insurance_products
    
    def _get_business_source(self, row: pd.Series) -> str:
        """获取业务来源，根据数据库字段值映射，严格按照规范返回online/offline"""
        source_value = self._get_string_value(row, 'system_source')
        
        if source_value:
            source_lower = source_value.lower()
            if source_lower in ['offline']:
                return "offline"
        
        # 默认返回线上
        return "online" 