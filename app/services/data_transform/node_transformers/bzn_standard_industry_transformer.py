"""
BZN标准行业数据转换器
"""
import pandas as pd
from typing import List, Optional

from .base_transformer import BaseNodeTransformer
from app.models.nodes.bzn_standard_industry_node import BznStandardIndustryNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BznStandardIndustryTransformer(BaseNodeTransformer):
    """BZN标准行业数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[BznStandardIndustryNode]:
        """
        将数据行映射为BZN标准行业节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[BznStandardIndustryNode]: BZN标准行业节点
        """
        try:
            # 提取行业信息
            industry_name = self._get_string_value(row, 'industry_name')
            industry_id = self._get_string_value(row, 'id')
            keywords = self._get_string_value(row, 'keywords')
            
            # 跳过空值
            if not industry_name or industry_name == 'nan' or industry_name == 'None':
                return None
            
            # 创建BZN标准行业对象
            industry = BznStandardIndustryNode(
                code=industry_name,  # 根据要求，code使用industry_name
                name=industry_name,  # 根据要求，name也使用industry_name
                keywords=keywords
            )
            
            return industry
            
        except Exception as e:
            self.logger.warning(f"映射BZN标准行业节点失败: {str(e)}, 行数据: {row.to_dict()}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[BznStandardIndustryNode]:
        """
        将DataFrame转换为BZN标准行业业务模型列表
        
        Args:
            df: 包含行业数据的DataFrame
            
        Returns:
            List[BznStandardIndustryNode]: 转换后的BZN标准行业列表
        """
        try:
            if df is None or df.empty:
                self.logger.warning("输入的DataFrame为空")
                return []
            
            industries = []
            seen_codes = set()  # 用于去重
            
            for _, row in df.iterrows():
                try:
                    industry = self.map_to_node(row)
                    if industry:
                        # 使用业务ID去重
                        unique_key = industry.get_unique_key()
                        if unique_key not in seen_codes:
                            industries.append(industry)
                            seen_codes.add(unique_key)
                        else:
                            self.logger.debug(f"跳过重复的BZN标准行业: {industry.code}")
                    
                except Exception as e:
                    self.logger.error(f"转换BZN标准行业数据失败: {row.get('industry_name', 'unknown')}, 错误: {str(e)}")
                    continue
            
            self.logger.info(f"成功转换 {len(df)} 条原始数据为 {len(industries)} 个唯一的BZN标准行业")
            return industries
            
        except Exception as e:
            self.logger.error(f"BZN标准行业数据转换失败: {str(e)}")
            return [] 