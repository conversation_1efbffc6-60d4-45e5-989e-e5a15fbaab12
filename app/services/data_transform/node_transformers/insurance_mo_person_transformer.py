"""
保险市场运营人员节点转换器

负责保险市场运营人员数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insurance_mo_person_node import InsuranceMOPersonNode
from .base_transformer import BaseNodeTransformer


class InsuranceMOPersonTransformer(BaseNodeTransformer):
    """保险市场运营人员数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceMOPersonNode]:
        """
        将数据行映射为保险市场运营人员节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceMOPersonNode]: 保险市场运营人员节点
        """
        try:
            # 姓名是必须的
            name = self._get_string_value(row, 'name')
            if not name:
                return None
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "InsuranceMOPerson"
            
            insurance_mo_person = InsuranceMOPersonNode(
                # 基础字段（从SQL查询映射而来）
                name=name,  # 来自 sale_name AS name
                code=self._get_string_value(row, 'code'),  # 来自 id AS code
                
                # 个人信息
                gender=self._get_string_value(row, 'gender'),
                phone=self._get_string_value(row, 'phone'),
                email=self._get_string_value(row, 'email'),
                
                # 地理位置信息
                province_code=self._get_string_value(row, 'province_code'),
                province_name=self._get_string_value(row, 'province_name'),
                city_code=self._get_string_value(row, 'city_code'),
                city_name=self._get_string_value(row, 'city_name'),
                county_code=self._get_string_value(row, 'county_code'),
                county_name=self._get_string_value(row, 'county_name'),
                
                # 业务关联信息
                referrer_user_id=self._get_string_value(row, 'referrer_user_id'),
                bdm_user_id=self._get_string_value(row, 'bdm_user_id'),
                channel_id=self._get_string_value(row, 'channel_id'),
                team_id=self._get_string_value(row, 'team_id'),
                status=self._get_string_value(row, 'status'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                data_source_id=self._get_string_value(row, 'data_source_id'),
                data_source_organization_name=self._get_string_value(row, 'data_source_organization_name'),
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return insurance_mo_person
            
        except Exception as e:
            self.logger.error(f"转换保险市场运营人员数据失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceMOPersonNode]:
        """
        批量转换保险市场运营人员数据
        
        Args:
            df: 保险市场运营人员数据DataFrame
            
        Returns:
            List[InsuranceMOPersonNode]: 转换后的保险市场运营人员节点列表
        """
        logger = self.logger
        logger.info(f"开始转换保险市场运营人员数据，共 {len(df)} 条记录")
        
        insurance_mo_person_list = []
        failed_count = 0
        
        for index, row in df.iterrows():
            try:
                insurance_mo_person = self.map_to_node(row)
                if insurance_mo_person:
                    insurance_mo_person_list.append(insurance_mo_person)
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"转换第 {index} 行数据失败: {str(e)}")
                failed_count += 1
        
        logger.info(f"保险市场运营人员数据转换完成: {len(insurance_mo_person_list)} 个保险市场运营人员记录，失败 {failed_count} 个")
        return insurance_mo_person_list 