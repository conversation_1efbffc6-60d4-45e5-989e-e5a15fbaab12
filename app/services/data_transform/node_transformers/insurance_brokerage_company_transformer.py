"""
保险经纪公司节点转换器

负责保险经纪公司数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insurance_brokerage_company_node import InsuranceBrokerageCompanyNode
from .base_transformer import BaseNodeTransformer


class InsuranceBrokerageCompanyTransformer(BaseNodeTransformer):
    """保险经纪公司数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceBrokerageCompanyNode]:
        """
        将数据行映射为保险经纪公司节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceBrokerageCompanyNode]: 保险经纪公司节点
        """
        try:
            name = self._get_string_value(row, 'system_matched_company_name')
            unified_social_credit_code = self._get_string_value(row, 'unified_social_credit_code')
            
            # 至少要有机构名称或统一社会信用代码
            if not name and not unified_social_credit_code:
                return None
            
            # 验证统一社会信用代码格式
            if unified_social_credit_code and not self._validate_business_id(unified_social_credit_code, "company"):
                self.logger.warning(f"统一社会信用代码格式不正确: {unified_social_credit_code}")
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "InsuranceBrokerageCompany"
            
            company = InsuranceBrokerageCompanyNode(
                name=name,
                short_name=self._get_string_value(row, 'short_name'),
                code=unified_social_credit_code,  # code字段设置为统一社会信用代码
                unified_social_credit_code=unified_social_credit_code,
                company_address=self._get_string_value(row, 'company_address'),
                
                # 法定代表人信息
                legal_representative=self._get_string_value(row, 'legal_representative'),
                
                # 联系方式
                phone=self._get_string_value(row, 'phone'),
                additional_phones=self._get_string_value(row, 'additional_phones'),
                email=self._get_string_value(row, 'email'),
                additional_emails=self._get_string_value(row, 'additional_emails'),
                
                # 地理位置信息（包含区域代码）
                province_code=self._get_string_value(row, 'province_code'),
                province=self._get_string_value(row, 'province'),
                city_code=self._get_string_value(row, 'city_code'),
                city=self._get_string_value(row, 'city'),
                district_code=self._get_string_value(row, 'district_code'),
                district=self._get_string_value(row, 'district'),
                
                # 行业分类信息
                industry_category=self._get_string_value(row, 'industry_category'),
                industry_major=self._get_string_value(row, 'industry_major'),
                industry_medium=self._get_string_value(row, 'industry_medium'),
                industry_minor=self._get_string_value(row, 'industry_minor'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                data_source_id=self._get_string_value(row, 'data_source_id'),
                data_source_organization_name=self._get_string_value(row, 'data_source_organization_name'),
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return company
            
        except Exception as e:
            self.logger.error(f"映射保险经纪公司节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceBrokerageCompanyNode]:
        """
        转换保险经纪公司数据
        
        Args:
            df: 保险经纪公司数据DataFrame
            
        Returns:
            List[InsuranceBrokerageCompanyNode]: 转换后的保险经纪公司节点列表
        """
        self.logger.info(f"开始转换保险经纪公司数据，共 {len(df)} 条记录")
        
        companies = []
        
        for _, row in df.iterrows():
            try:
                company = self.map_to_node(row)
                if company:
                    companies.append(company)
                    
            except Exception as e:
                self.logger.error(f"转换保险经纪公司数据失败: {row.get('system_matched_company_name', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"保险经纪公司数据转换完成: {len(companies)} 个保险经纪公司")
        
        return companies 