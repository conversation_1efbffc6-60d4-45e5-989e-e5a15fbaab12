"""
企业节点转换器

负责企业数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.insurance_company_node import InsuranceCompanyNode
from .base_transformer import BaseNodeTransformer


class CompanyTransformer(BaseNodeTransformer):
    """企业数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceCompanyNode]:
        """
        将数据行映射为企业节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceCompanyNode]: 企业节点
        """
        try:
            company_code = self._get_string_value(row, 'company_code')
            credit_code = self._get_string_value(row, 'credit_code')
            
            if not company_code and not credit_code:
                return None
            
            # 验证企业代码格式
            if credit_code and not self._validate_business_id(credit_code, "company"):
                self.logger.warning(f"统一社会信用代码格式不正确: {credit_code}")
            
            company = InsuranceCompanyNode(
                name=self._get_string_value(row, 'company_name'),
                unified_social_credit_code=credit_code,
                company_address=self._get_string_value(row, 'registered_address'),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return company
            
        except Exception as e:
            self.logger.error(f"映射企业节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceCompanyNode]:
        """
        转换企业数据
        
        Args:
            df: 企业数据DataFrame
            
        Returns:
            List[InsuranceCompanyNode]: 转换后的企业节点列表
        """
        self.logger.info(f"开始转换企业数据，共 {len(df)} 条记录")
        
        companies = []
        
        for _, row in df.iterrows():
            try:
                company = self.map_to_node(row)
                if company:
                    companies.append(company)
                    
            except Exception as e:
                self.logger.error(f"转换企业数据失败: {row.get('company_code', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"企业数据转换完成: {len(companies)} 个企业")
        
        return companies
    
    def map_holder_to_node(self, row: pd.Series) -> Optional[InsuranceCompanyNode]:
        """
        将投保人企业数据映射为企业节点
        
        Args:
            row: 数据行（包含投保人企业信息）
            
        Returns:
            Optional[InsuranceCompanyNode]: 企业节点
        """
        try:
            company_code = self._get_string_value(row, 'holder_company_code')
            credit_code = self._get_string_value(row, 'holder_credit_code')
            
            if not company_code and not credit_code:
                return None
            
            company = InsuranceCompanyNode(
                name=self._get_string_value(row, 'holder_company_name'),
                unified_social_credit_code=credit_code,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return company
            
        except Exception as e:
            self.logger.error(f"映射投保人企业节点失败: {str(e)}")
            return None 