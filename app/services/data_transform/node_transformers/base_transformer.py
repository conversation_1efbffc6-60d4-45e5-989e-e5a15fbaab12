"""
基础节点转换器类

定义所有节点转换器的通用接口和工具方法
"""

import pandas as pd
from typing import Optional, Any, List
from datetime import datetime, date
from abc import ABC, abstractmethod

from app.utils.logger import get_logger

from app.utils.validators import validate_id_card, validate_phone

logger = get_logger(__name__)


class BaseNodeTransformer(ABC):
    """基础节点转换器类"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    def map_to_node(self, row: pd.Series) -> Optional[Any]:
        """
        将数据行映射为节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[Any]: 节点对象
        """
        pass
    
    @abstractmethod
    def transform_data(self, df: pd.DataFrame) -> List[Any]:
        """
        转换数据DataFrame为节点列表
        
        Args:
            df: 数据DataFrame
            
        Returns:
            List[Any]: 节点列表
        """
        pass
    
    def _get_string_value(self, row: pd.Series, key: str) -> Optional[str]:
        """获取字符串值，智能处理数值ID字段"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None

        # 智能处理数值ID字段（如team_id, channel_id等）
        if key.endswith('_id'):
            try:
                # 如果是数值类型，直接转换为整数字符串，避免科学计数法
                if isinstance(value, (int, float)):
                    # 对于浮点数，检查是否为整数值
                    if isinstance(value, float) and value.is_integer():
                        return str(int(value))
                    elif isinstance(value, int):
                        return str(value)
                    else:
                        # 非整数浮点数，保持原样
                        return str(value).strip()

                # 如果是字符串，尝试解析为数值
                str_value = str(value).strip()

                # 处理科学计数法格式（如 6.396169485058089e+17）
                if 'e+' in str_value.lower() or 'e-' in str_value.lower():
                    try:
                        # 转换科学计数法为整数
                        float_val = float(str_value)
                        if float_val.is_integer():
                            return str(int(float_val))
                        else:
                            return str(float_val)
                    except (ValueError, TypeError):
                        pass

                # 处理包含小数点的情况（如 123.0）
                elif '.' in str_value:
                    try:
                        float_val = float(str_value)
                        if float_val.is_integer():
                            return str(int(float_val))
                    except (ValueError, TypeError):
                        pass

                return str_value

            except (ValueError, TypeError):
                pass

        # 非ID字段，直接转换为字符串
        return str(value).strip()
    
    def _get_int_value(self, row: pd.Series, key: str) -> Optional[int]:
        """获取整数值"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    
    def _get_float_value(self, row: pd.Series, key: str) -> Optional[float]:
        """获取浮点数值"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _get_decimal_value(self, row: pd.Series, key: str) -> Optional[str]:
        """获取Decimal值（作为字符串返回）"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None
        
        try:
            # 如果是字符串，需要特殊处理百分比
            if isinstance(value, str):
                value = value.strip()
                if value.endswith('%'):
                    # 移除百分号并转换为小数
                    percentage = float(value[:-1])
                    decimal_value = percentage / 100
                    return str(decimal_value)
                else:
                    # 普通字符串，直接返回
                    return value if value else None
            
            # 其他类型转换为字符串以保持精度
            return str(value)
        except (ValueError, TypeError):
            return None
    
    def _get_date_value(self, row: pd.Series, key: str) -> Optional[date]:
        """获取日期值"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None
        
        # 优先处理pandas Timestamp类型
        if hasattr(value, 'date') and callable(getattr(value, 'date')):
            try:
                return value.date()
            except Exception:
                pass
        
        if isinstance(value, date) and not isinstance(value, datetime):
            return value
        
        if isinstance(value, datetime):
            return value.date()
        
        if isinstance(value, str):
            try:
                # 尝试多种日期格式
                for fmt in ["%Y-%m-%d", "%Y/%m/%d", "%Y%m%d"]:
                    try:
                        return datetime.strptime(value, fmt).date()
                    except ValueError:
                        continue
            except Exception:
                pass
        
        return None
    
    def _get_datetime_value(self, row: pd.Series, key: str) -> Optional[datetime]:
        """获取日期时间值"""
        value = row.get(key)
        if pd.isna(value) or value is None:
            return None
        
        if isinstance(value, datetime):
            return value
        
        if isinstance(value, date):
            return datetime.combine(value, datetime.min.time())
        
        if isinstance(value, str):
            try:
                # 尝试多种日期时间格式
                for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%Y/%m/%d %H:%M:%S", "%Y/%m/%d"]:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
            except Exception:
                pass
        
        return None
    
    def _validate_id_card(self, id_card: str) -> Optional[str]:
        """验证身份证号"""
        if not id_card:
            return None
        
        if not validate_id_card(id_card):
            self.logger.warning(f"身份证号格式不正确: {id_card}")
            return None
        
        return id_card
    
    def _validate_phone(self, phone: str) -> Optional[str]:
        """验证手机号"""
        if not phone:
            return None
        
        if not validate_phone(phone):
            self.logger.warning(f"手机号格式不正确: {phone}")
            return None
        
        return phone
    
    def _validate_business_id(self, business_id: str, id_type: str) -> bool:
        """验证业务ID格式"""
        if not business_id:
            return False
        
        return True 