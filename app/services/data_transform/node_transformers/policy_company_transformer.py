"""
保单企业节点转换器

负责保单企业数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime

from app.models.nodes.policy_company_node import PolicyCompanyNode
from .base_transformer import BaseNodeTransformer


class PolicyCompanyTransformer(BaseNodeTransformer):
    """保单企业数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[PolicyCompanyNode]:
        """
        将数据行映射为保单企业节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[PolicyCompanyNode]: 保单企业节点
        """
        try:
            # 企业名称是必须的 (对应 system_matched_company_name)
            company_name = self._get_string_value(row, 'system_matched_company_name')
            if not company_name:
                return None
            
            # 获取统一社会信用代码 (对应 unified_social_credit_code)
            credit_code = self._get_string_value(row, 'unified_social_credit_code')
            
            # 验证企业名称格式
            if not self._validate_business_id(company_name, "company"):
                self.logger.warning(f"企业名称格式不正确: {company_name}")
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "PolicyCompany"
            
            company = PolicyCompanyNode(
                # 核心字段映射: name = system_matched_company_name, code = unified_social_credit_code
                name=company_name,
                code=credit_code,
                unified_social_credit_code=credit_code,
                
                # 法定代表人信息
                legal_representative=self._get_string_value(row, 'legal_representative'),
                
                # 联系方式
                phone=self._get_string_value(row, 'phone'),
                additional_phones=self._get_string_value(row, 'additional_phones'),
                email=self._get_string_value(row, 'email'),
                additional_emails=self._get_string_value(row, 'additional_emails'),
                
                # 地理位置信息（包含区域代码）
                province_code=self._get_string_value(row, 'province_code'),
                province=self._get_string_value(row, 'province'),
                city_code=self._get_string_value(row, 'city_code'),
                city=self._get_string_value(row, 'city'),
                district_code=self._get_string_value(row, 'district_code'),
                district=self._get_string_value(row, 'district'),
                
                # 行业分类
                industry_category=self._get_string_value(row, 'industry_category'),
                industry_major=self._get_string_value(row, 'industry_major'),
                industry_medium=self._get_string_value(row, 'industry_medium'),
                industry_minor=self._get_string_value(row, 'industry_minor'),
                
                # 地址信息
                company_address=self._get_string_value(row, 'company_address'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                data_source_id=self._get_string_value(row, 'data_source_id'),
                data_source_organization_name=self._get_string_value(row, 'data_source_organization_name'),
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return company
            
        except Exception as e:
            self.logger.error(f"映射保单企业节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[PolicyCompanyNode]:
        """
        转换保单企业数据
        
        Args:
            df: 保单企业数据DataFrame
            
        Returns:
            List[PolicyCompanyNode]: 转换后的保单企业节点列表
        """
        self.logger.info(f"开始转换保单企业数据，共 {len(df)} 条记录")
        
        companies = []
        seen_keys = set()  # 用于去重
        
        for _, row in df.iterrows():
            try:
                company = self.map_to_node(row)
                if company:
                    # 使用业务ID去重
                    unique_key = company.get_unique_key()
                    if unique_key not in seen_keys:
                        companies.append(company)
                        seen_keys.add(unique_key)
                    else:
                        self.logger.debug(f"跳过重复的保单企业: {company.name}")
                    
            except Exception as e:
                self.logger.error(f"转换保单企业数据失败: {row.get('system_matched_company_name', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"保单企业数据转换完成: {len(companies)} 个保单企业")
        
        return companies 