"""
保单节点转换器

负责保单数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime, date

from app.models.nodes.policy_node import PolicyNode
from .base_transformer import BaseNodeTransformer


class PolicyTransformer(BaseNodeTransformer):
    """保单数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[PolicyNode]:
        """
        将数据行映射为保单节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[PolicyNode]: 保单节点
        """
        try:
            # 保单号是必须的，优先使用policy_code，如果没有则使用policy_no
            policy_code = self._get_string_value(row, 'policy_code')
            if not policy_code:
                policy_code = self._get_string_value(row, 'policy_no')
            
            if not policy_code:
                return None
            
            # 验证保单号格式
            if not self._validate_business_id(policy_code, "policy"):
                self.logger.warning(f"保单号格式不正确: {policy_code}")
            
            policy = PolicyNode(
                # 基础标识字段
                code=policy_code,
                name=policy_code,
                policy_code=policy_code,
                
                # 保单时间信息
                policy_start_date=self._get_date_value(row, 'policy_start_date'),
                policy_end_date=self._get_date_value(row, 'policy_end_date'),
                assure_time=self._get_datetime_value(row, 'assure_time'),
                
                # 保单状态
                policy_status=self._get_string_value(row, 'policy_status'),
                
                # 保险公司信息
                insurance_code=self._get_string_value(row, 'insurance_code'),
                insurance_name=self._get_string_value(row, 'insurance_name'),
                
                # 投保人信息
                insure_code=self._get_string_value(row, 'insure_code'),
                insure_name=self._get_string_value(row, 'insure_name'),
                
                # 被保险人信息
                insured_code=self._get_string_value(row, 'insured_code'),
                insured_name=self._get_string_value(row, 'insured_name'),
                
                # 产品信息
                product_code=self._get_string_value(row, 'product_code'),
                product_name=self._get_string_value(row, 'product_name'),
                
                # 渠道信息
                channel_id=self._get_string_value(row, 'channel_id'),
                channel_name=self._get_string_value(row, 'channel_name'),
                
                # 销售人员信息
                sale_code=self._get_string_value(row, 'sale_code'),
                sale_name=self._get_string_value(row, 'sale_name'),
                sale_mobile=self._get_string_value(row, 'sale_mobile'),
                
                # 业务数据
                sku_ratio=self._get_decimal_value(row, 'sku_ratio'),
                policy_coverage=self._get_decimal_value(row, 'policy_coverage'),
                
                # 行业信息
                industry=self._get_string_value(row, 'industry'),
                
                # 时间戳
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return policy
            
        except Exception as e:
            self.logger.error(f"映射保单节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[PolicyNode]:
        """
        转换保单数据
        
        Args:
            df: 保单数据DataFrame
            
        Returns:
            List[PolicyNode]: 转换后的保单节点列表
        """
        self.logger.info(f"开始转换保单数据，共 {len(df)} 条记录")
        
        policies = []
        seen_keys = set()  # 用于去重
        
        for _, row in df.iterrows():
            try:
                policy = self.map_to_node(row)
                if policy:
                    # 使用业务ID去重
                    unique_key = policy.get_unique_key()
                    if unique_key not in seen_keys:
                        policies.append(policy)
                        seen_keys.add(unique_key)
                    else:
                        self.logger.debug(f"跳过重复的保单: {policy.policy_code}")
                    
            except Exception as e:
                policy_id = row.get('policy_code') or row.get('policy_no', 'unknown')
                self.logger.error(f"转换保单数据失败: {policy_id}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"保单数据转换完成: {len(policies)} 个保单")
        
        return policies 