"""
特殊名单节点转换器

负责特殊名单数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime, date

from app.models.nodes.special_list_node import SpecialListNode
from .base_transformer import BaseNodeTransformer


class SpecialListTransformer(BaseNodeTransformer):
    """特殊名单数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[SpecialListNode]:
        """
        将数据行映射为特殊名单节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[SpecialListNode]: 特殊名单节点
        """
        try:
            # 特殊名单ID是必须的
            list_id = self._get_string_value(row, 'id')
            if not list_id:
                return None
            
            # 证件号码作为code
            cert_no = self._get_string_value(row, 'code')  # SQL中 cert_no AS code
            name = self._get_string_value(row, 'name')
            
            special_list = SpecialListNode(
                # 基础标识字段
                code=cert_no or list_id,  # 使用证件号码作为code，如果没有则使用list_id
                name=name or f"特殊名单_{list_id}",
                list_id=list_id,
                
                # 名单分类信息
                list_type=self._get_int_value(row, 'list_type'),
                list_category=self._get_int_value(row, 'list_category'),
                
                # 数据来源信息
                data_source=self._get_string_value(row, 'data_source'),
                data_source_type=self._get_string_value(row, 'data_source_type'),
                
                # 风险信息
                risk_level=self._get_string_value(row, 'risk_level'),
                
                # 状态信息
                status=self._get_string_value(row, 'status'),
                
                # 时间信息
                effective_day=self._get_date_value(row, 'effective_day'),
                
                # 其他信息
                reason=self._get_string_value(row, 'reason'),
                remark=self._get_string_value(row, 'remark'),
                
                # 时间戳
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return special_list
            
        except Exception as e:
            list_id = row.get('id', 'unknown')
            self.logger.error(f"映射特殊名单节点失败: {list_id}, 错误: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[SpecialListNode]:
        """
        转换特殊名单数据
        
        Args:
            df: 特殊名单数据DataFrame
            
        Returns:
            List[SpecialListNode]: 转换后的特殊名单节点列表
        """
        self.logger.info(f"开始转换特殊名单数据，共 {len(df)} 条记录")
        
        special_lists = []
        seen_keys = set()  # 用于去重
        
        for _, row in df.iterrows():
            try:
                special_list = self.map_to_node(row)
                if special_list:
                    # 使用业务ID去重
                    unique_key = special_list.get_unique_key()
                    if unique_key not in seen_keys:
                        special_lists.append(special_list)
                        seen_keys.add(unique_key)
                    else:
                        self.logger.debug(f"跳过重复的特殊名单: {special_list.list_id}")
                    
            except Exception as e:
                list_id = row.get('id', 'unknown')
                self.logger.error(f"转换特殊名单数据失败: {list_id}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"特殊名单数据转换完成: {len(special_lists)} 个特殊名单")
        
        return special_lists 