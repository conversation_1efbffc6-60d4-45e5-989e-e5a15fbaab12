"""
国家标准行业数据转换器

将Hive原始数据转换为国家标准行业业务模型
"""

import pandas as pd
from typing import List, Any, Optional

from app.services.data_transform.node_transformers.base_transformer import BaseNodeTransformer
from app.models.nodes.national_standard_industry_node import NationalStandardIndustryNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class NationalStandardIndustryTransformer(BaseNodeTransformer):
    """国家标准行业数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[NationalStandardIndustryNode]:
        """
        将数据行映射为国家标准行业节点
        
        Args:
            row: 单行国家标准行业数据
            
        Returns:
            Optional[NationalStandardIndustryNode]: 转换后的国家标准行业对象
        """
        try:
            return self._transform_single_industry(row)
        except Exception as e:
            self.logger.error(f"转换国家标准行业记录失败: {str(e)}, 数据: {row.to_dict()}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[NationalStandardIndustryNode]:
        """
        转换国家标准行业数据
        
        Args:
            df: 包含国家标准行业信息的DataFrame
            
        Returns:
            List[NationalStandardIndustryNode]: 转换后的国家标准行业列表
        """
        try:
            if df.empty:
                logger.warning("输入的国家标准行业数据为空")
                return []
            
            industries = []
            seen_codes = set()  # 用于去重
            
            for _, row in df.iterrows():
                industry = self.map_to_node(row)
                if industry:
                    # 使用业务ID去重
                    unique_key = industry.get_unique_key()
                    if unique_key not in seen_codes:
                        industries.append(industry)
                        seen_codes.add(unique_key)
                    else:
                        self.logger.debug(f"跳过重复的国家标准行业: {industry.code}")
            
            logger.info(f"成功转换 {len(df)} 条原始数据为 {len(industries)} 个唯一的国家标准行业记录")
            return industries
            
        except Exception as e:
            logger.error(f"国家标准行业数据转换失败: {str(e)}")
            return []
    
    def _transform_single_industry(self, row: pd.Series) -> NationalStandardIndustryNode:
        """
        转换单个国家标准行业记录
        
        Args:
            row: 单行国家标准行业数据
            
        Returns:
            NationalStandardIndustryNode: 转换后的国家标准行业对象
        """
        # 提取基本字段
        code = self._get_string_value(row, 'code')
        name = self._get_string_value(row, 'name')
        
        # 验证必填字段
        if not code:
            raise ValueError("行业编码不能为空")
        if not name:
            raise ValueError("行业名称不能为空")
        
        # 提取可选字段
        parent_code = self._get_string_value(row, 'parent_code')
        level = self._get_int_value(row, 'level')
        
        # 处理空的parent_code
        if parent_code and parent_code.strip() == '':
            parent_code = None
        
        return NationalStandardIndustryNode(
            code=code,
            name=name,
            parent_code=parent_code,
            level=level
        ) 