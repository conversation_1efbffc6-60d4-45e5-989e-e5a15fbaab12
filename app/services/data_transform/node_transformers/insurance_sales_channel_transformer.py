"""
保险销售渠道节点转换器

负责保险销售渠道数据的映射和转换
"""

import pandas as pd
from typing import Optional, List
from datetime import datetime, date

from app.models.nodes.insurance_sales_channel_node import InsuranceSalesChannelNode
from .base_transformer import BaseNodeTransformer


class InsuranceSalesChannelTransformer(BaseNodeTransformer):
    """保险销售渠道数据转换器"""
    
    def map_to_node(self, row: pd.Series) -> Optional[InsuranceSalesChannelNode]:
        """
        将数据行映射为保险销售渠道节点
        
        Args:
            row: 数据行
            
        Returns:
            Optional[InsuranceSalesChannelNode]: 保险销售渠道节点
        """
        try:
            # 渠道ID是必须的
            channel_id = self._get_string_value(row, 'id')
            if not channel_id:
                return None
            
            # 组织名称是必须的
            org_name = self._get_string_value(row, 'org_name')
            if not org_name:
                self.logger.warning(f"跳过缺少组织名称的记录: ID={channel_id}")
                return None
            
            # 验证渠道ID格式
            if not self._validate_business_id(channel_id, "channel"):
                self.logger.warning(f"渠道ID格式不正确: {channel_id}")
            
            # 设置数据来源节点类型（固定值）
            data_source_node = "InsuranceSalesChannel"
            
            channel = InsuranceSalesChannelNode(
                code=channel_id,
                name=org_name,
                org_type=self._get_int_value(row, 'org_type'),
                level=self._get_int_value(row, 'level'),
                parent_id=self._get_int_value(row, 'parent_id'),
                head=self._get_int_value(row, 'head'),
                bd_user_id=self._get_int_value(row, 'bd_user_id'),
                bds_user_id=self._get_int_value(row, 'bds_user_id'),
                status=self._get_int_value(row, 'status'),
                province_code=self._get_string_value(row, 'province_code'),
                province_name=self._get_string_value(row, 'province_name'),
                city_code=self._get_string_value(row, 'city_code'),
                city_name=self._get_string_value(row, 'city_name'),
                
                # 数据来源信息
                data_source_node=data_source_node,
                data_source_id=self._get_string_value(row, 'data_source_id'),
                data_source_organization_name=self._get_string_value(row, 'data_source_organization_name'),
                
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return channel
            
        except Exception as e:
            self.logger.error(f"映射保险销售渠道节点失败: {str(e)}")
            return None
    
    def transform_data(self, df: pd.DataFrame) -> List[InsuranceSalesChannelNode]:
        """
        转换保险销售渠道数据
        
        Args:
            df: 保险销售渠道数据DataFrame
            
        Returns:
            List[InsuranceSalesChannelNode]: 转换后的保险销售渠道节点列表
        """
        self.logger.info(f"开始转换保险销售渠道数据，共 {len(df)} 条记录")
        
        channels = []
        seen_keys = set()  # 用于去重
        
        for _, row in df.iterrows():
            try:
                channel = self.map_to_node(row)
                if channel:
                    # 使用业务ID去重
                    unique_key = channel.get_unique_key()
                    if unique_key not in seen_keys:
                        channels.append(channel)
                        seen_keys.add(unique_key)
                    else:
                        self.logger.debug(f"跳过重复的保险销售渠道: {channel.code}")
                    
            except Exception as e:
                self.logger.error(f"转换保险销售渠道数据失败: {row.get('id', 'unknown')}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"保险销售渠道数据转换完成: {len(channels)} 个渠道")
        
        return channels 