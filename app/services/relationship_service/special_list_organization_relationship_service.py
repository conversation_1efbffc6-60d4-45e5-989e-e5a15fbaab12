"""
特殊名单与组织机构关系服务

专门负责 SpecialList 节点（list_category = 2 && list_type = 1）与 Organization 节点之间关系的创建和管理
从Neo4j查询特殊名单的code属性，关联对应的组织机构节点
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SpecialListOrganizationRelationshipService(BaseRelationshipService):
    """特殊名单与组织机构关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                           limit: Optional[int] = None,
                           list_id_filter: Optional[str] = None) -> bool:
        """
        从Neo4j查询特殊名单数据，关联Organization节点，并建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            list_id_filter: 特殊名单ID过滤条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建特殊名单与组织机构关系")
            
            # 1. 从Neo4j查询特殊名单数据
            special_list_data = self._query_special_list_data_from_neo4j(limit, list_id_filter)
            if not special_list_data:
                logger.warning("未找到有效的特殊名单数据")
                return True
            
            # 2. 查找匹配的Organization节点
            matched_data = self._find_matching_organizations(special_list_data)
            if not matched_data:
                logger.warning("未找到匹配的组织机构数据")
                return True
            
            # 3. 创建关系
            success = self._create_special_list_organization_relationships(matched_data)
            
            if success:
                logger.info(f"成功处理 {len(matched_data)} 个特殊名单与组织机构关系")
                return True
            else:
                logger.error("创建特殊名单与组织机构关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建特殊名单与组织机构关系失败: {str(e)}")
            return False
    
    def _query_special_list_data_from_neo4j(self, 
                                           limit: Optional[int] = None,
                                           list_id_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询特殊名单数据（企业黑名单）
        
        Args:
            limit: 记录数限制
            list_id_filter: 特殊名单ID过滤条件
            
        Returns:
            List[Dict[str, Any]]: 特殊名单数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "sl.list_category = 2",  # 企业
                "sl.list_type = 1",      # 黑名单
                "sl.code IS NOT NULL", 
                "sl.code <> ''"
            ]
            parameters = {}
            
            if list_id_filter:
                conditions.append("sl.list_id CONTAINS $list_id_filter")
                parameters['list_id_filter'] = list_id_filter
            
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (sl:SpecialList)
            WHERE {where_clause}
            RETURN sl.list_id as list_id,
                   sl.code as code,
                   sl.name as name,
                   sl.list_type as list_type,
                   sl.list_category as list_category,
                   sl.risk_level as risk_level,
                   sl.status as status,
                   sl.reason as reason
            ORDER BY sl.list_id
            {limit_clause}
            """
            
            logger.info(f"查询特殊名单数据，条件: {where_clause}")
            results = self._safe_execute_query(query, parameters)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                code = str(record.get('code', '')).strip()
                list_id = str(record.get('list_id', '')).strip()
                
                if code and code != 'nan' and code != 'None' and list_id:
                    valid_data.append({
                        'list_id': list_id,
                        'code': code,
                        'name': record.get('name', ''),
                        'list_type': record.get('list_type'),
                        'list_category': record.get('list_category'),
                        'risk_level': record.get('risk_level', ''),
                        'status': record.get('status', ''),
                        'reason': record.get('reason', '')
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的特殊名单记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j特殊名单数据失败: {str(e)}")
            return []
    
    def _find_matching_organizations(self, special_list_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        查找匹配的Organization节点
        
        Args:
            special_list_data: 特殊名单数据列表
            
        Returns:
            List[Dict[str, Any]]: 匹配的数据列表
        """
        try:
            # 提取所有code
            codes = [data['code'] for data in special_list_data]
            
            if not codes:
                return []
            
            # 查询匹配的Organization节点
            query = """
            UNWIND $codes as code
            MATCH (sl:SpecialList {code: code})
            MATCH (org:Organization {code: code})
            RETURN sl.list_id as list_id,
                   sl.code as sl_code,
                   sl.name as sl_name,
                   sl.risk_level as risk_level,
                   sl.status as status,
                   sl.reason as reason,
                   org.code as org_code,
                   org.name as org_name,
                   org.business_id as org_business_id
            """
            
            result = self._safe_execute_query(query, {'codes': codes})
            
            if not result:
                logger.warning("未找到匹配的组织机构")
                return []
            
            matched_data = []
            for record in result:
                matched_data.append({
                    'list_id': record['list_id'],
                    'sl_code': record['sl_code'],
                    'sl_name': record['sl_name'],
                    'risk_level': record['risk_level'],
                    'status': record['status'],
                    'reason': record['reason'],
                    'org_code': record['org_code'],
                    'org_name': record['org_name'],
                    'org_business_id': record['org_business_id']
                })
            
            logger.info(f"找到 {len(matched_data)} 个匹配的组织机构")
            return matched_data
            
        except Exception as e:
            logger.error(f"查找匹配的组织机构失败: {str(e)}")
            return []
    
    def _create_special_list_organization_relationships(self, matched_data: List[Dict[str, Any]]) -> bool:
        """
        创建特殊名单与组织机构的关系
        
        Args:
            matched_data: 匹配的数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(matched_data), batch_size):
                batch_data = matched_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (sl:SpecialList {list_id: rel.list_id})
                MATCH (org:Organization {code: rel.org_code})
                MERGE (sl)-[r:BLACKLISTED]->(org)
                SET r.relationship_type = 'BLACKLISTED',
                    r.relationship_status = 'active',
                    r.relationship_strength = 10,
                    r.data_source_node = 'SpecialList',
                    r.risk_level = rel.risk_level,
                    r.blacklist_status = rel.status,
                    r.blacklist_reason = rel.reason,
                    r.remarks = '企业黑名单: ' + rel.sl_name + ' -> ' + rel.org_name,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个特殊名单与组织机构关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建特殊名单与组织机构关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有特殊名单与组织机构关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有特殊名单与组织机构关系")
            return True
            
        except Exception as e:
            logger.error(f"删除特殊名单与组织机构关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取特殊名单与组织机构关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 黑名单企业数量
            blacklisted_org_query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            RETURN count(DISTINCT org) as blacklisted_org_count
            """
            org_result = self._safe_execute_query(blacklisted_org_query)
            blacklisted_org_count = org_result[0]['blacklisted_org_count'] if org_result else 0
            
            # 特殊名单企业数量
            special_list_count_query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            RETURN count(DISTINCT sl) as special_list_count
            """
            sl_result = self._safe_execute_query(special_list_count_query)
            special_list_count = sl_result[0]['special_list_count'] if sl_result else 0
            
            # 按风险等级统计
            risk_level_query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            WHERE r.risk_level IS NOT NULL
            RETURN r.risk_level as risk_level, count(r) as count
            ORDER BY count DESC
            """
            risk_result = self._safe_execute_query(risk_level_query)
            risk_level_stats = {record['risk_level']: record['count'] for record in risk_result}
            
            # 按状态统计
            status_query = """
            MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
            WHERE r.blacklist_status IS NOT NULL
            RETURN r.blacklist_status as status, count(r) as count
            ORDER BY count DESC
            """
            status_result = self._safe_execute_query(status_query)
            status_stats = {record['status']: record['count'] for record in status_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="特殊名单与组织机构关系",
                additional_stats={
                    "blacklisted_organizations": blacklisted_org_count,
                    "special_list_entries": special_list_count,
                    "by_risk_level": risk_level_stats,
                    "by_status": status_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取特殊名单与组织机构关系统计失败: {str(e)}")
            return {}
    
    def search_special_list_organization_relationships(self, 
                                                     list_id: Optional[str] = None,
                                                     org_name: Optional[str] = None,
                                                     risk_level: Optional[str] = None,
                                                     include_node_details: bool = True,
                                                     limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索特殊名单与组织机构关系
        
        Args:
            list_id: 特殊名单ID过滤条件
            org_name: 组织机构名称过滤条件
            risk_level: 风险等级过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if list_id:
                conditions.append("sl.list_id CONTAINS $list_id")
                parameters['list_id'] = list_id
            
            if org_name:
                conditions.append("org.name CONTAINS $org_name")
                parameters['org_name'] = org_name
            
            if risk_level:
                conditions.append("r.risk_level = $risk_level")
                parameters['risk_level'] = risk_level
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
                WHERE {where_clause}
                RETURN r,
                       {{list_id: sl.list_id, code: sl.code, name: sl.name, risk_level: sl.risk_level}} as special_list_info,
                       {{code: org.code, name: org.name, business_id: org.business_id}} as organization_info
                ORDER BY sl.list_id, org.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (sl:SpecialList)-[r:BLACKLISTED]->(org:Organization)
                WHERE {where_clause}
                RETURN r, sl.list_id as list_id, org.code as org_code
                ORDER BY sl.list_id, org.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'BLACKLISTED',
                    'relationship_status': 'active'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['special_list'] = record['special_list_info']
                    rel_data['organization'] = record['organization_info']
                else:
                    rel_data['list_id'] = record['list_id']
                    rel_data['org_code'] = record['org_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个特殊名单与组织机构关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索特殊名单与组织机构关系失败: {str(e)}")
            return [] 