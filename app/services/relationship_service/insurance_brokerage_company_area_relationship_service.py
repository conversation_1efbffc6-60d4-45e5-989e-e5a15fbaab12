"""
保险经纪公司区域关系服务

专门负责保险经纪公司 InsuranceBrokerageCompany 节点与区域 Area 节点之间关系的创建和管理
从Neo4j查询保险经纪公司的地址信息，匹配对应的区域节点并建立关系
按优先级顺序匹配: district -> city -> province
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceBrokerageCompanyAreaRelationshipService(BaseRelationshipService):
    """保险经纪公司区域关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险经纪公司数据，匹配区域节点并建立关系

        Args:
            limit: 限制处理的记录数（用于测试）

        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险经纪公司区域关系")

            # 1. 从Neo4j查询保险经纪公司地址数据
            company_data = self._query_company_area_data_from_neo4j(limit)
            if not company_data:
                logger.warning("未找到有效的保险经纪公司地址数据")
                return True

            # 2. 构建关系数据
            relationship_data = self._build_relationship_data_from_neo4j(company_data)

            if not relationship_data:
                logger.warning("没有有效的保险经纪公司区域关系数据需要创建")
                return True

            # 3. 批量创建关系
            success = self._create_company_area_relationships(relationship_data)

            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险经纪公司区域关系")
                return True
            else:
                logger.error("批量创建保险经纪公司区域关系失败")
                return False

        except Exception as e:
            logger.error(f"从Neo4j创建保险经纪公司区域关系失败: {str(e)}")
            return False
    
    def _query_company_area_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险经纪公司的地址数据

        Args:
            limit: 记录数限制

        Returns:
            List[Dict[str, Any]]: 保险经纪公司地址数据列表
        """
        try:
            # 构建查询条件，查询有地址信息的保险经纪公司
            conditions = [
                "(company.province IS NOT NULL AND company.province <> '')",
                "OR (company.city IS NOT NULL AND company.city <> '')",
                "OR (company.district IS NOT NULL AND company.district <> '')",
                "OR (company.address IS NOT NULL AND company.address <> '')"
            ]
            where_clause = " AND (" + " ".join(conditions) + ")"
            limit_clause = f"LIMIT {limit}" if limit else ""

            query = f"""
            MATCH (company:InsuranceBrokerageCompany)
            WHERE company.code IS NOT NULL AND company.code <> ''
            {where_clause}
            RETURN company.code as company_code,
                   company.name as company_name,
                   company.unified_social_credit_code as unified_social_credit_code,
                   company.province as province,
                   company.city as city,
                   company.district as district,
                   company.address as address,
                   company.business_id as company_business_id
            ORDER BY company.code
            {limit_clause}
            """

            logger.info(f"查询保险经纪公司地址数据，条件: {where_clause}")
            results = self._safe_execute_query(query)

            if not results:
                return []

            # 过滤掉无效的地址数据
            valid_data = []
            for record in results:
                company_code = str(record.get('company_code', '')).strip()
                company_name = str(record.get('company_name', '')).strip()

                # 检查是否有有效的地址信息
                province = str(record.get('province', '')).strip()
                city = str(record.get('city', '')).strip()
                district = str(record.get('district', '')).strip()
                address = str(record.get('address', '')).strip()

                if (company_code and company_name and
                    (self._is_valid_area_value(province) or
                     self._is_valid_area_value(city) or
                     self._is_valid_area_value(district) or
                     self._is_valid_area_value(address))):

                    valid_data.append({
                        'company_code': company_code,
                        'company_name': company_name,
                        'unified_social_credit_code': record.get('unified_social_credit_code', ''),
                        'province': province,
                        'city': city,
                        'district': district,
                        'address': address,
                        'company_business_id': record.get('company_business_id', company_code)
                    })

            logger.info(f"找到 {len(valid_data)} 个有效的保险经纪公司地址记录")
            return valid_data

        except Exception as e:
            logger.error(f"查询Neo4j保险经纪公司地址数据失败: {str(e)}")
            return []
    
    def _build_relationship_data_from_neo4j(self, company_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从Neo4j保险经纪公司数据构建与区域的关系数据
        按优先级匹配: district -> city -> province

        Args:
            company_data: Neo4j保险经纪公司数据列表

        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0

        for company in company_data:
            company_code = company.get('company_code', '')
            company_name = company.get('company_name', '')
            unified_social_credit_code = company.get('unified_social_credit_code', '')

            # 地址信息
            province = str(company.get('province', '')).strip()
            city = str(company.get('city', '')).strip()
            district = str(company.get('district', '')).strip()

            # 跳过无效数据
            if not company_code or not company_name:
                skipped_count += 1
                continue

            # 查找匹配的区域节点（按优先级: district -> city -> province）
            matched_area = self._find_matching_area(province, city, district)

            if not matched_area:
                skipped_count += 1
                continue

            relationship_data.append({
                'company_code': company_code,
                'company_name': company_name,
                'unified_social_credit_code': unified_social_credit_code if self._is_valid_area_value(unified_social_credit_code) else None,
                'area_code': matched_area['area_code'],
                'area_name': matched_area['area_name'],
                'location_type': matched_area['location_type'],
                'province': province if self._is_valid_area_value(province) else None,
                'city': city if self._is_valid_area_value(city) else None,
                'district': district if self._is_valid_area_value(district) else None,
                'remarks': f"保险经纪公司地理位置关系: {company_name} 位于 {matched_area['area_name']}({matched_area['area_code']})",
                'data_source_node': 'InsuranceBrokerageCompany',
                'relationship_strength': 8,
                'relationship_type': '地理位置关系'
            })

        logger.info(f"构建了 {len(relationship_data)} 个有效的保险经纪公司区域关系，跳过了 {skipped_count} 个无效记录")

        # 按位置类型统计
        location_stats = {}
        for rel in relationship_data:
            loc_type = rel['location_type']
            location_stats[loc_type] = location_stats.get(loc_type, 0) + 1

        logger.info(f"匹配统计: {location_stats}")

        return relationship_data
    
    def _find_matching_area(self, province: str, city: str, district: str) -> Optional[Dict[str, Any]]:
        """
        查找匹配的区域节点，按优先级匹配: district -> city -> province

        Args:
            province: 省份名称
            city: 城市名称
            district: 区县名称

        Returns:
            Optional[Dict[str, Any]]: 匹配的区域信息，包含 area_code, area_name, location_type
        """
        try:
            # 按优先级查找区域节点
            search_areas = []

            if self._is_valid_area_value(district):
                search_areas.append(('district', district))
            if self._is_valid_area_value(city):
                search_areas.append(('city', city))
            if self._is_valid_area_value(province):
                search_areas.append(('province', province))

            for location_type, area_name in search_areas:
                # 查找匹配的区域节点
                query = """
                MATCH (area:Area)
                WHERE area.name = $area_name
                RETURN area.code as area_code, area.name as area_name
                LIMIT 1
                """

                result = self._safe_execute_query(query, {'area_name': area_name})
                if result:
                    return {
                        'area_code': result[0]['area_code'],
                        'area_name': result[0]['area_name'],
                        'location_type': location_type
                    }

            return None

        except Exception as e:
            logger.error(f"查找匹配区域失败: {str(e)}")
            return None

    def _create_company_area_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险经纪公司与区域的关系

        Args:
            relationship_data: 关系数据列表

        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0

            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]

                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (company:InsuranceBrokerageCompany {code: rel.company_code})
                MATCH (area:Area {code: rel.area_code})
                MERGE (company)-[r:LOCATED_IN]->(area)
                SET r.relationship_type = 'LOCATED_IN',
                    r.relationship_status = 'active',
                    r.relationship_strength = rel.relationship_strength,
                    r.location_type = rel.location_type,
                    r.province = rel.province,
                    r.city = rel.city,
                    r.district = rel.district,
                    r.data_source_node = rel.data_source_node,
                    r.remarks = rel.remarks,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """

                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")

            logger.info(f"总共成功创建 {success_count} 个保险经纪公司区域关系")
            return success_count > 0

        except Exception as e:
            logger.error(f"创建保险经纪公司区域关系失败: {str(e)}")
            return False
    
    def _is_valid_area_value(self, value: str) -> bool:
        """
        判断区域值是否有效
        
        Args:
            value: 待检查的值
            
        Returns:
            bool: 是否有效
        """
        if not value:
            return False
        
        value_str = str(value).strip().lower()
        invalid_values = ['', 'nan', 'null', 'none', 'n/a', 'na']
        return value_str not in invalid_values
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险经纪公司区域关系

        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            DELETE r
            """

            self._safe_execute_query(query)
            logger.info("已删除所有保险经纪公司区域关系")
            return True

        except Exception as e:
            logger.error(f"删除保险经纪公司区域关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险经纪公司区域关系统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0

            # 有地址关系的保险经纪公司数量
            company_with_location_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN count(DISTINCT company) as company_count
            """
            company_result = self._safe_execute_query(company_with_location_query)
            company_count = company_result[0]['company_count'] if company_result else 0

            # 按位置类型统计
            location_type_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.location_type IS NOT NULL
            RETURN r.location_type as location_type, count(r) as count
            ORDER BY count DESC
            """
            location_type_result = self._safe_execute_query(location_type_query)
            location_type_stats = {record['location_type']: record['count'] for record in location_type_result}

            # 按区域统计（Top 10）
            area_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN area.name as area_name, count(r) as company_count
            ORDER BY company_count DESC
            LIMIT 10
            """
            area_result = self._safe_execute_query(area_query)
            area_stats = {record['area_name']: record['company_count'] for record in area_result}

            # 数据源统计
            source_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}

            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险经纪公司区域关系",
                additional_stats={
                    "companies_with_location": company_count,
                    "by_location_type": location_type_stats,
                    "by_area_name": area_stats,
                    "by_data_source_node": source_stats,
                }
            )

        except Exception as e:
            logger.error(f"获取保险经纪公司区域关系统计失败: {str(e)}")
            return {}

    def cleanup_duplicate_relationships(self) -> int:
        """
        清理重复的保险经纪公司区域关系
        对于相同的公司-区域对，保留最早创建的关系，删除其他重复关系

        Returns:
            int: 删除的重复关系数量
        """
        try:
            # 查找重复的关系
            find_duplicates_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            WITH company, area, collect(r) as rels
            WHERE size(rels) > 1
            RETURN company.code as company_code, area.code as area_code, rels
            """

            duplicates_result = self._safe_execute_query(find_duplicates_query)

            if not duplicates_result:
                logger.info("没有发现重复的保险经纪公司区域关系")
                return 0

            total_deleted = 0

            for record in duplicates_result:
                company_code = record['company_code']
                area_code = record['area_code']
                rels = record['rels']

                if len(rels) <= 1:
                    continue

                logger.info(f"发现重复的关系，公司: {company_code}，区域: {area_code}，共 {len(rels)} 个")

                # 保留最早创建的关系，删除其他
                cleanup_query = """
                MATCH (company:InsuranceBrokerageCompany {code: $company_code})-[r:LOCATED_IN]->(area:Area {code: $area_code})
                WITH r
                ORDER BY COALESCE(r.created_at, datetime()) ASC
                WITH collect(r) as rels
                WHERE size(rels) > 1
                UNWIND rels[1..] as duplicate_rel
                DELETE duplicate_rel
                RETURN count(duplicate_rel) as deleted_count
                """

                cleanup_result = self._safe_execute_query(cleanup_query, {
                    'company_code': company_code,
                    'area_code': area_code
                })
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count

                logger.info(f"清理了 {deleted_count} 个重复关系，公司: {company_code}，区域: {area_code}")

            logger.info(f"总共清理了 {total_deleted} 个重复的保险经纪公司区域关系")
            return total_deleted

        except Exception as e:
            logger.error(f"清理重复关系失败: {str(e)}")
            return 0
    
    def search_location_relationships(self,
                                    company_code: Optional[str] = None,
                                    company_name: Optional[str] = None,
                                    area_name: Optional[str] = None,
                                    location_type: Optional[str] = None,
                                    province: Optional[str] = None,
                                    city: Optional[str] = None,
                                    district: Optional[str] = None,
                                    include_node_details: bool = True,
                                    limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险经纪公司区域位置关系

        Args:
            company_code: 保险经纪公司编码过滤条件
            company_name: 公司名称过滤条件
            area_name: 区域名称过滤条件
            location_type: 位置类型过滤条件（district/city/province）
            province: 省份过滤条件
            city: 城市过滤条件
            district: 区县过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制

        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}

            if company_code:
                conditions.append("company.code CONTAINS $company_code")
                parameters['company_code'] = company_code

            if company_name:
                conditions.append("company.name CONTAINS $company_name")
                parameters['company_name'] = company_name

            if area_name:
                conditions.append("area.name CONTAINS $area_name")
                parameters['area_name'] = area_name

            if location_type:
                conditions.append("r.location_type = $location_type")
                parameters['location_type'] = location_type

            if province:
                conditions.append("r.province = $province")
                parameters['province'] = province

            if city:
                conditions.append("r.city = $city")
                parameters['city'] = city

            if district:
                conditions.append("r.district = $district")
                parameters['district'] = district

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            if include_node_details:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
                WHERE {where_clause}
                RETURN r,
                       {{code: company.code, name: company.name, business_id: company.business_id}} as company_info,
                       {{code: area.code, name: area.name, business_id: area.business_id}} as area_info
                ORDER BY company.code, area.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
                WHERE {where_clause}
                RETURN r, company.code as company_code, area.code as area_code
                ORDER BY company.code, area.code
                LIMIT {limit}
                """

            results = self._safe_execute_query(query, parameters)

            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'LOCATED_IN'
                }

                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass

                if include_node_details:
                    rel_data['company'] = record['company_info']
                    rel_data['area'] = record['area_info']
                else:
                    rel_data['company_code'] = record['company_code']
                    rel_data['area_code'] = record['area_code']

                relationships.append(rel_data)

            logger.info(f"搜索到 {len(relationships)} 个保险经纪公司区域关系")
            return relationships

        except Exception as e:
            logger.error(f"搜索保险经纪公司区域关系失败: {str(e)}")
            return []