"""
保险经纪公司区域关系服务

专门负责保险经纪公司 InsuranceBrokerageCompany 节点与区域 Area 节点之间关系的创建和管理
按优先级顺序匹配: district -> city -> province
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceBrokerageCompanyAreaRelationshipService(BaseRelationshipService):
    """保险经纪公司区域关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保险经纪公司与区域的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保险经纪公司区域关系")
            
            # 1. 从Hive查询保险经纪公司区域数据
            hive_data = self._query_company_area_data_from_hive(limit, where_conditions)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保险经纪公司区域关系数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保险经纪公司区域关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险经纪公司区域关系")
                return True
            else:
                logger.error("批量创建保险经纪公司区域关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保险经纪公司区域关系失败: {str(e)}")
            return False
    
    def _query_company_area_data_from_hive(self, 
                                         limit: Optional[int] = None,
                                         where_conditions: Optional[str] = None) -> pd.DataFrame:
        """
        从Hive查询保险经纪公司区域数据
        
        Args:
            limit: 限制查询数量
            where_conditions: 额外的WHERE条件
            
        Returns:
            pd.DataFrame: 保险经纪公司区域数据
        """
        try:
            limit_clause = f"LIMIT {limit}" if limit else ""
            where_clause = f"AND {where_conditions}" if where_conditions else ""
            
            query = f"""
            SELECT tcb.system_matched_company_name as company_name,
                   tcb.unified_social_credit_code,
                   tcb.province,
                   tcb.city,
                   tcb.district,
                   area_province.city_code         AS province_code,
                   area_city.city_code             AS city_code,
                   area_district.city_code         AS district_code
            FROM dwddb.dwd_company_productsadmin cp
            INNER JOIN dimdb.dim_tyc_company_base tcb ON cp.com_type = 2
                AND cp.com_name = tcb.system_matched_company_name
            LEFT JOIN dimdb.dim_area_code area_province
                      ON tcb.province = area_province.city_name AND area_province.city_level = '1'
            LEFT JOIN dimdb.dim_area_code area_city
                      ON tcb.city = area_city.city_name AND area_city.city_parent_code = area_province.city_code AND
                         area_city.city_level = '2'
            LEFT JOIN dimdb.dim_area_code area_district
                      ON tcb.district = area_district.city_name AND
                         area_district.city_parent_code = area_city.city_code AND
                         area_district.city_level = '3'
            WHERE tcb.system_matched_company_name IS NOT NULL
            {where_clause}
            ORDER BY tcb.system_matched_company_name ASC
            {limit_clause}
            """
            
            logger.info(f"查询保险经纪公司区域数据")
            data = self.query_hive_data(query)
            logger.info(f"从Hive查询到 {len(data)} 条保险经纪公司区域记录")
            return data
            
        except Exception as e:
            logger.error(f"从Hive查询保险经纪公司区域数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保险经纪公司与区域的关系数据
        按优先级匹配: district -> city -> province
        
        Args:
            hive_data: Hive保险经纪公司区域数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0
        
        for index, row in hive_data.iterrows():
            company_name = str(row.get('company_name', '')).strip()
            unified_social_credit_code = str(row.get('unified_social_credit_code', '')).strip()
            
            # 区域名称
            province = str(row.get('province', '')).strip()
            city = str(row.get('city', '')).strip()
            district = str(row.get('district', '')).strip()
            
            # 区域代码
            province_code = str(row.get('province_code', '')).strip()
            city_code = str(row.get('city_code', '')).strip()
            district_code = str(row.get('district_code', '')).strip()
            
            # 跳过无效数据
            if not company_name or company_name == 'nan':
                skipped_count += 1
                continue
            
            # 使用改进的判断逻辑
            district_valid = self._is_valid_area_value(district) and self._is_valid_area_value(district_code)
            city_valid = self._is_valid_area_value(city) and self._is_valid_area_value(city_code)
            province_valid = self._is_valid_area_value(province) and self._is_valid_area_value(province_code)
            
            # 确定匹配的区域名称和代码（优先级: district -> city -> province）
            area_name = None
            area_code = None
            location_type = None
            
            if district_valid:
                area_name = district
                area_code = district_code
                location_type = 'district'
            elif city_valid:
                area_name = city
                area_code = city_code
                location_type = 'city'
            elif province_valid:
                area_name = province
                area_code = province_code
                location_type = 'province'
            
            # 如果没有有效的区域信息，跳过
            if not area_name or not area_code:
                skipped_count += 1
                continue
            
            relationship_data.append({
                'company_name': company_name,
                'unified_social_credit_code': unified_social_credit_code if self._is_valid_area_value(unified_social_credit_code) else None,
                'area_name': area_name,
                'area_code': area_code,
                'location_type': location_type,
                'province': province if province_valid else None,
                'province_code': province_code if province_valid else None,
                'city': city if city_valid else None,
                'city_code': city_code if city_valid else None,
                'district': district if district_valid else None,
                'district_code': district_code if district_valid else None,
                'remarks': f"保险经纪公司地理位置关系: {company_name} 位于 {area_name}({area_code})",
                'data_source_node': 'InsuranceBrokerageCompany',
                'relationship_strength': 8,
                'relationship_type': '地理位置关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保险经纪公司区域关系，跳过了 {skipped_count} 个无效记录")
        
        # 按位置类型统计
        location_stats = {}
        for rel in relationship_data:
            loc_type = rel['location_type']
            location_stats[loc_type] = location_stats.get(loc_type, 0) + 1
        
        logger.info(f"匹配统计: {location_stats}")
        
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code进行精确匹配
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询，使用code进行精确匹配
                query = """
                UNWIND $relationships as rel
                MATCH (ibc:InsuranceBrokerageCompany)
                WHERE ibc.name = rel.company_name
                   OR (rel.unified_social_credit_code IS NOT NULL AND ibc.unified_social_credit_code = rel.unified_social_credit_code)
                MATCH (a:Area)
                WHERE a.code = rel.area_code
                MERGE (ibc)-[r:LOCATED_IN]->(a)
                SET r.remarks = rel.remarks,
                    r.location_type = rel.location_type,
                    r.province = rel.province,
                    r.province_code = rel.province_code,
                    r.city = rel.city,
                    r.city_code = rel.city_code,
                    r.district = rel.district,
                    r.district_code = rel.district_code,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self.neo4j_storage.execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险经纪公司区域关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def _is_valid_area_value(self, value: str) -> bool:
        """
        判断区域值是否有效
        
        Args:
            value: 待检查的值
            
        Returns:
            bool: 是否有效
        """
        if not value:
            return False
        
        value_str = str(value).strip().lower()
        invalid_values = ['', 'nan', 'null', 'none', 'n/a', 'na']
        return value_str not in invalid_values
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险经纪公司区域关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            DELETE r
            """
            
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保险经纪公司区域关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险经纪公司区域关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险经纪公司区域关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按位置类型统计
            location_type_query = """
            MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.location_type IS NOT NULL
            RETURN r.location_type as location_type, count(r) as count
            ORDER BY count DESC
            """
            location_type_result = self._safe_execute_query(location_type_query)
            location_type_stats = {record['location_type']: record['count'] for record in location_type_result}
            
            # 按区域统计（Top 10）
            area_query = """
            MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN area.name as area_name, count(r) as company_count
            ORDER BY company_count DESC
            LIMIT 10
            """
            area_result = self._safe_execute_query(area_query)
            area_stats = {record['area_name']: record['company_count'] for record in area_result}
            
            # 数据源统计
            source_query = """
            MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            # 使用基类的标准格式化方法
            additional_stats = {
                "by_location_type": location_type_stats,
                "by_area_name": area_stats,
                "by_data_source_node": source_stats
            }
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险经纪公司区域关系",
                additional_stats=additional_stats
            )
            
        except Exception as e:
            logger.error(f"获取保险经纪公司区域关系统计失败: {str(e)}")
            return self._format_standard_statistics(
                total_count=0,
                relationship_type="保险经纪公司区域关系",
                additional_stats={
                    "by_location_type": {},
                    "by_area_name": {},
                    "by_data_source_node": {}
                }
            )
    
    def search_location_relationships(self, 
                                    company_name: Optional[str] = None,
                                    area_name: Optional[str] = None,
                                    location_type: Optional[str] = None,
                                    province: Optional[str] = None,
                                    city: Optional[str] = None,
                                    district: Optional[str] = None,
                                    include_node_details: bool = True,
                                    limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险经纪公司区域位置关系
        
        Args:
            company_name: 公司名称过滤条件
            area_name: 区域名称过滤条件
            location_type: 位置类型过滤条件（district/city/province）
            province: 省份过滤条件
            city: 城市过滤条件
            district: 区县过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if company_name:
                conditions.append("ibc.name CONTAINS $company_name")
                parameters['company_name'] = company_name
            
            if area_name:
                conditions.append("area.name CONTAINS $area_name")
                parameters['area_name'] = area_name
            
            if location_type:
                conditions.append("r.location_type = $location_type")
                parameters['location_type'] = location_type
            
            if province:
                conditions.append("r.province = $province")
                parameters['province'] = province
            
            if city:
                conditions.append("r.city = $city")
                parameters['city'] = city
            
            if district:
                conditions.append("r.district = $district")
                parameters['district'] = district
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 构建查询语句
            if include_node_details:
                query = f"""
                MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
                {where_clause}
                RETURN r.location_type as location_type,
                       r.province as province, 
                       r.city as city,
                       r.district as district,
                       r.data_source_node as data_source_node,
                       area.name as area_name, 
                       area.code as area_code,
                       ibc.name as company_name, 
                       ibc.unified_social_credit_code as company_code
                ORDER BY ibc.name, area.name
                LIMIT $limit
                """
            else:
                query = f"""
                MATCH (ibc:InsuranceBrokerageCompany)-[r:LOCATED_IN]->(area:Area)
                {where_clause}
                RETURN r.location_type as location_type,
                       r.province as province, 
                       r.city as city,
                       r.district as district,
                       r.data_source_node as data_source_node,
                       area.name as area_name, 
                       area.code as area_code
                ORDER BY ibc.name, area.name
                LIMIT $limit
                """
            
            parameters['limit'] = limit
            
            # 使用基类的安全查询方法
            results = self._safe_execute_query(query, parameters)
            
            # 使用基类的安全记录处理方法
            relationships = []
            for i, record in enumerate(results):
                processed_record = self._handle_search_record_safely(record, i)
                if processed_record:
                    # 只保留需要的字段
                    rel_data = {
                        'location_type': processed_record.get('location_type'),
                        'province': processed_record.get('province'),
                        'city': processed_record.get('city'),
                        'district': processed_record.get('district'),
                        'data_source_node': processed_record.get('data_source_node'),
                        'area_name': processed_record.get('area_name'),
                        'area_code': processed_record.get('area_code')
                    }
                    
                    # 如果需要包含节点详情
                    if include_node_details:
                        rel_data['company_name'] = processed_record.get('company_name')
                        rel_data['company_code'] = processed_record.get('company_code')
                    
                    relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险经纪公司区域关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险经纪公司区域关系失败: {str(e)}")
            return [] 