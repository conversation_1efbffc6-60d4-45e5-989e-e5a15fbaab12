"""
关系服务模块

包含各种节点间关系的创建和管理服务
"""

# 基础关系服务
from .base_relationship_service import BaseRelationshipService

# 区域相关关系服务
from .area_hierarchy_relationship_service import AreaHierarchyRelationshipService
from .insurance_brokerage_company_area_relationship_service import InsuranceBrokerageCompanyAreaRelationshipService
from .insurance_claims_area_relationship_service import InsuranceClaimsAreaRelationshipService
from .insurance_sales_channel_area_relationship_service import InsuranceSalesChannelAreaRelationshipService
from .policy_company_area_relationship_service import PolicyCompanyAreaRelationshipService

# 联系方式相关关系服务 - 代理人
from .insurance_agent_email_relationship_service import InsuranceAgentEmailRelationshipService
from .insurance_agent_phone_relationship_service import InsuranceAgentPhoneRelationshipService

# 联系方式相关关系服务 - 商务拓展人员
from .insurance_bd_email_relationship_service import InsuranceBDEmailRelationshipService
from .insurance_bd_phone_relationship_service import InsuranceBDPhoneRelationshipService

# 联系方式相关关系服务 - 市场运营人员
from .insurance_mo_email_relationship_service import InsuranceMOEmailRelationshipService
from .insurance_mo_phone_relationship_service import InsuranceMOPhoneRelationshipService

# 联系方式相关关系服务 - 保险公司
from .insurance_company_email_relationship_service import InsuranceCompanyEmailRelationshipService
from .insurance_company_phone_relationship_service import InsuranceCompanyPhoneRelationshipService
from .insurance_company_additional_email_relationship_service import InsuranceCompanyAdditionalEmailRelationshipService
from .insurance_company_additional_phone_relationship_service import InsuranceCompanyAdditionalPhoneRelationshipService

# 联系方式相关关系服务 - 保险经纪公司
from .insurance_brokerage_company_email_relationship_service import InsuranceBrokerageCompanyEmailRelationshipService
from .insurance_brokerage_company_phone_relationship_service import InsuranceBrokerageCompanyPhoneRelationshipService
from .insurance_brokerage_company_additional_email_relationship_service import InsuranceBrokerageCompanyAdditionalEmailRelationshipService
from .insurance_brokerage_company_additional_phone_relationship_service import InsuranceBrokerageCompanyAdditionalPhoneRelationshipService

# 联系方式相关关系服务 - 保单公司
from .policy_company_email_relationship_service import PolicyCompanyEmailRelationshipService
from .policy_company_phone_relationship_service import PolicyCompanyPhoneRelationshipService
from .policy_company_additional_email_relationship_service import PolicyCompanyAdditionalEmailRelationshipService
from .policy_company_additional_phone_relationship_service import PolicyCompanyAdditionalPhoneRelationshipService

# 业务相关关系服务
from .insurance_claims_company_relationship_service import InsuranceClaimsCompanyRelationshipService
from .insurance_claims_company_insure_relationship_service import InsuranceClaimsCompanyInsureRelationshipService
from .insurance_claims_company_insured_relationship_service import InsuranceClaimsCompanyInsuredRelationshipService
from .insurance_claims_insured_person_relationship_service import InsuranceClaimsInsuredPersonRelationshipService
from .insurance_claims_policy_relationship_service import InsuranceClaimsPolicyRelationshipService

# 保单相关关系服务
from .policy_covered_company_relationship_service import PolicyCoveredCompanyRelationshipService
from .policy_insurance_company_relationship_service import PolicyInsuranceCompanyRelationshipService
from .policy_insurer_company_relationship_service import PolicyInsurerCompanyRelationshipService  
from .policy_product_relationship_service import PolicyProductRelationshipService
from .policy_sale_person_relationship_service import PolicySalePersonRelationshipService
from .policy_sales_channel_relationship_service import PolicySalesChannelRelationshipService
from .policy_industry_relationship_service import PolicyIndustryRelationshipService

# 被保险人相关关系服务
from .insured_person_policy_relationship_service import InsuredPersonPolicyRelationshipService

# 产品相关关系服务
from .insurance_product_company_relationship_service import InsuranceProductCompanyRelationshipService

# 销售渠道相关关系服务
from .insurance_sales_channel_agent_relationship_service import InsuranceSalesChannelAgentRelationshipService
from .insurance_sales_channel_bd_relationship_service import InsuranceSalesChannelBDRelationshipService
from .insurance_sales_channel_hierarchy_relationship_service import InsuranceSalesChannelHierarchyRelationshipService
from .insurance_sales_channel_mo_relationship_service import InsuranceSalesChannelMORelationshipService

# 行业相关关系服务
from .national_standard_industry_hierarchy_relationship_service import NationalStandardIndustryHierarchyRelationshipService
from .organization_industry_relationship_service import OrganizationIndustryRelationshipService

# 特殊名单相关关系服务
from .special_list_organization_relationship_service import SpecialListOrganizationRelationshipService


__all__ = [
    # 基础关系服务
    "BaseRelationshipService",
    
    # 区域相关关系服务
    "AreaHierarchyRelationshipService",
    "InsuranceBrokerageCompanyAreaRelationshipService",
    "InsuranceClaimsAreaRelationshipService", 
    "InsuranceSalesChannelAreaRelationshipService",
    "PolicyCompanyAreaRelationshipService",
    
    # 联系方式相关关系服务
    "InsuranceAgentEmailRelationshipService",
    "InsuranceAgentPhoneRelationshipService",
    "InsuranceBDEmailRelationshipService",
    "InsuranceBDPhoneRelationshipService",
    "InsuranceMOEmailRelationshipService",
    "InsuranceMOPhoneRelationshipService",
    "InsuranceCompanyEmailRelationshipService",
    "InsuranceCompanyPhoneRelationshipService",
    "InsuranceCompanyAdditionalEmailRelationshipService",
    "InsuranceCompanyAdditionalPhoneRelationshipService",
    "InsuranceBrokerageCompanyEmailRelationshipService",
    "InsuranceBrokerageCompanyPhoneRelationshipService",
    "InsuranceBrokerageCompanyAdditionalEmailRelationshipService",
    "InsuranceBrokerageCompanyAdditionalPhoneRelationshipService",
    "PolicyCompanyEmailRelationshipService",
    "PolicyCompanyPhoneRelationshipService",
    "PolicyCompanyAdditionalEmailRelationshipService",
    "PolicyCompanyAdditionalPhoneRelationshipService",
    
    # 业务相关关系服务
    "InsuranceClaimsCompanyRelationshipService",
    "InsuranceClaimsCompanyInsureRelationshipService",
    "InsuranceClaimsCompanyInsuredRelationshipService",
    "InsuranceClaimsInsuredPersonRelationshipService",
    "InsuranceClaimsPolicyRelationshipService",
    
    # 保单相关关系服务
    "PolicyCoveredCompanyRelationshipService",
    "PolicyInsuranceCompanyRelationshipService",
    "PolicyInsurerCompanyRelationshipService",
    "PolicyProductRelationshipService",
    "PolicySalePersonRelationshipService",
    "PolicySalesChannelRelationshipService",
    "PolicyIndustryRelationshipService",
    
    # 被保险人相关关系服务
    "InsuredPersonPolicyRelationshipService",
    
    # 产品相关关系服务
    "InsuranceProductCompanyRelationshipService",
    
    # 销售渠道相关关系服务
    "InsuranceSalesChannelAgentRelationshipService",
    "InsuranceSalesChannelBDRelationshipService",
    "InsuranceSalesChannelHierarchyRelationshipService",
    "InsuranceSalesChannelMORelationshipService",
    
    # 行业相关关系服务
    "NationalStandardIndustryHierarchyRelationshipService",
    "OrganizationIndustryRelationshipService",
    
    # 特殊名单相关关系服务
    "SpecialListOrganizationRelationshipService",
] 