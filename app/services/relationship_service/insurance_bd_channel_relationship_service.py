"""
保险BD人员销售渠道关系服务

专门负责 InsuranceBDPerson 节点与 InsuranceSalesChannel 节点之间关系的创建和管理
基于 InsuranceBDPerson 的 channel_id 属性关联对应的销售渠道节点
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceBDChannelRelationshipService(BaseRelationshipService):
    """保险BD人员销售渠道关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险BD人员数据，基于channel_id创建与销售渠道的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始创建保险BD人员与销售渠道关系")
            
            # 1. 从Neo4j查询保险BD人员-销售渠道匹配数据
            bd_channel_data = self._query_bd_channel_data_from_neo4j(limit)
            if not bd_channel_data:
                logger.warning("未找到有效的保险BD人员-销售渠道匹配数据")
                return True
            
            # 2. 创建关系
            success = self._create_bd_channel_relationships(bd_channel_data)
            
            if success:
                logger.info(f"成功处理 {len(bd_channel_data)} 个保险BD人员销售渠道关系")
                return True
            else:
                logger.error("创建保险BD人员销售渠道关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保险BD人员销售渠道关系失败: {str(e)}")
            return False
    
    def _query_bd_channel_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险BD人员与销售渠道的匹配数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: BD人员-销售渠道匹配数据列表
        """
        try:
            # 构建查询条件
            conditions = ["bd.channel_id IS NOT NULL", "bd.channel_id <> ''", "bd.channel_id <> 'nan'"]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (bd:InsuranceBDPerson)
            WHERE {where_clause}
            MATCH (channel:InsuranceSalesChannel {{code: bd.channel_id}})
            RETURN bd.code as bd_code,
                   bd.name as bd_name,
                   bd.channel_id as channel_id,
                   channel.code as channel_code,
                   channel.name as channel_name
            ORDER BY bd.code
            {limit_clause}
            """
            
            logger.info(f"查询保险BD人员-销售渠道匹配数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤和验证数据
            valid_data = []
            for record in results:
                bd_code = str(record.get('bd_code', '')).strip()
                channel_id = str(record.get('channel_id', '')).strip()
                channel_code = str(record.get('channel_code', '')).strip()
                
                if bd_code and channel_id and channel_code and channel_id == channel_code:
                    valid_data.append({
                        'bd_code': bd_code,
                        'bd_name': record.get('bd_name', ''),
                        'channel_id': channel_id,
                        'channel_code': channel_code,
                        'channel_name': record.get('channel_name', '')
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保险BD人员-销售渠道匹配记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险BD人员销售渠道数据失败: {str(e)}")
            return []
    
    def _create_bd_channel_relationships(self, bd_channel_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险BD人员与销售渠道的关系
        
        Args:
            bd_channel_data: BD人员-销售渠道数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(bd_channel_data), batch_size):
                batch_data = bd_channel_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (bd:InsuranceBDPerson {code: rel.bd_code})
                MATCH (channel:InsuranceSalesChannel {code: rel.channel_code})
                MERGE (bd)-[r:BELONGS_TO_CHANNEL]->(channel)
                SET r.relationship_type = 'BELONGS_TO_CHANNEL',
                    r.relationship_status = 'active',
                    r.relationship_strength = 8,
                    r.data_source_node = 'InsuranceBDPerson',
                    r.remarks = '保险BD人员所属销售渠道: ' + rel.bd_name + ' -> ' + rel.channel_name,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险BD人员销售渠道关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险BD人员销售渠道关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险BD人员销售渠道关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险BD人员销售渠道关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险BD人员销售渠道关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险BD人员销售渠道关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有销售渠道的BD人员数量
            bd_count_query = """
            MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
            RETURN count(DISTINCT bd) as bd_count
            """
            bd_result = self._safe_execute_query(bd_count_query)
            bd_count = bd_result[0]['bd_count'] if bd_result else 0
            
            # 关联的销售渠道数量
            channel_count_query = """
            MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
            RETURN count(DISTINCT channel) as channel_count
            """
            channel_result = self._safe_execute_query(channel_count_query)
            channel_count = channel_result[0]['channel_count'] if channel_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险BD人员销售渠道关系",
                additional_stats={
                    "bd_with_channel": bd_count,
                    "unique_channels": channel_count,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险BD人员销售渠道关系统计失败: {str(e)}")
            return {}
    
    def search_bd_channel_relationships(self, 
                                      bd_code: Optional[str] = None,
                                      bd_name: Optional[str] = None,
                                      channel_code: Optional[str] = None,
                                      channel_name: Optional[str] = None,
                                      include_node_details: bool = True,
                                      limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险BD人员销售渠道关系
        
        Args:
            bd_code: BD人员编码过滤条件
            bd_name: BD人员姓名过滤条件
            channel_code: 销售渠道编码过滤条件
            channel_name: 销售渠道名称过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if bd_code:
                conditions.append("bd.code CONTAINS $bd_code")
                parameters['bd_code'] = bd_code
            
            if bd_name:
                conditions.append("bd.name CONTAINS $bd_name")
                parameters['bd_name'] = bd_name
            
            if channel_code:
                conditions.append("channel.code CONTAINS $channel_code")
                parameters['channel_code'] = channel_code
            
            if channel_name:
                conditions.append("channel.name CONTAINS $channel_name")
                parameters['channel_name'] = channel_name
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r,
                       {{code: bd.code, name: bd.name, business_id: bd.business_id, channel_id: bd.channel_id}} as bd_info,
                       {{code: channel.code, name: channel.name, business_id: channel.business_id}} as channel_info
                ORDER BY bd.code, channel.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (bd:InsuranceBDPerson)-[r:BELONGS_TO_CHANNEL]->(channel:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r, bd.code as bd_code, channel.code as channel_code
                ORDER BY bd.code, channel.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'BELONGS_TO_CHANNEL'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['bd'] = record['bd_info']
                    rel_data['channel'] = record['channel_info']
                else:
                    rel_data['bd_code'] = record['bd_code']
                    rel_data['channel_code'] = record['channel_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险BD人员销售渠道关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险BD人员销售渠道关系失败: {str(e)}")
            return [] 