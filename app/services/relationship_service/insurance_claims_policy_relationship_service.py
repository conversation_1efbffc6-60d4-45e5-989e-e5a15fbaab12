"""
保险理赔保单关系服务

专门负责保险理赔 InsuranceClaims 节点与保单 Policy 节点之间关系的创建和管理
从Neo4j查询保险理赔的保单信息，匹配对应的保单节点并建立关系
通过 policy_code 建立关联关系
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsPolicyRelationshipService(BaseRelationshipService):
    """保险理赔保单关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险理赔数据，匹配保单节点并建立关系

        Args:
            limit: 限制处理的记录数（用于测试）

        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险理赔保单关系")

            # 1. 从Neo4j查询保险理赔保单数据
            claims_data = self._query_claims_policy_data_from_neo4j(limit)
            if not claims_data:
                logger.warning("未找到有效的保险理赔保单数据")
                return True

            # 2. 构建关系数据
            relationship_data = self._build_relationship_data_from_neo4j(claims_data)

            if not relationship_data:
                logger.warning("没有有效的保险理赔保单关系数据需要创建")
                return True

            # 3. 批量创建关系
            success = self._create_claims_policy_relationships(relationship_data)

            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险理赔保单关系")
                return True
            else:
                logger.error("批量创建保险理赔保单关系失败")
                return False

        except Exception as e:
            logger.error(f"从Neo4j创建保险理赔保单关系失败: {str(e)}")
            return False
    
    def _query_claims_policy_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险理赔的保单数据

        Args:
            limit: 记录数限制

        Returns:
            List[Dict[str, Any]]: 保险理赔保单数据列表
        """
        try:
            # 构建查询条件，查询有保单信息的保险理赔
            conditions = [
                "claims.policy_code IS NOT NULL",
                "claims.policy_code <> ''"
            ]
            where_clause = " AND " + " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""

            query = f"""
            MATCH (claims:InsuranceClaims)
            WHERE claims.case_no IS NOT NULL AND claims.case_no <> ''
            {where_clause}
            RETURN claims.case_no as case_no,
                   claims.policy_code as policy_code,
                   claims.insured_name as insured_name,
                   claims.insure_name as insure_name,
                   claims.insurance_name as insurance_name,
                   claims.report_date as report_date,
                   claims.risk_date as risk_date,
                   claims.case_status as case_status,
                   claims.compensate_amt as compensate_amt,
                   claims.business_id as claims_business_id
            ORDER BY claims.case_no
            {limit_clause}
            """

            logger.info(f"查询保险理赔保单数据，条件: {where_clause}")
            results = self._safe_execute_query(query)

            if not results:
                return []

            # 过滤掉无效的保单数据
            valid_data = []
            for record in results:
                case_no = str(record.get('case_no', '')).strip()
                policy_code = str(record.get('policy_code', '')).strip()

                if case_no and policy_code and case_no != 'nan' and policy_code != 'nan':
                    valid_data.append({
                        'case_no': case_no,
                        'policy_code': policy_code,
                        'insured_name': record.get('insured_name', ''),
                        'insure_name': record.get('insure_name', ''),
                        'insurance_name': record.get('insurance_name', ''),
                        'report_date': record.get('report_date'),
                        'risk_date': record.get('risk_date'),
                        'case_status': record.get('case_status', ''),
                        'compensate_amt': record.get('compensate_amt', 0),
                        'claims_business_id': record.get('claims_business_id', case_no)
                    })

            logger.info(f"找到 {len(valid_data)} 个有效的保险理赔保单记录")
            return valid_data

        except Exception as e:
            logger.error(f"查询Neo4j保险理赔保单数据失败: {str(e)}")
            return []
    
    def _build_relationship_data_from_neo4j(self, claims_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从Neo4j保险理赔数据构建与保单的关系数据

        Args:
            claims_data: Neo4j保险理赔数据列表

        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0

        for claims in claims_data:
            case_no = claims.get('case_no', '')
            policy_code = claims.get('policy_code', '')

            # 跳过无效数据
            if not case_no or not policy_code:
                skipped_count += 1
                continue

            # 检查保单节点是否存在
            if not self._check_policy_exists(policy_code):
                skipped_count += 1
                continue

            # 获取其他关系属性
            insured_name = str(claims.get('insured_name', '')).strip()
            insure_name = str(claims.get('insure_name', '')).strip()
            insurance_name = str(claims.get('insurance_name', '')).strip()
            case_status = str(claims.get('case_status', '')).strip()

            # 处理金额字段
            compensate_amt = claims.get('compensate_amt', 0)
            try:
                compensate_amt = float(compensate_amt) if compensate_amt else 0.0
            except (ValueError, TypeError):
                compensate_amt = 0.0

            # 处理日期字段
            report_date = claims.get('report_date')
            risk_date = claims.get('risk_date')

            relationship_data.append({
                'case_no': case_no,
                'policy_code': policy_code,
                'insured_name': insured_name if insured_name and insured_name != 'nan' else None,
                'insure_name': insure_name if insure_name and insure_name != 'nan' else None,
                'insurance_name': insurance_name if insurance_name and insurance_name != 'nan' else None,
                'report_date': report_date if report_date else None,
                'risk_date': risk_date if risk_date else None,
                'case_status': case_status if case_status and case_status != 'nan' else None,
                'compensate_amt': compensate_amt,
                'remarks': f"保险理赔保单关系: 案件{case_no} 关联保单{policy_code}",
                'data_source_node': 'InsuranceClaims',
                'relationship_strength': 10,  # 理赔与保单的关系强度最高
                'relationship_type': '理赔保单关系'
            })

        logger.info(f"构建了 {len(relationship_data)} 个有效的保险理赔保单关系，跳过了 {skipped_count} 个无效记录")

        return relationship_data

    def _check_policy_exists(self, policy_code: str) -> bool:
        """
        检查保单节点是否存在

        Args:
            policy_code: 保单编码

        Returns:
            bool: 是否存在
        """
        try:
            query = """
            MATCH (policy:Policy)
            WHERE policy.code = $policy_code
            RETURN count(policy) as count
            """

            result = self._safe_execute_query(query, {'policy_code': policy_code})
            return result and result[0]['count'] > 0

        except Exception as e:
            logger.error(f"检查保单节点存在性失败: {str(e)}")
            return False
    
    def _create_claims_policy_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险理赔与保单的关系

        Args:
            relationship_data: 关系数据列表

        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0

            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]

                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (claims:InsuranceClaims {case_no: rel.case_no})
                MATCH (policy:Policy {code: rel.policy_code})
                MERGE (claims)-[r:BELONGS_TO_POLICY]->(policy)
                SET r.relationship_type = 'BELONGS_TO_POLICY',
                    r.relationship_status = 'active',
                    r.relationship_strength = rel.relationship_strength,
                    r.insured_name = rel.insured_name,
                    r.insure_name = rel.insure_name,
                    r.insurance_name = rel.insurance_name,
                    r.report_date = rel.report_date,
                    r.risk_date = rel.risk_date,
                    r.case_status = rel.case_status,
                    r.compensate_amt = rel.compensate_amt,
                    r.data_source_node = rel.data_source_node,
                    r.remarks = rel.remarks,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """

                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")

            logger.info(f"总共成功创建 {success_count} 个保险理赔保单关系")
            return success_count > 0

        except Exception as e:
            logger.error(f"创建保险理赔保单关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险理赔保单关系

        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            DELETE r
            """

            self._safe_execute_query(query)
            logger.info("已删除所有保险理赔保单关系")
            return True

        except Exception as e:
            logger.error(f"删除保险理赔保单关系失败: {str(e)}")
            return False

    def cleanup_duplicate_relationships(self) -> int:
        """
        清理重复的保险理赔保单关系
        对于相同的理赔-保单对，保留最早创建的关系，删除其他重复关系

        Returns:
            int: 删除的重复关系数量
        """
        try:
            # 查找重复的关系
            find_duplicates_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            WITH claims, policy, collect(r) as rels
            WHERE size(rels) > 1
            RETURN claims.case_no as case_no, policy.code as policy_code, rels
            """

            duplicates_result = self._safe_execute_query(find_duplicates_query)

            if not duplicates_result:
                logger.info("没有发现重复的保险理赔保单关系")
                return 0

            total_deleted = 0

            for record in duplicates_result:
                case_no = record['case_no']
                policy_code = record['policy_code']
                rels = record['rels']

                if len(rels) <= 1:
                    continue

                logger.info(f"发现重复的关系，理赔: {case_no}，保单: {policy_code}，共 {len(rels)} 个")

                # 保留最早创建的关系，删除其他
                cleanup_query = """
                MATCH (claims:InsuranceClaims {case_no: $case_no})-[r:BELONGS_TO_POLICY]->(policy:Policy {code: $policy_code})
                WITH r
                ORDER BY COALESCE(r.created_at, datetime()) ASC
                WITH collect(r) as rels
                WHERE size(rels) > 1
                UNWIND rels[1..] as duplicate_rel
                DELETE duplicate_rel
                RETURN count(duplicate_rel) as deleted_count
                """

                cleanup_result = self._safe_execute_query(cleanup_query, {
                    'case_no': case_no,
                    'policy_code': policy_code
                })
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count

                logger.info(f"清理了 {deleted_count} 个重复关系，理赔: {case_no}，保单: {policy_code}")

            logger.info(f"总共清理了 {total_deleted} 个重复的保险理赔保单关系")
            return total_deleted

        except Exception as e:
            logger.error(f"清理重复关系失败: {str(e)}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔保单关系统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0

            # 有保单关系的保险理赔数量
            claims_with_policy_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            RETURN count(DISTINCT claims) as claims_count
            """
            claims_result = self._safe_execute_query(claims_with_policy_query)
            claims_count = claims_result[0]['claims_count'] if claims_result else 0

            # 按案件状态统计
            status_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            WHERE r.case_status IS NOT NULL
            RETURN r.case_status as case_status, count(r) as count
            ORDER BY count DESC
            """
            status_result = self._safe_execute_query(status_query)
            status_stats = {record['case_status']: record['count'] for record in status_result}

            # 按保险公司统计（Top 10）
            insurance_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            WHERE r.insurance_name IS NOT NULL
            RETURN r.insurance_name as insurance_name, count(r) as claims_count
            ORDER BY claims_count DESC
            LIMIT 10
            """
            insurance_result = self._safe_execute_query(insurance_query)
            insurance_stats = {record['insurance_name']: record['claims_count'] for record in insurance_result}

            # 赔付金额统计
            amount_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            WHERE r.compensate_amt IS NOT NULL AND r.compensate_amt > 0
            RETURN avg(r.compensate_amt) as avg_amount,
                   max(r.compensate_amt) as max_amount,
                   min(r.compensate_amt) as min_amount,
                   sum(r.compensate_amt) as total_amount,
                   count(r) as paid_claims_count
            """
            amount_result = self._safe_execute_query(amount_query)
            amount_stats = amount_result[0] if amount_result else {}

            # 数据源统计
            source_query = """
            MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险理赔保单关系",
                additional_stats={
                    "claims_with_policy": claims_count,
                    "by_case_status": status_stats,
                    "by_insurance_company": insurance_stats,
                    "amount_statistics": amount_stats,
                    "by_data_source_node": source_stats,
                }
            )

        except Exception as e:
            logger.error(f"获取保险理赔保单关系统计失败: {str(e)}")
            return {}
    
    def search_claims_policy_relationships(self,
                                         case_no: Optional[str] = None,
                                         policy_code: Optional[str] = None,
                                         insurance_name: Optional[str] = None,
                                         case_status: Optional[str] = None,
                                         insured_name: Optional[str] = None,
                                         include_node_details: bool = True,
                                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔保单关系

        Args:
            case_no: 案件号过滤条件
            policy_code: 保单号过滤条件
            insurance_name: 保险公司名称过滤条件
            case_status: 案件状态过滤条件
            insured_name: 被保险人姓名过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制

        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}

            if case_no:
                conditions.append("claims.case_no CONTAINS $case_no")
                parameters['case_no'] = case_no

            if policy_code:
                conditions.append("policy.code CONTAINS $policy_code")
                parameters['policy_code'] = policy_code

            if insurance_name:
                conditions.append("r.insurance_name CONTAINS $insurance_name")
                parameters['insurance_name'] = insurance_name

            if case_status:
                conditions.append("r.case_status = $case_status")
                parameters['case_status'] = case_status

            if insured_name:
                conditions.append("r.insured_name CONTAINS $insured_name")
                parameters['insured_name'] = insured_name

            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
                WHERE {where_clause}
                RETURN r,
                       {{case_no: claims.case_no, name: claims.name, business_id: claims.business_id}} as claims_info,
                       {{code: policy.code, name: policy.name, business_id: policy.business_id}} as policy_info
                ORDER BY claims.case_no, policy.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(policy:Policy)
                WHERE {where_clause}
                RETURN r, claims.case_no as case_no, policy.code as policy_code
                ORDER BY claims.case_no, policy.code
                LIMIT {limit}
                """

            results = self._safe_execute_query(query, parameters)

            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'BELONGS_TO_POLICY'
                }

                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass

                if include_node_details:
                    rel_data['claims'] = record['claims_info']
                    rel_data['policy'] = record['policy_info']
                else:
                    rel_data['case_no'] = record['case_no']
                    rel_data['policy_code'] = record['policy_code']

                relationships.append(rel_data)

            logger.info(f"搜索到 {len(relationships)} 个保险理赔保单关系")
            return relationships

        except Exception as e:
            logger.error(f"搜索保险理赔保单关系失败: {str(e)}")
            return []