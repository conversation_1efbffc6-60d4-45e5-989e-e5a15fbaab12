"""
保险理赔保单关系服务

专门负责保险理赔 InsuranceClaims 节点与保单 Policy 节点之间关系的创建和管理
通过 policy_code 建立关联关系
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsPolicyRelationshipService(BaseRelationshipService):
    """保险理赔保单关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保险理赔与保单的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保险理赔保单关系")
            
            # 1. 从Hive查询保险理赔保单数据
            hive_data = self._query_claims_policy_data_from_hive(limit, where_conditions)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保险理赔保单关系数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保险理赔保单关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险理赔保单关系")
                return True
            else:
                logger.error("批量创建保险理赔保单关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保险理赔保单关系失败: {str(e)}")
            return False
    
    def _query_claims_policy_data_from_hive(self, 
                                          limit: Optional[int] = None,
                                          where_conditions: Optional[str] = None) -> pd.DataFrame:
        """
        从Hive查询保险理赔保单数据
        
        Args:
            limit: 限制查询数量
            where_conditions: 额外的WHERE条件
            
        Returns:
            pd.DataFrame: 保险理赔保单数据
        """
        try:
            limit_clause = f"LIMIT {limit}" if limit else ""
            where_clause = f"AND {where_conditions}" if where_conditions else ""
            
            query = f"""
            SELECT fcd.case_no,          -- 报案号
                   fcd.policy_code,      -- 保单号
                   fcd.insured_name,     -- 被保险人姓名
                   fcd.insure_name,      -- 投保人姓名
                   fcd.insurance_name,   -- 保司名称
                   fcd.report_date,      -- 报案时间
                   fcd.risk_date,        -- 出险时间
                   fcd.case_status,      -- 案件状态
                   fcd.compensate_amt    -- 赔付金额
            FROM dwtdb.dwt_fnol_claim_detail fcd
                     LEFT JOIN dwddb.dwd_b_policy_central pc ON fcd.policy_code = pc.policy_code
            WHERE pc.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
              AND fcd.case_no IS NOT NULL
              AND fcd.case_no != ''
              AND fcd.policy_code IS NOT NULL
              AND fcd.policy_code != ''
              {where_clause}
            ORDER BY fcd.case_no DESC
            {limit_clause}
            """
            
            logger.info(f"查询保险理赔保单数据")
            data = self.query_hive_data(query)
            logger.info(f"从Hive查询到 {len(data)} 条保险理赔保单记录")
            return data
            
        except Exception as e:
            logger.error(f"从Hive查询保险理赔保单数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保险理赔与保单的关系数据
        
        Args:
            hive_data: Hive保险理赔保单数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0
        
        for index, row in hive_data.iterrows():
            case_no = str(row.get('case_no', '')).strip()
            policy_code = str(row.get('policy_code', '')).strip()
            
            # 跳过无效数据
            if not case_no or case_no == 'nan' or not policy_code or policy_code == 'nan':
                skipped_count += 1
                continue
            
            # 获取其他关系属性
            insured_name = str(row.get('insured_name', '')).strip()
            insure_name = str(row.get('insure_name', '')).strip()
            insurance_name = str(row.get('insurance_name', '')).strip()
            case_status = str(row.get('case_status', '')).strip()
            
            # 处理金额字段
            compensate_amt = row.get('compensate_amt')
            if pd.isna(compensate_amt) or compensate_amt == '':
                compensate_amt = 0.0
            else:
                try:
                    compensate_amt = float(compensate_amt)
                except (ValueError, TypeError):
                    compensate_amt = 0.0
            
            # 处理日期字段
            report_date = row.get('report_date')
            risk_date = row.get('risk_date')
            
            relationship_data.append({
                'case_no': case_no,
                'policy_code': policy_code,
                'insured_name': insured_name if insured_name != 'nan' else None,
                'insure_name': insure_name if insure_name != 'nan' else None,
                'insurance_name': insurance_name if insurance_name != 'nan' else None,
                'report_date': report_date if pd.notna(report_date) else None,
                'risk_date': risk_date if pd.notna(risk_date) else None,
                'case_status': case_status if case_status != 'nan' else None,
                'compensate_amt': compensate_amt,
                'remarks': f"保险理赔保单关系: 案件{case_no} 关联保单{policy_code}",
                'data_source_node': 'InsuranceClaims',
                'relationship_strength': 10,  # 理赔与保单的关系强度最高
                'relationship_type': '理赔保单关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保险理赔保单关系，跳过了 {skipped_count} 个无效记录")
        
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过policy_code进行精确匹配
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询，使用policy_code进行精确匹配
                query = """
                UNWIND $relationships as rel
                MATCH (ic:InsuranceClaims)
                WHERE ic.case_no = rel.case_no
                MATCH (p:Policy)
                WHERE p.code = rel.policy_code
                MERGE (ic)-[r:BELONGS_TO_POLICY]->(p)
                SET r.insured_name = rel.insured_name,
                    r.insure_name = rel.insure_name,
                    r.insurance_name = rel.insurance_name,
                    r.report_date = rel.report_date,
                    r.risk_date = rel.risk_date,
                    r.case_status = rel.case_status,
                    r.compensate_amt = rel.compensate_amt,
                    r.remarks = rel.remarks,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险理赔保单关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险理赔保单关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险理赔保单关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险理赔保单关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔保单关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按案件状态统计
            status_query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            WHERE r.case_status IS NOT NULL
            RETURN r.case_status as case_status, count(r) as count
            ORDER BY count DESC
            """
            status_result = self._safe_execute_query(status_query)
            status_stats = {record['case_status']: record['count'] for record in status_result}
            
            # 按保险公司统计（Top 10）
            insurance_query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            WHERE r.insurance_name IS NOT NULL
            RETURN r.insurance_name as insurance_name, count(r) as claims_count
            ORDER BY claims_count DESC
            LIMIT 10
            """
            insurance_result = self._safe_execute_query(insurance_query)
            insurance_stats = {record['insurance_name']: record['claims_count'] for record in insurance_result}
            
            # 赔付金额统计
            amount_query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            WHERE r.compensate_amt IS NOT NULL AND r.compensate_amt > 0
            RETURN avg(r.compensate_amt) as avg_amount,
                   max(r.compensate_amt) as max_amount,
                   min(r.compensate_amt) as min_amount,
                   sum(r.compensate_amt) as total_amount,
                   count(r) as paid_claims_count
            """
            amount_result = self._safe_execute_query(amount_query)
            amount_stats = amount_result[0] if amount_result else {}
            
            # 数据源统计
            source_query = """
            MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            # 使用基类的标准格式化方法
            additional_stats = {
                "by_case_status": status_stats,
                "by_insurance_company": insurance_stats,
                "amount_statistics": amount_stats,
                "by_data_source_node": source_stats
            }
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险理赔保单关系",
                additional_stats=additional_stats
            )
            
        except Exception as e:
            logger.error(f"获取保险理赔保单关系统计失败: {str(e)}")
            return self._format_standard_statistics(
                total_count=0,
                relationship_type="保险理赔保单关系",
                additional_stats={
                    "by_case_status": {},
                    "by_insurance_company": {},
                    "amount_statistics": {},
                    "by_data_source_node": {}
                }
            )
    
    def search_claims_policy_relationships(self, 
                                         case_no: Optional[str] = None,
                                         policy_code: Optional[str] = None,
                                         insurance_name: Optional[str] = None,
                                         case_status: Optional[str] = None,
                                         insured_name: Optional[str] = None,
                                         include_node_details: bool = True,
                                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔保单关系
        
        Args:
            case_no: 案件号过滤条件
            policy_code: 保单号过滤条件
            insurance_name: 保险公司名称过滤条件
            case_status: 案件状态过滤条件
            insured_name: 被保险人姓名过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if case_no:
                conditions.append("ic.case_no CONTAINS $case_no")
                parameters['case_no'] = case_no
            
            if policy_code:
                conditions.append("p.code CONTAINS $policy_code")
                parameters['policy_code'] = policy_code
            
            if insurance_name:
                conditions.append("r.insurance_name CONTAINS $insurance_name")
                parameters['insurance_name'] = insurance_name
            
            if case_status:
                conditions.append("r.case_status = $case_status")
                parameters['case_status'] = case_status
            
            if insured_name:
                conditions.append("r.insured_name CONTAINS $insured_name")
                parameters['insured_name'] = insured_name
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 构建查询语句
            if include_node_details:
                query = f"""
                MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
                {where_clause}
                RETURN r.insured_name as insured_name,
                       r.insure_name as insure_name,
                       r.insurance_name as insurance_name,
                       r.case_status as case_status,
                       r.compensate_amt as compensate_amt,
                       r.report_date as report_date,
                       r.risk_date as risk_date,
                       r.data_source_node as data_source_node,
                       r.relationship_type as relationship_type,
                       ic.case_no as case_no,
                       p.code as policy_code,
                       p.name as policy_name
                ORDER BY ic.case_no, p.code
                LIMIT $limit
                """
            else:
                query = f"""
                MATCH (ic:InsuranceClaims)-[r:BELONGS_TO_POLICY]->(p:Policy)
                {where_clause}
                RETURN r.insured_name as insured_name,
                       r.insure_name as insure_name,
                       r.insurance_name as insurance_name,
                       r.case_status as case_status,
                       r.compensate_amt as compensate_amt,
                       r.data_source_node as data_source_node,
                       r.relationship_type as relationship_type
                ORDER BY ic.case_no, p.code
                LIMIT $limit
                """
            
            parameters['limit'] = limit
            
            # 使用基类的安全查询方法
            results = self._safe_execute_query(query, parameters)
            
            # 使用基类的安全记录处理方法
            relationships = []
            for i, record in enumerate(results):
                processed_record = self._handle_search_record_safely(record, i)
                if processed_record:
                    # 只保留需要的字段
                    rel_data = {
                        'insured_name': processed_record.get('insured_name'),
                        'insure_name': processed_record.get('insure_name'),
                        'insurance_name': processed_record.get('insurance_name'),
                        'case_status': processed_record.get('case_status'),
                        'compensate_amt': processed_record.get('compensate_amt'),
                        'report_date': processed_record.get('report_date'),
                        'risk_date': processed_record.get('risk_date'),
                        'data_source_node': processed_record.get('data_source_node'),
                        'relationship_type': processed_record.get('relationship_type')
                    }
                    
                    # 如果需要包含节点详情
                    if include_node_details:
                        rel_data['case_no'] = processed_record.get('case_no')
                        rel_data['policy_code'] = processed_record.get('policy_code')
                        rel_data['policy_name'] = processed_record.get('policy_name')
                    
                    relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险理赔保单关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险理赔保单关系失败: {str(e)}")
            return [] 