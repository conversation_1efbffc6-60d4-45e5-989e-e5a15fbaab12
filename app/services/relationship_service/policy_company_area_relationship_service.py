"""
保单企业区域关系服务

专门负责保单企业 PolicyCompany 节点与区域 Area 节点之间关系的创建和管理
按优先级顺序匹配: district -> city -> province
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyCompanyAreaRelationshipService(BaseRelationshipService):
    """保单企业区域关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None,
                                     custom_day: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保单企业与区域的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期（格式：YYYY-MM-DD），默认使用昨天
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保单企业与区域关系")
            
            # 1. 从Hive查询保单企业区域数据
            hive_data = self._query_policy_company_area_data_from_hive(limit, where_conditions, custom_day)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保单企业区域数据")
                return True
            
            # 2. 直接构建关系数据（假设节点都已存在）
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保单企业区域关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保单企业区域关系")
                return True
            else:
                logger.error("批量创建保单企业区域关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保单企业区域关系失败: {str(e)}")
            return False
    
    def _build_query(self, 
                    limit: Optional[int] = None, 
                    offset: int = 0,
                    where_conditions: Optional[str] = None,
                    custom_day: Optional[str] = None) -> str:
        """
        构建保单企业区域查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量（为将来分页支持预留）
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期，默认使用昨天
            
        Returns:
            str: SQL查询语句
        """
        # 确定日期
        if custom_day:
            day_value = custom_day
        else:
            day_value = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # 构建查询条件（不包含product_code，因为在子查询中单独处理）
        conditions = [
            f"`day` = '{day_value}'"
        ]
        
        if where_conditions:
            conditions.append(where_conditions)
        
        where_clause = " AND ".join(conditions)
        
        # 构建LIMIT子句（支持分页）
        limit_clause = ""
        if limit:
            if offset > 0:
                limit_clause = f"LIMIT {limit} OFFSET {offset}"
            else:
                limit_clause = f"LIMIT {limit}"
        
        query = f"""
        SELECT tcb.system_matched_company_name as company_name,
               tcb.unified_social_credit_code,
               area_province.city_code AS province_code,
               tcb.province,
               area_city.city_code AS city_code,
               tcb.city,
               area_district.city_code AS district_code,
               tcb.district
        FROM dimdb.dim_tyc_company_base tcb
        LEFT JOIN dimdb.dim_area_code area_province
                  ON tcb.province = area_province.city_name AND area_province.city_level = '1'
        LEFT JOIN dimdb.dim_area_code area_city
                  ON tcb.city = area_city.city_name AND area_city.city_parent_code = area_province.city_code AND
                     area_city.city_level = '2'
        LEFT JOIN dimdb.dim_area_code area_district
                  ON tcb.district = area_district.city_name AND
                     area_district.city_parent_code = area_city.city_code AND
                     area_district.city_level = '3'
        WHERE tcb.system_matched_company_name IN (
            SELECT company_name
            FROM (
                SELECT pcd1.insure_name as company_name
                FROM dwadb.dwa_policy_common_data_test pcd1
                WHERE {where_clause}
                    AND pcd1.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
                UNION
                SELECT pcd2.insured_name as company_name
                FROM dwadb.dwa_policy_common_data_test pcd2
                WHERE {where_clause}
                    AND pcd2.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
            ) tmp 
            ORDER BY company_name ASC
        )
        AND tcb.system_matched_company_name IS NOT NULL
        AND (tcb.province IS NOT NULL OR tcb.city IS NOT NULL OR tcb.district IS NOT NULL)
        ORDER BY tcb.system_matched_company_name ASC
        {limit_clause}
        """
        
        return query
    
    def _query_policy_company_area_data_from_hive(self, 
                                                limit: Optional[int] = None,
                                                where_conditions: Optional[str] = None,
                                                custom_day: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从Hive查询保单企业区域数据
        
        Args:
            limit: 记录数限制
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期，默认使用昨天
            
        Returns:
            Optional[pd.DataFrame]: 查询结果
        """
        try:
            # 使用构建的查询
            query = self._build_query(limit, 0, where_conditions, custom_day)
            
            logger.info(f"查询保单企业区域数据，使用日期: {custom_day or (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')}")
            return self.query_hive_data(query)
            
        except Exception as e:
            logger.error(f"查询Hive保单企业区域数据失败: {str(e)}")
            return None
    
    def _is_valid_area_value(self, value: str) -> bool:
        """
        判断区域值是否有效（非空）
        
        Args:
            value: 区域值
            
        Returns:
            bool: 是否有效
        """
        if not value:
            return False
        
        # 转换为字符串并去除空白
        str_value = str(value).strip()
        
        # 检查各种空值情况
        invalid_values = ['', 'nan', 'null', 'none', 'NULL', 'None', 'NaN']
        
        return str_value not in invalid_values and len(str_value) > 0
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保单企业与区域的关系数据
        按优先级匹配: district -> city -> province
        
        Args:
            hive_data: Hive保单企业区域数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0
        
        for index, row in hive_data.iterrows():
            company_name = str(row.get('company_name', '')).strip()
            unified_social_credit_code = str(row.get('unified_social_credit_code', '')).strip()
            
            # 区域名称
            province = str(row.get('province', '')).strip()
            city = str(row.get('city', '')).strip()
            district = str(row.get('district', '')).strip()
            
            # 区域代码
            province_code = str(row.get('province_code', '')).strip()
            city_code = str(row.get('city_code', '')).strip()
            district_code = str(row.get('district_code', '')).strip()
            
            # 跳过无效数据
            if not company_name or company_name == 'nan':
                skipped_count += 1
                continue
            
            # 使用改进的判断逻辑
            district_valid = self._is_valid_area_value(district) and self._is_valid_area_value(district_code)
            city_valid = self._is_valid_area_value(city) and self._is_valid_area_value(city_code)
            province_valid = self._is_valid_area_value(province) and self._is_valid_area_value(province_code)
            
            # 确定匹配的区域名称和代码（优先级: district -> city -> province）
            area_name = None
            area_code = None
            location_type = None
            
            if district_valid:
                area_name = district
                area_code = district_code
                location_type = 'district'
                logger.debug(f"企业 {company_name} 使用区县级匹配: {district} ({district_code})")
            elif city_valid:
                area_name = city
                area_code = city_code
                location_type = 'city'
                logger.debug(f"企业 {company_name} 使用城市级匹配: {city} ({city_code})")
            elif province_valid:
                area_name = province
                area_code = province_code
                location_type = 'province'
                logger.debug(f"企业 {company_name} 使用省份级匹配: {province} ({province_code})")
            
            if not area_name or not area_code:
                logger.debug(f"跳过企业 {company_name}: 无有效的区域名称或代码")
                skipped_count += 1
                continue
            
            relationship_data.append({
                'company_name': company_name,
                'unified_social_credit_code': unified_social_credit_code if self._is_valid_area_value(unified_social_credit_code) else None,
                'area_name': area_name,
                'area_code': area_code,
                'location_type': location_type,
                'province': province if province_valid else None,
                'province_code': province_code if province_valid else None,
                'city': city if city_valid else None,
                'city_code': city_code if city_valid else None,
                'district': district if district_valid else None,
                'district_code': district_code if district_valid else None,
                'remarks': f"保单企业地理位置关系: {company_name} 位于 {area_name}({area_code})",
                'data_source_node': 'PolicyCompany',
                'relationship_strength': 8,
                'relationship_type': '地理位置关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保单企业区域关系，跳过了 {skipped_count} 个无效记录")
        
        # 按位置类型统计
        location_stats = {}
        for rel in relationship_data:
            loc_type = rel['location_type']
            location_stats[loc_type] = location_stats.get(loc_type, 0) + 1
        
        logger.info(f"匹配统计: {location_stats}")
        
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code进行精确匹配
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询，使用code进行精确匹配
                query = """
                UNWIND $relationships as rel
                MATCH (pc:PolicyCompany)
                WHERE pc.name = rel.company_name
                   OR (rel.unified_social_credit_code IS NOT NULL AND pc.code = rel.unified_social_credit_code)
                MATCH (a:Area)
                WHERE a.code = rel.area_code
                MERGE (pc)-[r:LOCATED_IN]->(a)
                SET r.remarks = rel.remarks,
                    r.location_type = rel.location_type,
                    r.province = rel.province,
                    r.province_code = rel.province_code,
                    r.city = rel.city,
                    r.city_code = rel.city_code,
                    r.district = rel.district,
                    r.district_code = rel.district_code,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type
                RETURN count(r) as created_count
                """
                
                result = self.neo4j_storage.execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保单企业区域关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保单企业区域关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            DELETE r
            """
            
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保单企业区域关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保单企业区域关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单企业区域关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN count(r) as total_count
            """
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按位置类型统计
            location_type_query = """
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.location_type IS NOT NULL
            RETURN r.location_type as location_type, count(r) as count
            ORDER BY count DESC
            """
            location_type_result = self.neo4j_storage.execute_query(location_type_query)
            location_type_stats = {record['location_type']: record['count'] for record in location_type_result}
            
            # 按区域统计（Top 10）
            area_query = """
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            RETURN area.name as area_name, count(r) as company_count
            ORDER BY company_count DESC
            LIMIT 10
            """
            area_result = self.neo4j_storage.execute_query(area_query)
            area_stats = {record['area_name']: record['company_count'] for record in area_result}
            
            # 数据源统计
            source_query = """
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self.neo4j_storage.execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保单企业区域关系",
                additional_stats={
                    "by_location_type": location_type_stats,
                    "by_area_name": area_stats,
                    "by_data_source_node": source_stats
                }
            )
            
        except Exception as e:
            logger.error(f"获取保单企业区域关系统计失败: {str(e)}")
            return self._format_standard_statistics(
                total_count=0,
                relationship_type="保单企业区域关系",
                additional_stats={
                    "by_location_type": {},
                    "by_area_name": {},
                    "by_data_source_node": {}
                }
            )
    
    def search_location_relationships(self, 
                                    company_name: Optional[str] = None,
                                    area_name: Optional[str] = None,
                                    location_type: Optional[str] = None,
                                    province: Optional[str] = None,
                                    city: Optional[str] = None,
                                    district: Optional[str] = None,
                                    include_node_details: bool = True,
                                    limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单企业区域位置关系
        
        Args:
            company_name: 企业名称过滤条件
            area_name: 区域名称过滤条件
            location_type: 位置类型过滤条件（district/city/province）
            province: 省份过滤条件
            city: 城市过滤条件
            district: 区县过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if company_name:
                conditions.append("pc.name CONTAINS $company_name")
                parameters['company_name'] = company_name
            
            if area_name:
                conditions.append("area.name CONTAINS $area_name")
                parameters['area_name'] = area_name
            
            if location_type:
                conditions.append("r.location_type = $location_type")
                parameters['location_type'] = location_type
            
            if province:
                conditions.append("r.province CONTAINS $province")
                parameters['province'] = province
            
            if city:
                conditions.append("r.city CONTAINS $city")
                parameters['city'] = city
            
            if district:
                conditions.append("r.district CONTAINS $district")
                parameters['district'] = district
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 选择返回字段
            return_fields = "r, area.name as area_name, area.code as area_code"
            if include_node_details:
                return_fields += ", pc.name as company_name, pc.code as company_code"
            
            query = f"""
            MATCH (pc:PolicyCompany)-[r:LOCATED_IN]->(area:Area)
            {where_clause}
            RETURN {return_fields}
            ORDER BY pc.name, area.name
            LIMIT $limit
            """
            
            parameters['limit'] = limit
            
            results = self.neo4j_storage.execute_query(query, parameters)
            
            relationships = []
            for record in results:
                rel_data = dict(record['r'])
                rel_data.update({
                    'area_name': record.get('area_name'),
                    'area_code': record.get('area_code')
                })
                
                if include_node_details:
                    rel_data.update({
                        'company_name': record.get('company_name'),
                        'company_code': record.get('company_code')
                    })
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保单企业区域关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保单企业区域关系失败: {str(e)}")
            return [] 