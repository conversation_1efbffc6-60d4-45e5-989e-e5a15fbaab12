"""
保单保险公司关系服务

专门负责保单 Policy 节点与保险公司 InsuranceCompany 节点之间关系的创建和管理
从Neo4j查询保单的保险公司信息，匹配对应的保险公司节点并建立关系
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyInsuranceCompanyRelationshipService(BaseRelationshipService):
    """保单保险公司关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保单数据，匹配保险公司节点并建立关系

        Args:
            limit: 限制处理的记录数（用于测试）

        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保单与保险公司关系")

            # 1. 从Neo4j查询保单保险公司数据
            policy_data = self._query_policy_insurance_data_from_neo4j(limit)
            if not policy_data:
                logger.warning("未找到有效的保单保险公司数据")
                return True

            # 2. 构建关系数据
            relationship_data = self._build_relationship_data_from_neo4j(policy_data)

            if not relationship_data:
                logger.warning("没有有效的保单保险公司关系数据需要创建")
                return True

            # 3. 批量创建关系
            success = self._create_policy_insurance_relationships(relationship_data)

            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保单保险公司关系")
                return True
            else:
                logger.error("批量创建保单保险公司关系失败")
                return False

        except Exception as e:
            logger.error(f"从Neo4j创建保单保险公司关系失败: {str(e)}")
            return False
    
    def _query_policy_insurance_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保单的保险公司数据

        Args:
            limit: 记录数限制

        Returns:
            List[Dict[str, Any]]: 保单保险公司数据列表
        """
        try:
            # 构建查询条件，查询有保险公司信息的保单
            conditions = [
                "policy.insurance_company_code IS NOT NULL",
                "policy.insurance_company_code <> ''",
                "policy.insurance_company_name IS NOT NULL",
                "policy.insurance_company_name <> ''"
            ]
            where_clause = " AND " + " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""

            query = f"""
            MATCH (policy:Policy)
            WHERE policy.code IS NOT NULL AND policy.code <> ''
            {where_clause}
            RETURN policy.code as policy_code,
                   policy.name as policy_name,
                   policy.insurance_company_code as insurance_company_code,
                   policy.insurance_company_name as insurance_company_name,
                   policy.policy_start_date as policy_start_date,
                   policy.policy_end_date as policy_end_date,
                   policy.policy_status as policy_status,
                   policy.business_id as policy_business_id
            ORDER BY policy.code
            {limit_clause}
            """

            logger.info(f"查询保单保险公司数据，条件: {where_clause}")
            results = self._safe_execute_query(query)

            if not results:
                return []

            # 过滤掉无效的保险公司数据
            valid_data = []
            for record in results:
                policy_code = str(record.get('policy_code', '')).strip()
                insurance_company_code = str(record.get('insurance_company_code', '')).strip()
                insurance_company_name = str(record.get('insurance_company_name', '')).strip()

                if (policy_code and insurance_company_code and insurance_company_name and
                    policy_code != 'nan' and insurance_company_code != 'nan' and insurance_company_name != 'nan'):

                    valid_data.append({
                        'policy_code': policy_code,
                        'policy_name': record.get('policy_name', ''),
                        'insurance_company_code': insurance_company_code,
                        'insurance_company_name': insurance_company_name,
                        'policy_start_date': record.get('policy_start_date'),
                        'policy_end_date': record.get('policy_end_date'),
                        'policy_status': record.get('policy_status', ''),
                        'policy_business_id': record.get('policy_business_id', policy_code)
                    })

            logger.info(f"找到 {len(valid_data)} 个有效的保单保险公司记录")
            return valid_data

        except Exception as e:
            logger.error(f"查询Neo4j保单保险公司数据失败: {str(e)}")
            return []
    

    
    def _build_relationship_data_from_neo4j(self, policy_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从Neo4j保单数据构建与保险公司的关系数据

        Args:
            policy_data: Neo4j保单数据列表

        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0

        for policy in policy_data:
            policy_code = policy.get('policy_code', '')
            insurance_company_code = policy.get('insurance_company_code', '')
            insurance_company_name = policy.get('insurance_company_name', '')

            # 跳过无效数据
            if not policy_code or not insurance_company_code:
                skipped_count += 1
                continue

            # 检查保险公司节点是否存在
            if not self._check_insurance_company_exists(insurance_company_code):
                skipped_count += 1
                continue

            relationship_data.append({
                'policy_code': policy_code,
                'insurance_company_code': insurance_company_code,
                'insurance_company_name': insurance_company_name,
                'policy_start_date': policy.get('policy_start_date'),
                'policy_end_date': policy.get('policy_end_date'),
                'policy_status': policy.get('policy_status', ''),
                'remarks': f"保单承保关系: 保单 {policy_code} 由保险公司 {insurance_company_name}({insurance_company_code}) 承保",
                'data_source_node': 'Policy',
                'relationship_strength': 10,  # 承保关系是核心业务关系
                'relationship_type': '承保关系'
            })

        logger.info(f"构建了 {len(relationship_data)} 个有效的保单保险公司关系，跳过了 {skipped_count} 个无效记录")
        return relationship_data

    def _check_insurance_company_exists(self, insurance_company_code: str) -> bool:
        """
        检查保险公司节点是否存在

        Args:
            insurance_company_code: 保险公司编码

        Returns:
            bool: 是否存在
        """
        try:
            query = """
            MATCH (company:InsuranceCompany)
            WHERE company.code = $insurance_company_code
            RETURN count(company) as count
            """

            result = self._safe_execute_query(query, {'insurance_company_code': insurance_company_code})
            return result and result[0]['count'] > 0

        except Exception as e:
            logger.error(f"检查保险公司节点存在性失败: {str(e)}")
            return False
    
    def _create_policy_insurance_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        创建保单与保险公司的关系

        Args:
            relationship_data: 关系数据列表

        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0

            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]

                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (policy:Policy {code: rel.policy_code})
                MATCH (company:InsuranceCompany {code: rel.insurance_company_code})
                MERGE (policy)-[r:ISSUED_BY]->(company)
                SET r.relationship_type = 'ISSUED_BY',
                    r.relationship_status = 'active',
                    r.relationship_strength = rel.relationship_strength,
                    r.insurance_company_name = rel.insurance_company_name,
                    r.policy_start_date = rel.policy_start_date,
                    r.policy_end_date = rel.policy_end_date,
                    r.policy_status = rel.policy_status,
                    r.data_source_node = rel.data_source_node,
                    r.remarks = rel.remarks,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """

                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")

            logger.info(f"总共成功创建 {success_count} 个保单保险公司关系")
            return success_count > 0

        except Exception as e:
            logger.error(f"创建保单保险公司关系失败: {str(e)}")
            return False
    

    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保单保险公司关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保单保险公司关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保单保险公司关系失败: {str(e)}")
            return False
    
    def cleanup_duplicate_relationships(self) -> int:
        """
        清理重复的保单保险公司关系
        对于相同的保单-保险公司对，保留最早创建的关系，删除其他重复关系

        Returns:
            int: 删除的重复关系数量
        """
        try:
            # 查找重复的关系
            find_duplicates_query = """
            MATCH (policy:Policy)-[r:ISSUED_BY]->(company:InsuranceCompany)
            WITH policy, company, collect(r) as rels
            WHERE size(rels) > 1
            RETURN policy.code as policy_code, company.code as company_code, rels
            """

            duplicates_result = self._safe_execute_query(find_duplicates_query)

            if not duplicates_result:
                logger.info("没有发现重复的保单保险公司关系")
                return 0

            total_deleted = 0

            for record in duplicates_result:
                policy_code = record['policy_code']
                company_code = record['company_code']
                rels = record['rels']

                if len(rels) <= 1:
                    continue

                logger.info(f"发现重复的关系，保单: {policy_code}，保险公司: {company_code}，共 {len(rels)} 个")

                # 保留最早创建的关系，删除其他
                cleanup_query = """
                MATCH (policy:Policy {code: $policy_code})-[r:ISSUED_BY]->(company:InsuranceCompany {code: $company_code})
                WITH r
                ORDER BY COALESCE(r.created_at, datetime()) ASC
                WITH collect(r) as rels
                WHERE size(rels) > 1
                UNWIND rels[1..] as duplicate_rel
                DELETE duplicate_rel
                RETURN count(duplicate_rel) as deleted_count
                """

                cleanup_result = self._safe_execute_query(cleanup_query, {
                    'policy_code': policy_code,
                    'company_code': company_code
                })
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count

                logger.info(f"清理了 {deleted_count} 个重复关系，保单: {policy_code}，保险公司: {company_code}")

            logger.info(f"总共清理了 {total_deleted} 个重复的保单保险公司关系")
            return total_deleted

        except Exception as e:
            logger.error(f"清理重复关系失败: {str(e)}")
            return 0

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单保险公司关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按保险公司统计
            company_query = """
            MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
            WHERE r.insurance_name IS NOT NULL
            RETURN r.insurance_name as company_name, count(r) as policy_count
            ORDER BY policy_count DESC
            LIMIT 10
            """
            company_result = self._safe_execute_query(company_query)
            company_stats = {record['company_name']: record['policy_count'] for record in company_result}
            
            # 按产品代码统计
            product_query = """
            MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
            WHERE r.product_code IS NOT NULL
            RETURN r.product_code as product_code, count(r) as count
            ORDER BY count DESC
            """
            product_result = self._safe_execute_query(product_query)
            product_stats = {record['product_code']: record['count'] for record in product_result}
            
            # 数据源统计
            source_query = """
            MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保单保险公司关系",
                additional_stats={
                    "by_insurance_company": company_stats,
                    "by_product_code": product_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保单保险公司关系统计失败: {str(e)}")
            return {}
    
    def search_underwriting_relationships(self, 
                                        policy_code: Optional[str] = None,
                                        insurance_code: Optional[str] = None,
                                        insurance_name: Optional[str] = None,
                                        product_code: Optional[str] = None,
                                        include_node_details: bool = True,
                                        limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单保险公司承保关系
        
        Args:
            policy_code: 保单编码过滤条件
            insurance_code: 保险公司编码过滤条件
            insurance_name: 保险公司名称过滤条件
            product_code: 产品代码过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if policy_code:
                conditions.append("policy.code = $policy_code")
                parameters['policy_code'] = policy_code
            
            if insurance_code:
                conditions.append("r.insurance_code = $insurance_code")
                parameters['insurance_code'] = insurance_code
            
            if insurance_name:
                conditions.append("r.insurance_name CONTAINS $insurance_name")
                parameters['insurance_name'] = insurance_name
            
            if product_code:
                conditions.append("r.product_code = $product_code")
                parameters['product_code'] = product_code
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
                WHERE {where_clause}
                RETURN r,
                       {{code: policy.code, policy_start_date: policy.policy_start_date, policy_end_date: policy.policy_end_date, policy_status: policy.policy_status}} as policy_info,
                       {{code: company.code, name: company.name, short_name: company.short_name}} as company_info
                ORDER BY policy.code, company.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (policy:Policy)-[r:UNDERWRITTEN_BY]->(company:InsuranceCompany)
                WHERE {where_clause}
                RETURN r, policy.code as policy_code, company.code as insurance_code
                ORDER BY policy.code, company.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'UNDERWRITTEN_BY',
                    'relationship_status': 'active'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['policy'] = record['policy_info']
                    rel_data['insurance_company'] = record['company_info']
                else:
                    rel_data['policy_code'] = record['policy_code']
                    rel_data['insurance_code'] = record['insurance_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保单保险公司承保关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保单保险公司承保关系失败: {str(e)}")
            return [] 