"""
保险代理人员邮箱关系服务

专门负责 InsuranceAgentPerson 节点与 Email 节点之间关系的创建和管理
从Neo4j查询保险代理人员的email属性，创建或关联对应的邮箱节点
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceAgentEmailRelationshipService(BaseRelationshipService):
    """保险代理人员邮箱关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险代理人员数据，创建或关联Email节点，并建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险代理人员与邮箱关系")
            
            # 1. 从Neo4j查询保险代理人员数据
            agent_data = self._query_agent_email_data_from_neo4j(limit)
            if not agent_data:
                logger.warning("未找到有效的保险代理人员邮箱数据")
                return True
            
            # 2. 创建缺失的Email节点
            created_emails = self._create_missing_emails(agent_data)
            logger.info(f"创建了 {created_emails} 个Email节点")
            
            # 3. 创建关系
            success = self._create_agent_email_relationships(agent_data)
            
            if success:
                logger.info(f"成功处理 {len(agent_data)} 个保险代理人员邮箱关系")
                return True
            else:
                logger.error("创建保险代理人员邮箱关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保险代理人员邮箱关系失败: {str(e)}")
            return False
    
    def _query_agent_email_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险代理人员的邮箱数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 代理人员邮箱数据列表
        """
        try:
            # 构建查询条件
            conditions = ["agent.email IS NOT NULL", "agent.email <> ''"]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (agent:InsuranceAgentPerson)
            WHERE {where_clause}
            RETURN agent.code as agent_code,
                   agent.name as agent_name,
                   agent.email as email,
                   agent.business_id as agent_business_id
            ORDER BY agent.code
            {limit_clause}
            """
            
            logger.info(f"查询保险代理人员邮箱数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的邮箱地址
            valid_data = []
            for record in results:
                email = str(record.get('email', '')).strip()
                agent_code = str(record.get('agent_code', '')).strip()
                
                # 简单的邮箱格式验证
                if email and email != 'nan' and email != 'None' and '@' in email and agent_code:
                    valid_data.append({
                        'agent_code': agent_code,
                        'agent_name': record.get('agent_name', ''),
                        'email': email,
                        'agent_business_id': record.get('agent_business_id', agent_code)
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保险代理人员邮箱记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险代理人员邮箱数据失败: {str(e)}")
            return []
    
    def _create_missing_emails(self, agent_data: List[Dict[str, Any]]) -> int:
        """
        创建缺失的Email节点
        
        Args:
            agent_data: 代理人员数据列表
            
        Returns:
            int: 创建的节点数量
        """
        try:
            # 提取所有邮箱地址
            emails = [data['email'] for data in agent_data]
            
            if not emails:
                return 0
            
            # 使用MERGE确保不会创建重复的Email节点
            # MERGE会先查找，如果不存在才创建
            create_query = """
            UNWIND $emails as email
            MERGE (emailNode:Email {code: email})
            ON CREATE SET 
                emailNode.business_id = email,
                emailNode.created_at = datetime(),
                emailNode.updated_at = datetime()
            ON MATCH SET 
                emailNode.updated_at = datetime()
            RETURN count(emailNode) as processed_count
            """
            
            result = self._safe_execute_query(create_query, {'emails': emails})
            processed_count = result[0]['processed_count'] if result else 0
            
            logger.info(f"处理了 {processed_count} 个Email节点（包含新创建和已存在的）")
            return processed_count
            
        except Exception as e:
            logger.error(f"创建Email节点失败: {str(e)}")
            return 0
    
    def _create_agent_email_relationships(self, agent_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险代理人员与邮箱的关系
        
        Args:
            agent_data: 代理人员数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(agent_data), batch_size):
                batch_data = agent_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (agent:InsuranceAgentPerson {code: rel.agent_code})
                MATCH (emailNode:Email {code: rel.email})
                MERGE (agent)-[r:HAS_EMAIL]->(emailNode)
                SET r.relationship_type = 'HAS_EMAIL',
                    r.relationship_status = 'active',
                    r.relationship_strength = 8,
                    r.data_source_node = 'InsuranceAgentPerson',
                    r.remarks = '保险代理人员邮箱: ' + rel.agent_name + ' - ' + rel.email,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险代理人员邮箱关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险代理人员邮箱关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险代理人员邮箱关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险代理人员邮箱关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险代理人员邮箱关系失败: {str(e)}")
            return False
    
    def delete_orphaned_emails(self) -> int:
        """
        删除孤立的Email节点（没有任何关系的节点）
        
        Returns:
            int: 删除的节点数量
        """
        try:
            query = """
            MATCH (emailNode:Email)
            WHERE NOT (emailNode)--()
            DELETE emailNode
            RETURN count(emailNode) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = result[0]['deleted_count'] if result else 0
            
            logger.info(f"删除了 {deleted_count} 个孤立的Email节点")
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除孤立Email节点失败: {str(e)}")
            return 0
    
    def cleanup_duplicate_emails(self) -> int:
        """
        清理重复的Email节点
        对于相同code的节点，保留最早创建的节点，删除其他重复节点
        
        Returns:
            int: 删除的重复节点数量
        """
        try:
            # 查找重复的Email节点
            find_duplicates_query = """
            MATCH (emailNode:Email)
            WITH emailNode.code as code, collect(emailNode) as emails
            WHERE size(emails) > 1
            RETURN code, emails
            """
            
            duplicates_result = self._safe_execute_query(find_duplicates_query)
            
            if not duplicates_result:
                logger.info("没有发现重复的Email节点")
                return 0
            
            total_deleted = 0
            
            for record in duplicates_result:
                code = record['code']
                emails = record['emails']
                
                if len(emails) <= 1:
                    continue
                
                logger.info(f"发现重复的Email节点，code: {code}，共 {len(emails)} 个")
                
                # 对于每组重复节点，保留最早创建的节点，删除其他
                cleanup_query = """
                MATCH (emailNode:Email {code: $code})
                WITH emailNode 
                ORDER BY COALESCE(emailNode.created_at, datetime()) ASC
                WITH collect(emailNode) as emails
                WHERE size(emails) > 1
                UNWIND emails[1..] as duplicate_email
                
                // 将所有关系转移到第一个（最早的）节点
                OPTIONAL MATCH (duplicate_email)-[r]-()
                WITH duplicate_email, collect(r) as rels, emails[0] as keep_email
                
                // 重新创建关系到保留的节点
                UNWIND rels as rel
                WITH duplicate_email, rel, keep_email,
                     startNode(rel) as start_node, 
                     endNode(rel) as end_node,
                     type(rel) as rel_type,
                     properties(rel) as rel_props
                
                // 如果重复节点是起始节点
                FOREACH (ignore IN CASE WHEN start_node = duplicate_email THEN [1] ELSE [] END |
                    MERGE (keep_email)-[new_rel:HAS_EMAIL]->(end_node)
                    SET new_rel = rel_props
                )
                
                // 如果重复节点是结束节点  
                FOREACH (ignore IN CASE WHEN end_node = duplicate_email THEN [1] ELSE [] END |
                    MERGE (start_node)-[new_rel:HAS_EMAIL]->(keep_email)
                    SET new_rel = rel_props
                )
                
                // 删除原关系和重复节点
                DELETE rel, duplicate_email
                RETURN count(duplicate_email) as deleted_count
                """
                
                cleanup_result = self._safe_execute_query(cleanup_query, {'code': code})
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count
                
                logger.info(f"清理了 {deleted_count} 个重复的Email节点，code: {code}")
            
            logger.info(f"总共清理了 {total_deleted} 个重复的Email节点")
            return total_deleted
            
        except Exception as e:
            logger.error(f"清理重复Email节点失败: {str(e)}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险代理人员邮箱关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有邮箱的代理人员数量
            agent_with_email_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
            RETURN count(DISTINCT agent) as agent_count
            """
            agent_result = self._safe_execute_query(agent_with_email_query)
            agent_count = agent_result[0]['agent_count'] if agent_result else 0
            
            # Email节点总数
            email_total_query = """
            MATCH (emailNode:Email)
            RETURN count(emailNode) as email_total
            """
            email_result = self._safe_execute_query(email_total_query)
            email_total = email_result[0]['email_total'] if email_result else 0
            
            # 孤立的Email节点数量
            orphaned_query = """
            MATCH (emailNode:Email)
            WHERE NOT (emailNode)--()
            RETURN count(emailNode) as orphaned_count
            """
            orphaned_result = self._safe_execute_query(orphaned_query)
            orphaned_count = orphaned_result[0]['orphaned_count'] if orphaned_result else 0
            
            # 重复的Email节点统计
            duplicate_query = """
            MATCH (emailNode:Email)
            WITH emailNode.code as code, count(emailNode) as email_count
            WHERE email_count > 1
            RETURN count(code) as duplicate_codes, sum(email_count - 1) as duplicate_nodes
            """
            duplicate_result = self._safe_execute_query(duplicate_query)
            duplicate_codes = duplicate_result[0]['duplicate_codes'] if duplicate_result else 0
            duplicate_nodes = duplicate_result[0]['duplicate_nodes'] if duplicate_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险代理人员邮箱关系",
                additional_stats={
                    "agents_with_email": agent_count,
                    "total_emails": email_total,
                    "orphaned_emails": orphaned_count,
                    "duplicate_email_codes": duplicate_codes,
                    "duplicate_email_nodes": duplicate_nodes,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险代理人员邮箱关系统计失败: {str(e)}")
            return {}
    
    def search_agent_email_relationships(self, 
                                       agent_code: Optional[str] = None,
                                       agent_name: Optional[str] = None,
                                       email: Optional[str] = None,
                                       include_node_details: bool = True,
                                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险代理人员邮箱关系
        
        Args:
            agent_code: 代理人员编码过滤条件
            agent_name: 代理人员姓名过滤条件
            email: 邮箱地址过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if agent_code:
                conditions.append("agent.code CONTAINS $agent_code")
                parameters['agent_code'] = agent_code
            
            if agent_name:
                conditions.append("agent.name CONTAINS $agent_name")
                parameters['agent_name'] = agent_name
            
            if email:
                conditions.append("emailNode.code CONTAINS $email")
                parameters['email'] = email
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
                WHERE {where_clause}
                RETURN r,
                       {{code: agent.code, name: agent.name, business_id: agent.business_id}} as agent_info,
                       {{code: emailNode.code, name: emailNode.name, business_id: emailNode.business_id}} as email_info
                ORDER BY agent.code, emailNode.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (agent:InsuranceAgentPerson)-[r:HAS_EMAIL]->(emailNode:Email)
                WHERE {where_clause}
                RETURN r, agent.code as agent_code, emailNode.code as email
                ORDER BY agent.code, emailNode.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'HAS_EMAIL'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['agent'] = record['agent_info']
                    rel_data['email'] = record['email_info']
                else:
                    rel_data['agent_code'] = record['agent_code']
                    rel_data['email'] = record['email']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险代理人员邮箱关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险代理人员邮箱关系失败: {str(e)}")
            return [] 