"""
保险产品保险公司关系服务

专门负责 InsuranceProduct 节点与 InsuranceCompany 节点之间关系的创建和管理
根据InsuranceProduct的insurance_unified_social_credit_code属性关联到InsuranceCompany的unified_social_credit_code字段
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceProductCompanyRelationshipService(BaseRelationshipService):
    """保险产品保险公司关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询InsuranceProduct数据，根据insurance_unified_social_credit_code关联到InsuranceCompany节点
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险产品与保险公司关系")
            
            # 1. 从Neo4j查询保险产品数据
            product_data = self._query_insurance_product_company_data_from_neo4j(limit)
            if not product_data:
                logger.warning("未找到有效的保险产品公司数据")
                return True
            
            # 2. 创建关系
            success = self._create_insurance_product_company_relationships(product_data)
            
            if success:
                logger.info(f"成功处理 {len(product_data)} 个保险产品公司关系")
                return True
            else:
                logger.error("创建保险产品公司关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保险产品公司关系失败: {str(e)}")
            return False
    
    def _query_insurance_product_company_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险产品的公司数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保险产品公司数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "product.insurance_unified_social_credit_code IS NOT NULL", 
                "product.insurance_unified_social_credit_code <> ''",
                "product.insurance_unified_social_credit_code <> 'NULL'"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (product:InsuranceProduct)
            WHERE {where_clause}
            RETURN product.product_id as product_id,
                   product.product_code as product_code,
                   product.product_name as product_name,
                   product.insurance_name as insurance_name,
                   product.insurance_code as insurance_code,
                   product.insurance_unified_social_credit_code as insurance_unified_social_credit_code
            ORDER BY product.product_id
            {limit_clause}
            """
            
            logger.info(f"查询保险产品公司数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                product_id = str(record.get('product_id', '')).strip()
                insurance_unified_social_credit_code = str(record.get('insurance_unified_social_credit_code', '')).strip()
                
                if product_id and insurance_unified_social_credit_code and insurance_unified_social_credit_code != 'nan' and insurance_unified_social_credit_code != 'None':
                    valid_data.append({
                        'product_id': product_id,
                        'product_code': record.get('product_code', ''),
                        'product_name': record.get('product_name', ''),
                        'insurance_name': record.get('insurance_name', ''),
                        'insurance_code': record.get('insurance_code', ''),
                        'insurance_unified_social_credit_code': insurance_unified_social_credit_code
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保险产品公司记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险产品公司数据失败: {str(e)}")
            return []
    
    def _create_insurance_product_company_relationships(self, product_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险产品与保险公司的关系
        
        Args:
            product_data: 保险产品数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            failed_count = 0
            
            for i in range(0, len(product_data), batch_size):
                batch_data = product_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (product:InsuranceProduct {product_id: rel.product_id})
                MATCH (company:Organization:InsuranceCompany {unified_social_credit_code: rel.insurance_unified_social_credit_code})
                MERGE (product)-[r:ISSUED_BY]->(company)
                SET r.relationship_type = 'ISSUED_BY',
                    r.relationship_status = 'active',
                    r.relationship_strength = 10,
                    r.data_source_node = 'InsuranceProduct',
                    r.insurance_name = rel.insurance_name,
                    r.insurance_code = rel.insurance_code,
                    r.remarks = '保险产品发行方: ' + rel.product_name + ' -> ' + rel.insurance_name,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    batch_failed = len(batch_data) - batch_count
                    failed_count += batch_failed
                    
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系，失败 {batch_failed} 个")
                    
                    if batch_failed > 0:
                        # 记录失败的保险公司统一社会信用代码用于调试
                        failed_codes = [data['insurance_unified_social_credit_code'] for data in batch_data]
                        logger.warning(f"部分保险公司统一社会信用代码未找到匹配的InsuranceCompany节点: {set(failed_codes)}")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
                    failed_count += len(batch_data)
            
            logger.info(f"总共成功创建 {success_count} 个保险产品公司关系，失败 {failed_count} 个")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险产品公司关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险产品公司关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (product:InsuranceProduct)-[r:ISSUED_BY]->(company:Organization:InsuranceCompany)
            DELETE r
            RETURN count(r) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = 0
            if result:
                deleted_count = result[0].get('deleted_count', 0)
            
            logger.info(f"删除了 {deleted_count} 个保险产品公司关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险产品公司关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险产品公司关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数量
            total_query = """
            MATCH (product:InsuranceProduct)-[r:ISSUED_BY]->(company:Organization:InsuranceCompany)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 最热门的保险公司（发行产品最多的保险公司）
            popular_query = """
            MATCH (product:InsuranceProduct)-[r:ISSUED_BY]->(company:Organization:InsuranceCompany)
            RETURN company.name as company_name, 
                   company.unified_social_credit_code as company_code,
                   count(r) as product_count
            ORDER BY product_count DESC
            LIMIT 10
            """
            popular_result = self._safe_execute_query(popular_query)
            popular_companies = []
            for record in popular_result:
                popular_companies.append({
                    'name': record['company_name'],
                    'unified_social_credit_code': record['company_code'],
                    'product_count': record['product_count']
                })
            
            # 没有保险公司关联的产品数量
            unmatched_query = """
            MATCH (product:InsuranceProduct)
            WHERE product.insurance_unified_social_credit_code IS NOT NULL 
            AND product.insurance_unified_social_credit_code <> ''
            AND NOT EXISTS {
                MATCH (product)-[:ISSUED_BY]->(company:Organization:InsuranceCompany)
            }
            RETURN count(product) as unmatched_count
            """
            unmatched_result = self._safe_execute_query(unmatched_query)
            unmatched_count = unmatched_result[0]['unmatched_count'] if unmatched_result else 0
            
            return {
                "total_count": total_count,
                "relationship_type": "ISSUED_BY",
                "popular_companies": popular_companies,
                "unmatched_products": unmatched_count,
                "description": "保险产品公司关系统计"
            }
            
        except Exception as e:
            logger.error(f"获取保险产品公司关系统计失败: {str(e)}")
            return {
                "total_count": 0,
                "relationship_type": "ISSUED_BY",
                "error": str(e)
            }
    
    def get_unmatched_companies(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取未匹配的保险公司统一社会信用代码（InsuranceProduct中有但InsuranceCompany中没有的）
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 未匹配的保险公司数据
        """
        try:
            query = f"""
            MATCH (product:InsuranceProduct)
            WHERE product.insurance_unified_social_credit_code IS NOT NULL 
            AND product.insurance_unified_social_credit_code <> ''
            AND NOT EXISTS {{
                MATCH (company:Organization:InsuranceCompany)
                WHERE company.unified_social_credit_code = product.insurance_unified_social_credit_code
            }}
            RETURN product.insurance_unified_social_credit_code as company_code,
                   product.insurance_name as company_name,
                   count(product) as product_count
            ORDER BY product_count DESC
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            unmatched_data = []
            for record in results:
                unmatched_data.append({
                    'company_code': record.get('company_code'),
                    'company_name': record.get('company_name'),
                    'product_count': record.get('product_count', 0)
                })
            
            logger.info(f"找到 {len(unmatched_data)} 个未匹配的保险公司")
            return unmatched_data
            
        except Exception as e:
            logger.error(f"获取未匹配保险公司失败: {str(e)}")
            return []
    
    def search_insurance_product_company_relationships(self, 
                                                       product_id: Optional[str] = None,
                                                       product_code: Optional[str] = None,
                                                       product_name: Optional[str] = None,
                                                       company_name: Optional[str] = None,
                                                       company_code: Optional[str] = None,
                                                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险产品公司关系
        
        Args:
            product_id: 产品ID过滤条件
            product_code: 产品代码过滤条件
            product_name: 产品名称过滤条件
            company_name: 保险公司名称过滤条件
            company_code: 保险公司统一社会信用代码过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if product_id:
                conditions.append(f"product.product_id CONTAINS '{product_id}'")
            
            if product_code:
                conditions.append(f"product.product_code CONTAINS '{product_code}'")
            
            if product_name:
                conditions.append(f"product.product_name CONTAINS '{product_name}'")
            
            if company_name:
                conditions.append(f"company.name CONTAINS '{company_name}'")
            
            if company_code:
                conditions.append(f"company.unified_social_credit_code = '{company_code}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (product:InsuranceProduct)-[r:ISSUED_BY]->(company:Organization:InsuranceCompany)
            WHERE {where_clause}
            RETURN product.product_id as product_id,
                   product.product_code as product_code,
                   product.product_name as product_name,
                   company.unified_social_credit_code as company_code,
                   company.name as company_name,
                   r.created_at as relationship_created_at
            ORDER BY product.product_id
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            relationship_data = []
            for record in results:
                relationship_data.append({
                    'product_id': record.get('product_id'),
                    'product_code': record.get('product_code'),
                    'product_name': record.get('product_name'),
                    'company_code': record.get('company_code'),
                    'company_name': record.get('company_name'),
                    'created_at': record.get('relationship_created_at')
                })
            
            logger.info(f"搜索到 {len(relationship_data)} 个保险产品公司关系")
            return relationship_data
            
        except Exception as e:
            logger.error(f"搜索保险产品公司关系失败: {str(e)}")
            return [] 