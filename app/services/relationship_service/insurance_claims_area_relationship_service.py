"""
保险理赔区域关系服务

专门负责保险理赔 InsuranceClaims 节点与区域 Area 节点之间关系的创建和管理
从Neo4j查询保险理赔的出险地址信息，匹配对应的区域节点并建立关系
按优先级顺序匹配: district -> city -> province
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsAreaRelationshipService(BaseRelationshipService):
    """保险理赔区域关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险理赔数据，匹配区域节点并建立关系

        Args:
            limit: 限制处理的记录数（用于测试）

        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险理赔区域关系")

            # 1. 从Neo4j查询保险理赔出险地址数据
            claims_data = self._query_claims_area_data_from_neo4j(limit)
            if not claims_data:
                logger.warning("未找到有效的保险理赔出险地址数据")
                return True

            # 2. 构建关系数据
            relationship_data = self._build_relationship_data_from_neo4j(claims_data)

            if not relationship_data:
                logger.warning("没有有效的保险理赔区域关系数据需要创建")
                return True

            # 3. 批量创建关系
            success = self._create_claims_area_relationships(relationship_data)

            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险理赔区域关系")
                return True
            else:
                logger.error("批量创建保险理赔区域关系失败")
                return False

        except Exception as e:
            logger.error(f"从Neo4j创建保险理赔区域关系失败: {str(e)}")
            return False
    
    def _query_claims_area_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险理赔的出险地址数据

        Args:
            limit: 记录数限制

        Returns:
            List[Dict[str, Any]]: 保险理赔出险地址数据列表
        """
        try:
            # 构建查询条件，查询有出险地址信息的保险理赔
            conditions = [
                "(claims.risk_province IS NOT NULL AND claims.risk_province <> '')",
                "OR (claims.risk_city IS NOT NULL AND claims.risk_city <> '')",
                "OR (claims.risk_district IS NOT NULL AND claims.risk_district <> '')",
                "OR (claims.risk_address IS NOT NULL AND claims.risk_address <> '')"
            ]
            where_clause = " AND (" + " ".join(conditions) + ")"
            limit_clause = f"LIMIT {limit}" if limit else ""

            query = f"""
            MATCH (claims:InsuranceClaims)
            WHERE claims.case_no IS NOT NULL AND claims.case_no <> ''
            {where_clause}
            RETURN claims.case_no as case_no,
                   claims.risk_province as risk_province,
                   claims.risk_city as risk_city,
                   claims.risk_district as risk_district,
                   claims.risk_address as risk_address,
                   claims.business_id as claims_business_id
            ORDER BY claims.case_no
            {limit_clause}
            """

            logger.info(f"查询保险理赔出险地址数据，条件: {where_clause}")
            results = self._safe_execute_query(query)

            if not results:
                return []

            # 过滤掉无效的地址数据
            valid_data = []
            for record in results:
                case_no = str(record.get('case_no', '')).strip()

                # 检查是否有有效的出险地址信息
                risk_province = str(record.get('risk_province', '')).strip()
                risk_city = str(record.get('risk_city', '')).strip()
                risk_district = str(record.get('risk_district', '')).strip()
                risk_address = str(record.get('risk_address', '')).strip()

                if (case_no and
                    (self._is_valid_area_value(risk_province) or
                     self._is_valid_area_value(risk_city) or
                     self._is_valid_area_value(risk_district) or
                     self._is_valid_area_value(risk_address))):

                    valid_data.append({
                        'case_no': case_no,
                        'risk_province': risk_province,
                        'risk_city': risk_city,
                        'risk_district': risk_district,
                        'risk_address': risk_address,
                        'claims_business_id': record.get('claims_business_id', case_no)
                    })

            logger.info(f"找到 {len(valid_data)} 个有效的保险理赔出险地址记录")
            return valid_data

        except Exception as e:
            logger.error(f"查询Neo4j保险理赔出险地址数据失败: {str(e)}")
            return []
    
    def _build_relationship_data_from_neo4j(self, claims_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从Neo4j保险理赔数据构建与区域的关系数据
        按优先级匹配: district -> city -> province

        Args:
            claims_data: Neo4j保险理赔数据列表

        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0

        for claims in claims_data:
            case_no = claims.get('case_no', '')

            # 出险地址信息
            risk_province = str(claims.get('risk_province', '')).strip()
            risk_city = str(claims.get('risk_city', '')).strip()
            risk_district = str(claims.get('risk_district', '')).strip()

            # 跳过无效数据
            if not case_no:
                skipped_count += 1
                continue

            # 查找匹配的区域节点（按优先级: district -> city -> province）
            matched_area = self._find_matching_area(risk_province, risk_city, risk_district)

            if not matched_area:
                skipped_count += 1
                continue

            relationship_data.append({
                'case_no': case_no,
                'area_code': matched_area['area_code'],
                'area_name': matched_area['area_name'],
                'location_type': matched_area['location_type'],
                'risk_province': risk_province if self._is_valid_area_value(risk_province) else None,
                'risk_city': risk_city if self._is_valid_area_value(risk_city) else None,
                'risk_district': risk_district if self._is_valid_area_value(risk_district) else None,
                'remarks': f"保险理赔出险地理位置关系: {case_no} 出险地在 {matched_area['area_name']}({matched_area['area_code']})",
                'data_source_node': 'InsuranceClaims',
                'relationship_strength': 9,
                'relationship_type': '出险地理位置关系'
            })

        logger.info(f"构建了 {len(relationship_data)} 个有效的保险理赔区域关系，跳过了 {skipped_count} 个无效记录")

        # 按位置类型统计
        location_stats = {}
        for rel in relationship_data:
            loc_type = rel['location_type']
            location_stats[loc_type] = location_stats.get(loc_type, 0) + 1

        logger.info(f"匹配统计: {location_stats}")

        return relationship_data
    
    def _find_matching_area(self, risk_province: str, risk_city: str, risk_district: str) -> Optional[Dict[str, Any]]:
        """
        查找匹配的区域节点，按优先级匹配: district -> city -> province

        Args:
            risk_province: 出险省份名称
            risk_city: 出险城市名称
            risk_district: 出险区县名称

        Returns:
            Optional[Dict[str, Any]]: 匹配的区域信息，包含 area_code, area_name, location_type
        """
        try:
            # 按优先级查找区域节点
            search_areas = []

            if self._is_valid_area_value(risk_district):
                search_areas.append(('district', risk_district))
            if self._is_valid_area_value(risk_city):
                search_areas.append(('city', risk_city))
            if self._is_valid_area_value(risk_province):
                search_areas.append(('province', risk_province))

            for location_type, area_name in search_areas:
                # 查找匹配的区域节点
                query = """
                MATCH (area:Area)
                WHERE area.name = $area_name
                RETURN area.code as area_code, area.name as area_name
                LIMIT 1
                """

                result = self._safe_execute_query(query, {'area_name': area_name})
                if result:
                    return {
                        'area_code': result[0]['area_code'],
                        'area_name': result[0]['area_name'],
                        'location_type': location_type
                    }

            return None

        except Exception as e:
            logger.error(f"查找匹配区域失败: {str(e)}")
            return None

    def _create_claims_area_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险理赔与区域的关系

        Args:
            relationship_data: 关系数据列表

        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0

            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]

                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (claims:InsuranceClaims {case_no: rel.case_no})
                MATCH (area:Area {code: rel.area_code})
                MERGE (claims)-[r:OCCURRED_IN]->(area)
                SET r.relationship_type = 'OCCURRED_IN',
                    r.relationship_status = 'active',
                    r.relationship_strength = rel.relationship_strength,
                    r.location_type = rel.location_type,
                    r.risk_province = rel.risk_province,
                    r.risk_city = rel.risk_city,
                    r.risk_district = rel.risk_district,
                    r.data_source_node = rel.data_source_node,
                    r.remarks = rel.remarks,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """

                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")

            logger.info(f"总共成功创建 {success_count} 个保险理赔区域关系")
            return success_count > 0

        except Exception as e:
            logger.error(f"创建保险理赔区域关系失败: {str(e)}")
            return False
    
    def _is_valid_area_value(self, value: str) -> bool:
        """
        判断区域值是否有效
        
        Args:
            value: 待检查的值
            
        Returns:
            bool: 是否有效
        """
        if not value:
            return False
        
        value_str = str(value).strip().lower()
        invalid_values = ['', 'nan', 'null', 'none', 'n/a', 'na']
        return value_str not in invalid_values
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险理赔区域关系

        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            DELETE r
            """

            self._safe_execute_query(query)
            logger.info("已删除所有保险理赔区域关系")
            return True

        except Exception as e:
            logger.error(f"删除保险理赔区域关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔区域关系统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0

            # 有出险地关系的保险理赔数量
            claims_with_location_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            RETURN count(DISTINCT claims) as claims_count
            """
            claims_result = self._safe_execute_query(claims_with_location_query)
            claims_count = claims_result[0]['claims_count'] if claims_result else 0

            # 按位置类型统计
            location_type_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            WHERE r.location_type IS NOT NULL
            RETURN r.location_type as location_type, count(r) as count
            ORDER BY count DESC
            """
            location_type_result = self._safe_execute_query(location_type_query)
            location_type_stats = {record['location_type']: record['count'] for record in location_type_result}

            # 按区域统计（Top 10）
            area_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            RETURN area.name as area_name, count(r) as claims_count
            ORDER BY claims_count DESC
            LIMIT 10
            """
            area_result = self._safe_execute_query(area_query)
            area_stats = {record['area_name']: record['claims_count'] for record in area_result}

            # 数据源统计
            source_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}

            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险理赔区域关系",
                additional_stats={
                    "claims_with_location": claims_count,
                    "by_location_type": location_type_stats,
                    "by_area_name": area_stats,
                    "by_data_source_node": source_stats,
                }
            )

        except Exception as e:
            logger.error(f"获取保险理赔区域关系统计失败: {str(e)}")
            return {}

    def cleanup_duplicate_relationships(self) -> int:
        """
        清理重复的保险理赔区域关系
        对于相同的理赔-区域对，保留最早创建的关系，删除其他重复关系

        Returns:
            int: 删除的重复关系数量
        """
        try:
            # 查找重复的关系
            find_duplicates_query = """
            MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
            WITH claims, area, collect(r) as rels
            WHERE size(rels) > 1
            RETURN claims.case_no as case_no, area.code as area_code, rels
            """

            duplicates_result = self._safe_execute_query(find_duplicates_query)

            if not duplicates_result:
                logger.info("没有发现重复的保险理赔区域关系")
                return 0

            total_deleted = 0

            for record in duplicates_result:
                case_no = record['case_no']
                area_code = record['area_code']
                rels = record['rels']

                if len(rels) <= 1:
                    continue

                logger.info(f"发现重复的关系，理赔: {case_no}，区域: {area_code}，共 {len(rels)} 个")

                # 保留最早创建的关系，删除其他
                cleanup_query = """
                MATCH (claims:InsuranceClaims {case_no: $case_no})-[r:OCCURRED_IN]->(area:Area {code: $area_code})
                WITH r
                ORDER BY COALESCE(r.created_at, datetime()) ASC
                WITH collect(r) as rels
                WHERE size(rels) > 1
                UNWIND rels[1..] as duplicate_rel
                DELETE duplicate_rel
                RETURN count(duplicate_rel) as deleted_count
                """

                cleanup_result = self._safe_execute_query(cleanup_query, {
                    'case_no': case_no,
                    'area_code': area_code
                })
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count

                logger.info(f"清理了 {deleted_count} 个重复关系，理赔: {case_no}，区域: {area_code}")

            logger.info(f"总共清理了 {total_deleted} 个重复的保险理赔区域关系")
            return total_deleted

        except Exception as e:
            logger.error(f"清理重复关系失败: {str(e)}")
            return 0
    
    def search_claims_area_relationships(self,
                                       case_no: Optional[str] = None,
                                       area_name: Optional[str] = None,
                                       location_type: Optional[str] = None,
                                       risk_province: Optional[str] = None,
                                       risk_city: Optional[str] = None,
                                       risk_district: Optional[str] = None,
                                       include_node_details: bool = True,
                                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔区域位置关系

        Args:
            case_no: 案件号过滤条件
            area_name: 区域名称过滤条件
            location_type: 位置类型过滤条件（district/city/province）
            risk_province: 出险省份过滤条件
            risk_city: 出险城市过滤条件
            risk_district: 出险区县过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制

        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}

            if case_no:
                conditions.append("claims.case_no CONTAINS $case_no")
                parameters['case_no'] = case_no

            if area_name:
                conditions.append("area.name CONTAINS $area_name")
                parameters['area_name'] = area_name

            if location_type:
                conditions.append("r.location_type = $location_type")
                parameters['location_type'] = location_type

            if risk_province:
                conditions.append("r.risk_province = $risk_province")
                parameters['risk_province'] = risk_province

            if risk_city:
                conditions.append("r.risk_city = $risk_city")
                parameters['risk_city'] = risk_city

            if risk_district:
                conditions.append("r.risk_district = $risk_district")
                parameters['risk_district'] = risk_district

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            if include_node_details:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
                WHERE {where_clause}
                RETURN r,
                       {{case_no: claims.case_no, name: claims.name, business_id: claims.business_id}} as claims_info,
                       {{code: area.code, name: area.name, business_id: area.business_id}} as area_info
                ORDER BY claims.case_no, area.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:OCCURRED_IN]->(area:Area)
                WHERE {where_clause}
                RETURN r, claims.case_no as case_no, area.code as area_code
                ORDER BY claims.case_no, area.code
                LIMIT {limit}
                """

            results = self._safe_execute_query(query, parameters)

            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'OCCURRED_IN'
                }

                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass

                if include_node_details:
                    rel_data['claims'] = record['claims_info']
                    rel_data['area'] = record['area_info']
                else:
                    rel_data['case_no'] = record['case_no']
                    rel_data['area_code'] = record['area_code']

                relationships.append(rel_data)

            logger.info(f"搜索到 {len(relationships)} 个保险理赔区域关系")
            return relationships

        except Exception as e:
            logger.error(f"搜索保险理赔区域关系失败: {str(e)}")
            return []