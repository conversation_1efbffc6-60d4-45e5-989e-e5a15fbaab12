"""
保险经纪公司额外邮箱关系服务

专门负责 InsuranceBrokerageCompany 节点与 Email 节点之间关系的创建和管理
从Neo4j查询保险经纪公司的additional_emails属性，创建或关联对应的邮箱节点
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceBrokerageCompanyAdditionalEmailRelationshipService(BaseRelationshipService):
    """保险经纪公司额外邮箱关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险经纪公司数据，创建或关联Email节点，并建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险经纪公司与额外邮箱关系")
            
            # 1. 从Neo4j查询保险经纪公司数据
            company_data = self._query_company_additional_email_data_from_neo4j(limit)
            if not company_data:
                logger.warning("未找到有效的保险经纪公司额外邮箱数据")
                return True
            
            # 2. 创建缺失的Email节点
            created_emails = self._create_missing_emails(company_data)
            logger.info(f"创建了 {created_emails} 个Email节点")
            
            # 3. 创建关系
            success = self._create_company_additional_email_relationships(company_data)
            
            if success:
                logger.info(f"成功处理 {len(company_data)} 个保险经纪公司额外邮箱关系")
                return True
            else:
                logger.error("创建保险经纪公司额外邮箱关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保险经纪公司额外邮箱关系失败: {str(e)}")
            return False
    
    def _query_company_additional_email_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险经纪公司的额外邮箱数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保险经纪公司额外邮箱数据列表
        """
        try:
            # 构建查询条件
            conditions = ["company.additional_emails IS NOT NULL", "company.additional_emails <> ''"]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (company:InsuranceBrokerageCompany)
            WHERE {where_clause}
            RETURN company.code as company_code,
                   company.name as company_name,
                   company.additional_emails as additional_emails,
                   company.business_id as company_business_id
            ORDER BY company.code
            {limit_clause}
            """
            
            logger.info(f"查询保险经纪公司额外邮箱数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 处理额外邮箱数据，分割、验证和展开
            expanded_data = []
            for record in results:
                additional_emails_str = str(record.get('additional_emails', '')).strip()
                company_code = str(record.get('company_code', '')).strip()
                company_name = record.get('company_name', '')
                
                if additional_emails_str and additional_emails_str != 'nan' and additional_emails_str != 'None' and company_code:
                    # 处理额外邮箱字段
                    emails = self._process_additional_emails(additional_emails_str)
                    
                    # 为每个邮箱创建一条记录
                    for email in emails:
                        expanded_data.append({
                            'company_code': company_code,
                            'company_name': company_name,
                            'email': email,
                            'company_business_id': record.get('company_business_id', company_code)
                        })
            
            logger.info(f"找到 {len(expanded_data)} 个有效的保险经纪公司额外邮箱记录")
            return expanded_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险经纪公司额外邮箱数据失败: {str(e)}")
            return []
    
    def _process_additional_emails(self, additional_emails_str: str) -> List[str]:
        """
        处理额外邮箱字符串，分割并验证邮箱地址
        
        Args:
            additional_emails_str: 用分号分割的邮箱字符串
            
        Returns:
            List[str]: 有效的邮箱地址列表
        """
        try:
            if not additional_emails_str or additional_emails_str.strip() == '':
                return []
            
            # 按分号分割
            emails = additional_emails_str.split(';')
            
            # 去除前后空格，过滤无效邮箱
            valid_emails = []
            for email in emails:
                email = email.strip()
                # 简单的邮箱格式验证
                if email and email != 'nan' and email != 'None' and '@' in email:
                    valid_emails.append(email)
            
            # 去重
            valid_emails = list(set(valid_emails))
            
            return valid_emails
            
        except Exception as e:
            logger.error(f"处理额外邮箱字符串失败: {str(e)}")
            return []
    
    def _create_missing_emails(self, company_data: List[Dict[str, Any]]) -> int:
        """
        创建缺失的Email节点
        
        Args:
            company_data: 保险经纪公司数据列表
            
        Returns:
            int: 创建的节点数量
        """
        try:
            # 提取所有邮箱地址
            emails = [data['email'] for data in company_data]
            
            if not emails:
                return 0
            
            # 使用MERGE确保不会创建重复的Email节点
            # MERGE会先查找，如果不存在才创建
            create_query = """
            UNWIND $emails as email
            MERGE (emailNode:Email {code: email})
            ON CREATE SET 
                emailNode.business_id = email,
                emailNode.created_at = datetime(),
                emailNode.updated_at = datetime()
            ON MATCH SET 
                emailNode.updated_at = datetime()
            RETURN count(emailNode) as processed_count
            """
            
            result = self._safe_execute_query(create_query, {'emails': emails})
            processed_count = result[0]['processed_count'] if result else 0
            
            logger.info(f"处理了 {processed_count} 个Email节点（包含新创建和已存在的）")
            return processed_count
            
        except Exception as e:
            logger.error(f"创建Email节点失败: {str(e)}")
            return 0
    
    def _create_company_additional_email_relationships(self, company_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险经纪公司与额外邮箱的关系
        
        Args:
            company_data: 保险经纪公司数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(company_data), batch_size):
                batch_data = company_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (company:InsuranceBrokerageCompany {code: rel.company_code})
                MATCH (emailNode:Email {code: rel.email})
                MERGE (company)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode)
                SET r.relationship_type = 'HAS_ADDITIONAL_EMAIL',
                    r.relationship_status = 'active',
                    r.relationship_strength = 7,
                    r.data_source_node = 'InsuranceBrokerageCompany',
                    r.remarks = '保险经纪公司额外邮箱: ' + rel.company_name + ' - ' + rel.email,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险经纪公司额外邮箱关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险经纪公司额外邮箱关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险经纪公司额外邮箱关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险经纪公司额外邮箱关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险经纪公司额外邮箱关系失败: {str(e)}")
            return False
    
    def delete_orphaned_emails(self) -> int:
        """
        删除孤立的Email节点（没有任何关系的节点）
        
        Returns:
            int: 删除的节点数量
        """
        try:
            query = """
            MATCH (emailNode:Email)
            WHERE NOT (emailNode)--()
            DELETE emailNode
            RETURN count(emailNode) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = result[0]['deleted_count'] if result else 0
            
            logger.info(f"删除了 {deleted_count} 个孤立的Email节点")
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除孤立Email节点失败: {str(e)}")
            return 0
    
    def cleanup_duplicate_emails(self) -> int:
        """
        清理重复的Email节点
        对于相同code的节点，保留最早创建的节点，删除其他重复节点
        
        Returns:
            int: 删除的重复节点数量
        """
        try:
            # 查找重复的Email节点
            find_duplicates_query = """
            MATCH (emailNode:Email)
            WITH emailNode.code as code, collect(emailNode) as emails
            WHERE size(emails) > 1
            RETURN code, emails
            """
            
            duplicates_result = self._safe_execute_query(find_duplicates_query)
            
            if not duplicates_result:
                logger.info("没有发现重复的Email节点")
                return 0
            
            total_deleted = 0
            
            for record in duplicates_result:
                code = record['code']
                emails = record['emails']
                
                if len(emails) <= 1:
                    continue
                
                logger.info(f"发现重复的Email节点，code: {code}，共 {len(emails)} 个")
                
                # 对于每组重复节点，保留最早创建的节点，删除其他
                cleanup_query = """
                MATCH (emailNode:Email {code: $code})
                WITH emailNode 
                ORDER BY COALESCE(emailNode.created_at, datetime()) ASC
                WITH collect(emailNode) as emails
                WHERE size(emails) > 1
                UNWIND emails[1..] as duplicate_email
                
                // 将所有关系转移到第一个（最早的）节点
                OPTIONAL MATCH (duplicate_email)-[r]-()
                WITH duplicate_email, collect(r) as rels, emails[0] as keep_email
                
                // 重新创建关系到保留的节点
                UNWIND rels as rel
                WITH duplicate_email, rel, keep_email,
                     startNode(rel) as start_node, 
                     endNode(rel) as end_node,
                     type(rel) as rel_type,
                     properties(rel) as rel_props
                
                // 如果重复节点是起始节点
                FOREACH (ignore IN CASE WHEN start_node = duplicate_email THEN [1] ELSE [] END |
                    MERGE (keep_email)-[new_rel:HAS_ADDITIONAL_EMAIL]->(end_node)
                    SET new_rel = rel_props
                )
                
                // 如果重复节点是结束节点  
                FOREACH (ignore IN CASE WHEN end_node = duplicate_email THEN [1] ELSE [] END |
                    MERGE (start_node)-[new_rel:HAS_ADDITIONAL_EMAIL]->(keep_email)
                    SET new_rel = rel_props
                )
                
                // 删除原关系和重复节点
                DELETE rel, duplicate_email
                RETURN count(duplicate_email) as deleted_count
                """
                
                cleanup_result = self._safe_execute_query(cleanup_query, {'code': code})
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count
                
                logger.info(f"清理了 {deleted_count} 个重复的Email节点，code: {code}")
            
            logger.info(f"总共清理了 {total_deleted} 个重复的Email节点")
            return total_deleted
            
        except Exception as e:
            logger.error(f"清理重复Email节点失败: {str(e)}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险经纪公司额外邮箱关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有额外邮箱的保险经纪公司数量
            company_with_additional_email_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
            RETURN count(DISTINCT company) as company_count
            """
            company_result = self._safe_execute_query(company_with_additional_email_query)
            company_count = company_result[0]['company_count'] if company_result else 0
            
            # Email节点总数
            email_total_query = """
            MATCH (emailNode:Email)
            RETURN count(emailNode) as email_total
            """
            email_result = self._safe_execute_query(email_total_query)
            email_total = email_result[0]['email_total'] if email_result else 0
            
            # 孤立的Email节点数量
            orphaned_query = """
            MATCH (emailNode:Email)
            WHERE NOT (emailNode)--()
            RETURN count(emailNode) as orphaned_count
            """
            orphaned_result = self._safe_execute_query(orphaned_query)
            orphaned_count = orphaned_result[0]['orphaned_count'] if orphaned_result else 0
            
            # 重复的Email节点统计
            duplicate_query = """
            MATCH (emailNode:Email)
            WITH emailNode.code as code, count(emailNode) as email_count
            WHERE email_count > 1
            RETURN count(code) as duplicate_codes, sum(email_count - 1) as duplicate_nodes
            """
            duplicate_result = self._safe_execute_query(duplicate_query)
            duplicate_codes = duplicate_result[0]['duplicate_codes'] if duplicate_result else 0
            duplicate_nodes = duplicate_result[0]['duplicate_nodes'] if duplicate_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险经纪公司额外邮箱关系",
                additional_stats={
                    "companies_with_additional_email": company_count,
                    "total_emails": email_total,
                    "orphaned_emails": orphaned_count,
                    "duplicate_email_codes": duplicate_codes,
                    "duplicate_email_nodes": duplicate_nodes,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险经纪公司额外邮箱关系统计失败: {str(e)}")
            return {}
    
    def search_company_additional_email_relationships(self, 
                                                    company_code: Optional[str] = None,
                                                    company_name: Optional[str] = None,
                                                    email: Optional[str] = None,
                                                    include_node_details: bool = True,
                                                    limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险经纪公司额外邮箱关系
        
        Args:
            company_code: 保险经纪公司编码过滤条件
            company_name: 保险经纪公司名称过滤条件
            email: 邮箱地址过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if company_code:
                conditions.append("company.code CONTAINS $company_code")
                parameters['company_code'] = company_code
            
            if company_name:
                conditions.append("company.name CONTAINS $company_name")
                parameters['company_name'] = company_name
            
            if email:
                conditions.append("emailNode.code CONTAINS $email")
                parameters['email'] = email
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
                WHERE {where_clause}
                RETURN r,
                       {{code: company.code, name: company.name, business_id: company.business_id}} as company_info,
                       {{code: emailNode.code, name: emailNode.name, business_id: emailNode.business_id}} as email_info
                ORDER BY company.code, emailNode.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:HAS_ADDITIONAL_EMAIL]->(emailNode:Email)
                WHERE {where_clause}
                RETURN r, company.code as company_code, emailNode.code as email
                ORDER BY company.code, emailNode.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'HAS_ADDITIONAL_EMAIL'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['company'] = record['company_info']
                    rel_data['email'] = record['email_info']
                else:
                    rel_data['company_code'] = record['company_code']
                    rel_data['email'] = record['email']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险经纪公司额外邮箱关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险经纪公司额外邮箱关系失败: {str(e)}")
            return [] 