"""
保险代理人员团队关系服务

专门负责 InsuranceAgentPerson 节点与 InsuranceSalesChannel 节点之间关系的创建和管理
基于 InsuranceAgentPerson 的 team_id 属性关联对应的销售渠道（团队）
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceAgentTeamRelationshipService(BaseRelationshipService):
    """保险代理人员团队关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        基于 InsuranceAgentPerson 的 team_id 创建与 InsuranceSalesChannel 的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始创建保险代理人员与团队关系")
            
            # 1. 从Neo4j查询保险代理人员和团队匹配数据
            agent_data = self._query_agent_team_data_from_neo4j(limit)
            if not agent_data:
                logger.warning("未找到有效的保险代理人员-团队匹配数据")
                return True
            
            # 2. 创建关系
            success = self._create_agent_team_relationships(agent_data)
            
            if success:
                logger.info(f"成功处理 {len(agent_data)} 个保险代理人员团队关系")
                return True
            else:
                logger.error("创建保险代理人员团队关系失败")
                return False
            
        except Exception as e:
            logger.error(f"创建保险代理人员团队关系失败: {str(e)}")
            return False
    
    def _query_agent_team_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险代理人员和团队的匹配数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 匹配数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "agent.team_id IS NOT NULL", 
                "agent.team_id <> ''",
                "agent.team_id <> 'nan'"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (agent:InsuranceAgentPerson)
            WHERE {where_clause}
            MATCH (team:InsuranceSalesChannel {{code: agent.team_id}})
            RETURN agent.code as agent_code,
                   agent.name as agent_name,
                   agent.team_id as team_id,
                   team.code as team_code,
                   team.name as team_name
            ORDER BY agent.code
            {limit_clause}
            """
            
            logger.info(f"查询保险代理人员-团队匹配数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                agent_code = str(record.get('agent_code', '')).strip()
                team_id = str(record.get('team_id', '')).strip()
                team_code = str(record.get('team_code', '')).strip()
                
                if agent_code and team_id and team_code:
                    valid_data.append({
                        'agent_code': agent_code,
                        'agent_name': record.get('agent_name', ''),
                        'team_id': team_id,
                        'team_code': team_code,
                        'team_name': record.get('team_name', '')
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保险代理人员-团队匹配记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险代理人员-团队匹配数据失败: {str(e)}")
            return []
    
    def _create_agent_team_relationships(self, agent_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险代理人员与团队的关系
        
        Args:
            agent_data: 代理人员数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(agent_data), batch_size):
                batch_data = agent_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (agent:InsuranceAgentPerson {code: rel.agent_code})
                MATCH (team:InsuranceSalesChannel {code: rel.team_code})
                MERGE (agent)-[r:BELONGS_TO_TEAM]->(team)
                SET r.relationship_type = 'BELONGS_TO_TEAM',
                    r.relationship_status = 'active',
                    r.relationship_strength = 8,
                    r.data_source_node = 'InsuranceAgentPerson',
                    r.remarks = '保险代理人员所属团队: ' + rel.agent_name + ' -> ' + rel.team_name,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险代理人员团队关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险代理人员团队关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险代理人员团队关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险代理人员团队关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险代理人员团队关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险代理人员团队关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有团队的代理人员数量
            agent_with_team_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
            RETURN count(DISTINCT agent) as agent_count
            """
            agent_result = self._safe_execute_query(agent_with_team_query)
            agent_count = agent_result[0]['agent_count'] if agent_result else 0
            
            # 有代理人员的团队数量
            team_with_agent_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
            RETURN count(DISTINCT team) as team_count
            """
            team_result = self._safe_execute_query(team_with_agent_query)
            team_count = team_result[0]['team_count'] if team_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险代理人员团队关系",
                additional_stats={
                    "agents_with_team": agent_count,
                    "teams_with_agent": team_count,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险代理人员团队关系统计失败: {str(e)}")
            return {}
    
    def search_agent_team_relationships(self, 
                                      agent_code: Optional[str] = None,
                                      agent_name: Optional[str] = None,
                                      team_code: Optional[str] = None,
                                      team_name: Optional[str] = None,
                                      include_node_details: bool = True,
                                      limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险代理人员团队关系
        
        Args:
            agent_code: 代理人员编码过滤条件
            agent_name: 代理人员姓名过滤条件
            team_code: 团队编码过滤条件
            team_name: 团队名称过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if agent_code:
                conditions.append("agent.code CONTAINS $agent_code")
                parameters['agent_code'] = agent_code
            
            if agent_name:
                conditions.append("agent.name CONTAINS $agent_name")
                parameters['agent_name'] = agent_name
            
            if team_code:
                conditions.append("team.code CONTAINS $team_code")
                parameters['team_code'] = team_code
            
            if team_name:
                conditions.append("team.name CONTAINS $team_name")
                parameters['team_name'] = team_name
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r,
                       {{code: agent.code, name: agent.name, team_id: agent.team_id}} as agent_info,
                       {{code: team.code, name: team.name}} as team_info
                ORDER BY agent.code, team.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (agent:InsuranceAgentPerson)-[r:BELONGS_TO_TEAM]->(team:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r, agent.code as agent_code, team.code as team_code
                ORDER BY agent.code, team.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'BELONGS_TO_TEAM'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['agent'] = record['agent_info']
                    rel_data['team'] = record['team_info']
                else:
                    rel_data['agent_code'] = record['agent_code']
                    rel_data['team_code'] = record['team_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险代理人员团队关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险代理人员团队关系失败: {str(e)}")
            return [] 