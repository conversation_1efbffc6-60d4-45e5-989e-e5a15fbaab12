"""
国家标准行业层级关系服务

专门负责 NationalStandardIndustry 节点之间的层级关系创建和管理
根据节点的parent_code属性创建与上级节点的PARENT_OF关系
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class NationalStandardIndustryHierarchyRelationshipService(BaseRelationshipService):
    """国家标准行业层级关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询国家标准行业数据，根据parent_code创建层级关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建国家标准行业层级关系")
            
            # 1. 从Neo4j查询国家标准行业数据
            industry_data = self._query_industry_hierarchy_data_from_neo4j(limit)
            if not industry_data:
                logger.warning("未找到有效的国家标准行业层级数据")
                return True
            
            # 2. 创建层级关系
            success = self._create_industry_hierarchy_relationships(industry_data)
            
            if success:
                logger.info(f"成功处理 {len(industry_data)} 个国家标准行业层级关系")
                return True
            else:
                logger.error("创建国家标准行业层级关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建国家标准行业层级关系失败: {str(e)}")
            return False
    
    def _query_industry_hierarchy_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询国家标准行业的层级数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 行业层级数据列表
        """
        try:
            # 构建查询条件 - 只查询有parent_code的节点
            conditions = [
                "child.parent_code IS NOT NULL", 
                "child.parent_code <> ''",
                "child.parent_code <> 'NULL'"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (child:Industry:NationalStandardIndustry)
            WHERE {where_clause}
            RETURN child.code as child_code,
                   child.name as child_name,
                   child.parent_code as parent_code,
                   child.level as child_level,
                   child.business_id as child_business_id
            ORDER BY child.level, child.code
            {limit_clause}
            """
            
            logger.info(f"查询国家标准行业层级数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                child_code = str(record.get('child_code', '')).strip()
                parent_code = str(record.get('parent_code', '')).strip()
                
                if child_code and parent_code and parent_code != 'nan' and parent_code != 'None':
                    valid_data.append({
                        'child_code': child_code,
                        'child_name': record.get('child_name', ''),
                        'parent_code': parent_code,
                        'child_level': record.get('child_level'),
                        'child_business_id': record.get('child_business_id', child_code)
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的国家标准行业层级记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j国家标准行业层级数据失败: {str(e)}")
            return []
    
    def _create_industry_hierarchy_relationships(self, industry_data: List[Dict[str, Any]]) -> bool:
        """
        创建国家标准行业层级关系
        
        Args:
            industry_data: 行业数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(industry_data), batch_size):
                batch_data = industry_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (parent:Industry:NationalStandardIndustry {code: rel.parent_code})
                MATCH (child:Industry:NationalStandardIndustry {code: rel.child_code})
                MERGE (parent)-[r:PARENT_OF]->(child)
                SET r.relationship_type = 'PARENT_OF',
                    r.relationship_status = 'active',
                    r.relationship_strength = 10,
                    r.data_source_node = 'NationalStandardIndustry',
                    r.remarks = '国家标准行业层级关系: ' + parent.name + ' -> ' + child.name,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个层级关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 层级关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个国家标准行业层级关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建国家标准行业层级关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有国家标准行业层级关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (parent:Industry:NationalStandardIndustry)-[r:PARENT_OF]->(child:Industry:NationalStandardIndustry)
            DELETE r
            RETURN count(r) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = 0
            if result:
                deleted_count = result[0].get('deleted_count', 0)
            
            logger.info(f"删除了 {deleted_count} 个国家标准行业层级关系")
            return True
            
        except Exception as e:
            logger.error(f"删除国家标准行业层级关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取国家标准行业层级关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数量
            total_query = """
            MATCH (parent:Industry:NationalStandardIndustry)-[r:PARENT_OF]->(child:Industry:NationalStandardIndustry)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按层级统计
            level_query = """
            MATCH (parent:Industry:NationalStandardIndustry)-[r:PARENT_OF]->(child:Industry:NationalStandardIndustry)
            WHERE child.level IS NOT NULL
            RETURN child.level as child_level, count(r) as count
            ORDER BY child_level
            """
            level_result = self._safe_execute_query(level_query)
            level_stats = {record['child_level']: record['count'] for record in level_result}
            
            # 孤立节点统计（没有父节点的节点）
            orphan_query = """
            MATCH (industry:Industry:NationalStandardIndustry)
            WHERE NOT (industry)<-[:PARENT_OF]-()
            AND industry.level > 1
            RETURN count(industry) as orphan_count
            """
            orphan_result = self._safe_execute_query(orphan_query)
            orphan_count = orphan_result[0]['orphan_count'] if orphan_result else 0
            
            # 根节点统计（没有parent_code的节点）
            root_query = """
            MATCH (industry:Industry:NationalStandardIndustry)
            WHERE (industry.parent_code IS NULL OR industry.parent_code = '' OR industry.parent_code = 'NULL')
            RETURN count(industry) as root_count
            """
            root_result = self._safe_execute_query(root_query)
            root_count = root_result[0]['root_count'] if root_result else 0
            
            return {
                "total_count": total_count,
                "relationship_type": "PARENT_OF",
                "by_child_level": level_stats,
                "orphan_nodes": orphan_count,
                "root_nodes": root_count,
                "description": "国家标准行业层级关系统计"
            }
            
        except Exception as e:
            logger.error(f"获取国家标准行业层级关系统计失败: {str(e)}")
            return {
                "total_count": 0,
                "relationship_type": "PARENT_OF",
                "error": str(e)
            }
    
    def get_industry_hierarchy_tree(self, level: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取国家标准行业层级树结构
        
        Args:
            level: 指定层级（可选）
            
        Returns:
            List[Dict[str, Any]]: 层级树数据
        """
        try:
            level_filter = f"AND parent.level = {level}" if level is not None else ""
            
            query = f"""
            MATCH (parent:Industry:NationalStandardIndustry)-[r:PARENT_OF]->(child:Industry:NationalStandardIndustry)
            WHERE 1=1 {level_filter}
            RETURN parent.code as parent_code,
                   parent.name as parent_name,
                   parent.level as parent_level,
                   child.code as child_code,
                   child.name as child_name,
                   child.level as child_level
            ORDER BY parent.level, parent.code, child.code
            """
            
            results = self._safe_execute_query(query)
            
            hierarchy_data = []
            for record in results:
                hierarchy_data.append({
                    'parent_code': record.get('parent_code'),
                    'parent_name': record.get('parent_name'),
                    'parent_level': record.get('parent_level'),
                    'child_code': record.get('child_code'),
                    'child_name': record.get('child_name'),
                    'child_level': record.get('child_level')
                })
            
            logger.info(f"获取到 {len(hierarchy_data)} 个层级关系")
            return hierarchy_data
            
        except Exception as e:
            logger.error(f"获取国家标准行业层级树失败: {str(e)}")
            return [] 