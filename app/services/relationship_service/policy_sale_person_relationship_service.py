"""
保单销售人员关系服务

专门负责保单 Policy 节点与销售人员节点之间关系的创建和管理
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicySalePersonRelationshipService(BaseRelationshipService):
    """保单销售人员关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None,
                                     custom_day: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保单与销售人员的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期（格式：YYYY-MM-DD），默认使用昨天
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保单与销售人员关系")
            
            # 1. 从Hive查询保单销售人员数据
            hive_data = self._query_policy_sale_person_data_from_hive(limit, where_conditions, custom_day)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保单销售人员数据")
                return True
            
            # 2. 直接构建关系数据（假设节点都已存在）
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保单销售人员关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保单销售人员关系")
                return True
            else:
                logger.error("批量创建保单销售人员关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保单销售人员关系失败: {str(e)}")
            return False
    
    def _query_policy_sale_person_data_from_hive(self, 
                                               limit: Optional[int] = None,
                                               where_conditions: Optional[str] = None,
                                               custom_day: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从Hive查询保单销售人员数据
        
        Args:
            limit: 记录数限制
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期，默认使用昨天
            
        Returns:
            Optional[pd.DataFrame]: 查询结果
        """
        try:
            # 确定日期
            if custom_day:
                day_value = custom_day
            else:
                day_value = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 构建查询条件
            conditions = [
                "policy_code IS NOT NULL",
                "sale_code IS NOT NULL",
                "sale_name IS NOT NULL",
                f"`day` = '{day_value}'"
            ]
            
            # 添加产品代码过滤
            conditions.append("product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')")
            
            if where_conditions:
                conditions.append(where_conditions)
            
            where_clause = " AND ".join(conditions)
            
            # 构建LIMIT子句
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            SELECT
                policy_code,
                policy_start_date,
                policy_end_date,
                policy_status,
                insurance_code,
                insurance_name,
                insure_code,
                insure_name,
                insured_code,
                insured_name,
                product_code,
                product_name,
                CAST(channel_id AS STRING) as channel_id,
                channel_name,
                sale_code,
                sale_name,
                sale_mobile,
                assure_time,
                sku_ratio,
                policy_coverage,
                industry
            FROM dwadb.dwa_policy_common_data_test
            WHERE {where_clause}
            ORDER BY policy_code ASC
            {limit_clause}
            """
            
            logger.info(f"查询保单销售人员数据，使用日期: {day_value}")
            return self.query_hive_data(query)
            
        except Exception as e:
            logger.error(f"查询Hive保单销售人员数据失败: {str(e)}")
            return None
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保单与销售人员的关系数据
        
        Args:
            hive_data: Hive保单销售人员数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        
        for _, row in hive_data.iterrows():
            policy_code = str(row.get('policy_code', '')).strip()
            sale_code = str(row.get('sale_code', '')).strip()
            sale_name = str(row.get('sale_name', '')).strip()
            sale_mobile = str(row.get('sale_mobile', '')).strip()
            product_code = str(row.get('product_code', '')).strip()
            product_name = str(row.get('product_name', '')).strip()
            
            # 跳过无效数据
            if not policy_code or policy_code == 'nan' or not sale_code or sale_code == 'nan':
                continue
            
            relationship_data.append({
                'policy_code': policy_code,
                'sale_code': sale_code,
                'remarks': f"保单销售关系: 保单 {policy_code} 由销售人员 {sale_name}({sale_code}) 销售",
                'sale_name': sale_name,
                'sale_mobile': sale_mobile,
                'product_code': product_code,
                'product_name': product_name,
                'data_source_node': 'Policy',
                'relationship_strength': 8,
                'relationship_type': '销售关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保单销售人员关系")
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code匹配节点
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                cypher_query = """
                UNWIND $relationships AS rel
                MATCH (p:Policy {code: rel.policy_code})
                MATCH (s) WHERE s.code = rel.sale_code 
                      AND (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
                MERGE (p)-[r:SOLD_BY]->(s)
                SET r.remarks = rel.remarks,
                    r.sale_name = rel.sale_name,
                    r.sale_mobile = rel.sale_mobile,
                    r.product_code = rel.product_code,
                    r.product_name = rel.product_name,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(cypher_query, {'relationships': batch})
                
                if result:
                    batch_success = result[0]['created_count'] if result[0]['created_count'] else 0
                    success_count += batch_success
                    logger.info(f"批量 {i//batch_size + 1}: 成功创建 {batch_success} 个关系")
                else:
                    logger.warning(f"批量 {i//batch_size + 1}: 未创建任何关系")
            
            logger.info(f"总共成功创建 {success_count} 个保单销售人员关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保单销售人员关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (p:Policy)-[r:SOLD_BY]->(s)
            WHERE (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保单销售人员关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保单销售人员关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单销售人员关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数
            total_query = """
            MATCH (p:Policy)-[r:SOLD_BY]->(s)
            WHERE (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
            RETURN count(r) as total_count
            """
            
            result = self._safe_execute_query(total_query)
            total_count = result[0]['total_count'] if result else 0
            
            # 按销售人员类型统计
            type_query = """
            MATCH (p:Policy)-[r:SOLD_BY]->(s)
            WHERE (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
            RETURN labels(s) as person_type, count(r) as count
            ORDER BY count DESC
            """
            
            type_result = self._safe_execute_query(type_query)
            type_stats = {record['person_type'][1] if len(record['person_type']) > 1 else record['person_type'][0]: record['count'] for record in type_result}
            
            # 按产品代码统计
            product_query = """
            MATCH (p:Policy)-[r:SOLD_BY]->(s)
            WHERE (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
            RETURN r.product_code as product_code, count(r) as count
            ORDER BY count DESC
            LIMIT 10
            """
            
            product_result = self._safe_execute_query(product_query)
            product_stats = {record['product_code']: record['count'] for record in product_result}
            
            # 按数据源节点统计
            source_query = """
            MATCH (p:Policy)-[r:SOLD_BY]->(s)
            WHERE (s:InsuranceBDPerson OR s:InsuranceAgentPerson)
            RETURN s.data_source_node as source, count(r) as count
            ORDER BY count DESC
            """
            
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保单销售人员关系",
                additional_stats={
                    "by_person_type": type_stats,
                    "by_product_code": product_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保单销售人员关系统计失败: {str(e)}")
            return {}
    
    def search_sales_relationships(self, 
                                 policy_code: Optional[str] = None,
                                 sale_code: Optional[str] = None,
                                 sale_name: Optional[str] = None,
                                 product_code: Optional[str] = None,
                                 person_type: Optional[str] = None,
                                 include_node_details: bool = True,
                                 limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单销售人员关系
        
        Args:
            policy_code: 保单编码过滤条件
            sale_code: 销售人员编码过滤条件
            sale_name: 销售人员姓名过滤条件
            product_code: 产品代码过滤条件
            person_type: 销售人员类型过滤条件 (InsuranceBDPerson/InsuranceAgentPerson)
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = ["(s:InsuranceBDPerson OR s:InsuranceAgentPerson)"]
            
            if policy_code:
                conditions.append(f"p.code CONTAINS '{policy_code}'")
            
            if sale_code:
                conditions.append(f"s.code CONTAINS '{sale_code}'")
            
            if sale_name:
                conditions.append(f"s.name CONTAINS '{sale_name}'")
            
            if product_code:
                conditions.append(f"r.product_code CONTAINS '{product_code}'")
            
            if person_type:
                conditions.append(f"s:{person_type}")
            
            where_clause = " AND ".join(conditions)
            
            if include_node_details:
                query = f"""
                MATCH (p:Policy)-[r:SOLD_BY]->(s)
                WHERE {where_clause}
                RETURN p, r, s, labels(s) as person_labels
                ORDER BY r.created_at DESC
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (p:Policy)-[r:SOLD_BY]->(s)
                WHERE {where_clause}
                RETURN r, p.code as policy_code, s.code as sale_code, s.name as sale_name, labels(s) as person_labels
                ORDER BY r.created_at DESC
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query)
            
            relationships = []
            for record in results:
                if include_node_details:
                    relationships.append({
                        "policy": dict(record["p"]),
                        "relationship": dict(record["r"]),
                        "sale_person": dict(record["s"]),
                        "person_labels": record["person_labels"]
                    })
                else:
                    rel_data = dict(record["r"])
                    rel_data.update({
                        "policy_code": record["policy_code"],
                        "sale_code": record["sale_code"],
                        "sale_name": record["sale_name"],
                        "person_labels": record["person_labels"]
                    })
                    relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保单销售人员关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保单销售人员关系失败: {str(e)}")
            return [] 