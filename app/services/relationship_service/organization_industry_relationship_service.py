"""
机构行业关系服务

专门负责 Organization 节点与 NationalStandardIndustry 节点之间关系的创建和管理
根据Organization的industry_medium属性关联到NationalStandardIndustry的name字段
注意：匹配时限定为中类（level=3）行业，避免不同层级间的重名问题
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class OrganizationIndustryRelationshipService(BaseRelationshipService):
    """机构行业关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询Organization数据，根据industry_medium关联到NationalStandardIndustry节点
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建机构与国家标准行业关系")
            
            # 1. 从Neo4j查询机构数据
            org_data = self._query_organization_industry_data_from_neo4j(limit)
            if not org_data:
                logger.warning("未找到有效的机构行业数据")
                return True
            
            # 2. 创建关系
            success = self._create_organization_industry_relationships(org_data)
            
            if success:
                logger.info(f"成功处理 {len(org_data)} 个机构行业关系")
                return True
            else:
                logger.error("创建机构行业关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建机构行业关系失败: {str(e)}")
            return False
    
    def _query_organization_industry_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询机构的行业数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 机构行业数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "org.industry_medium IS NOT NULL", 
                "org.industry_medium <> ''",
                "org.industry_medium <> 'NULL'"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (org:Organization)
            WHERE {where_clause}
            RETURN org.code as org_code,
                   org.name as org_name,
                   org.industry_medium as industry_medium,
                   org.business_id as org_business_id
            ORDER BY org.code
            {limit_clause}
            """
            
            logger.info(f"查询机构行业数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                org_code = str(record.get('org_code', '')).strip()
                industry_medium = str(record.get('industry_medium', '')).strip()
                
                if org_code and industry_medium and industry_medium != 'nan' and industry_medium != 'None':
                    valid_data.append({
                        'org_code': org_code,
                        'org_name': record.get('org_name', ''),
                        'industry_medium': industry_medium,
                        'org_business_id': record.get('org_business_id', org_code)
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的机构行业记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j机构行业数据失败: {str(e)}")
            return []
    
    def _create_organization_industry_relationships(self, org_data: List[Dict[str, Any]]) -> bool:
        """
        创建机构与国家标准行业的关系
        
        Args:
            org_data: 机构数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            failed_count = 0
            
            for i in range(0, len(org_data), batch_size):
                batch_data = org_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (org:Organization {code: rel.org_code})
                MATCH (industry:Industry:NationalStandardIndustry {name: rel.industry_medium, level: 3})
                MERGE (org)-[r:BELONGS_TO_INDUSTRY]->(industry)
                SET r.relationship_type = 'BELONGS_TO_INDUSTRY',
                    r.relationship_status = 'active',
                    r.relationship_strength = 9,
                    r.data_source_node = 'Organization',
                    r.remarks = '机构所属行业: ' + rel.org_name + ' -> ' + rel.industry_medium + ' (中类)',
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    batch_failed = len(batch_data) - batch_count
                    failed_count += batch_failed
                    
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系，失败 {batch_failed} 个")
                    
                    if batch_failed > 0:
                        # 记录失败的行业名称用于调试
                        failed_industries = [data['industry_medium'] for data in batch_data]
                        logger.warning(f"部分行业名称未找到匹配的NationalStandardIndustry节点: {set(failed_industries)}")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
                    failed_count += len(batch_data)
            
            logger.info(f"总共成功创建 {success_count} 个机构行业关系，失败 {failed_count} 个")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建机构行业关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有机构行业关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (org:Organization)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            DELETE r
            RETURN count(r) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = 0
            if result:
                deleted_count = result[0].get('deleted_count', 0)
            
            logger.info(f"删除了 {deleted_count} 个机构行业关系")
            return True
            
        except Exception as e:
            logger.error(f"删除机构行业关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取机构行业关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数量
            total_query = """
            MATCH (org:Organization)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按行业层级统计
            level_query = """
            MATCH (org:Organization)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            WHERE industry.level IS NOT NULL
            RETURN industry.level as industry_level, count(r) as count
            ORDER BY industry_level
            """
            level_result = self._safe_execute_query(level_query)
            level_stats = {record['industry_level']: record['count'] for record in level_result}
            
            # 最热门的行业（关联机构最多的行业）
            popular_query = """
            MATCH (org:Organization)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            RETURN industry.name as industry_name, 
                   industry.level as industry_level,
                   count(r) as org_count
            ORDER BY org_count DESC
            LIMIT 10
            """
            popular_result = self._safe_execute_query(popular_query)
            popular_industries = []
            for record in popular_result:
                popular_industries.append({
                    'name': record['industry_name'],
                    'level': record['industry_level'],
                    'organization_count': record['org_count']
                })
            
            # 没有行业关联的机构数量
            unmatched_query = """
            MATCH (org:Organization)
            WHERE org.industry_medium IS NOT NULL 
            AND org.industry_medium <> ''
            AND NOT (org)-[:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            RETURN count(org) as unmatched_count
            """
            unmatched_result = self._safe_execute_query(unmatched_query)
            unmatched_count = unmatched_result[0]['unmatched_count'] if unmatched_result else 0
            
            return {
                "total_count": total_count,
                "relationship_type": "BELONGS_TO_INDUSTRY",
                "by_industry_level": level_stats,
                "popular_industries": popular_industries,
                "unmatched_organizations": unmatched_count,
                "description": "机构行业关系统计"
            }
            
        except Exception as e:
            logger.error(f"获取机构行业关系统计失败: {str(e)}")
            return {
                "total_count": 0,
                "relationship_type": "BELONGS_TO_INDUSTRY",
                "error": str(e)
            }
    
    def get_unmatched_industries(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取未匹配的行业名称（Organization中有但NationalStandardIndustry中没有的）
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 未匹配的行业数据
        """
        try:
            query = f"""
            MATCH (org:Organization)
            WHERE org.industry_medium IS NOT NULL 
            AND org.industry_medium <> ''
            AND NOT EXISTS {{
                MATCH (industry:Industry:NationalStandardIndustry {{name: org.industry_medium, level: 3}})
            }}
            RETURN org.industry_medium as industry_name, 
                   count(org) as org_count
            ORDER BY org_count DESC
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            unmatched_data = []
            for record in results:
                unmatched_data.append({
                    'industry_name': record.get('industry_name'),
                    'organization_count': record.get('org_count', 0)
                })
            
            logger.info(f"找到 {len(unmatched_data)} 个未匹配的行业名称")
            return unmatched_data
            
        except Exception as e:
            logger.error(f"获取未匹配行业失败: {str(e)}")
            return []
    
    def search_organization_industry_relationships(self, 
                                                  org_code: Optional[str] = None,
                                                  org_name: Optional[str] = None,
                                                  industry_name: Optional[str] = None,
                                                  industry_level: Optional[int] = None,
                                                  limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索机构行业关系
        
        Args:
            org_code: 机构编码过滤条件
            org_name: 机构名称过滤条件
            industry_name: 行业名称过滤条件
            industry_level: 行业层级过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if org_code:
                conditions.append(f"org.code CONTAINS '{org_code}'")
            
            if org_name:
                conditions.append(f"org.name CONTAINS '{org_name}'")
            
            if industry_name:
                conditions.append(f"industry.name CONTAINS '{industry_name}'")
            
            if industry_level is not None:
                conditions.append(f"industry.level = {industry_level}")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (org:Organization)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:NationalStandardIndustry)
            WHERE {where_clause}
            RETURN org.code as org_code,
                   org.name as org_name,
                   industry.code as industry_code,
                   industry.name as industry_name,
                   industry.level as industry_level,
                   r.created_at as relationship_created_at
            ORDER BY org.code
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            relationship_data = []
            for record in results:
                relationship_data.append({
                    'org_code': record.get('org_code'),
                    'org_name': record.get('org_name'),
                    'industry_code': record.get('industry_code'),
                    'industry_name': record.get('industry_name'),
                    'industry_level': record.get('industry_level'),
                    'created_at': record.get('relationship_created_at')
                })
            
            logger.info(f"搜索到 {len(relationship_data)} 个机构行业关系")
            return relationship_data
            
        except Exception as e:
            logger.error(f"搜索机构行业关系失败: {str(e)}")
            return [] 