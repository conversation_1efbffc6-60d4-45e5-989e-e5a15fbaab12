"""
区域层级关系服务

专门负责区域层级关系的创建和管理
"""

from typing import List, Dict, Any, Optional
import pandas as pd

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AreaHierarchyRelationshipService(BaseRelationshipService):
    """区域层级关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None) -> bool:
        """
        从Hive数据源创建区域层级关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建区域层级关系")
            
            # 1. 从Hive查询有父子关系的数据
            hive_data = self._query_hierarchy_data_from_hive(limit, where_conditions)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的区域层级关系数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的区域层级关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个区域层级关系")
                return True
            else:
                logger.error("批量创建区域层级关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建区域层级关系失败: {str(e)}")
            return False
    
    def _query_hierarchy_data_from_hive(self, 
                                      limit: Optional[int] = None,
                                      where_conditions: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从Hive查询区域层级关系数据
        
        Args:
            limit: 记录数限制
            where_conditions: 额外的WHERE条件
            
        Returns:
            Optional[pd.DataFrame]: 查询结果
        """
        try:
            # 构建查询条件
            conditions = [
                "city_code IS NOT NULL",
                "city_parent_code IS NOT NULL",
                "city_name IS NOT NULL"
            ]
            
            if where_conditions:
                conditions.append(where_conditions)
            
            where_clause = " AND ".join(conditions)
            
            # 构建LIMIT子句
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            SELECT DISTINCT
                city_code as child_code,
                city_parent_code as parent_code,
                city_name as child_name,
                city_level
            FROM dimdb.dim_area_code
            WHERE {where_clause}
            ORDER BY city_code ASC
            {limit_clause}
            """
            
            return self.query_hive_data(query)
            
        except Exception as e:
            logger.error(f"查询Hive区域层级数据失败: {str(e)}")
            return None
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建区域关系数据
        
        Args:
            hive_data: Hive层级数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        
        for _, row in hive_data.iterrows():
            parent_code = str(row['parent_code'])
            child_code = str(row['child_code'])
            
            # 跳过自循环关系（如"全国"指向自己的情况）
            if parent_code == child_code:
                logger.debug(f"跳过自循环关系: {parent_code} -> {child_code}")
                continue
            
            relationship_data.append({
                'parent_code': parent_code,
                'child_code': child_code,
                'remarks': f"区域层级关系: {parent_code} -> {child_code}",
                'hierarchy_level': 1,
                'management_type': '行政管辖',
                'data_source_node': 'Area',
                'relationship_strength': 9
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的区域关系")
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code匹配节点
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (parent:Area {code: rel.parent_code})
                MATCH (child:Area {code: rel.child_code})
                MERGE (parent)-[r:PARENT_OF]->(child)
                SET r.remarks = rel.remarks,
                    r.parent_area_name = rel.parent_area_name,
                    r.child_area_name = rel.child_area_name,
                    r.parent_level = rel.parent_level,
                    r.child_level = rel.child_level,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.relationship_status = 'active',
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个区域层级关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def create_relationships_from_pairs(self, relationship_pairs: List[Dict[str, str]]) -> bool:
        """
        从关系对列表创建区域层级关系（保留此方法供外部调用）
        
        Args:
            relationship_pairs: 关系对列表，每个元素包含 parent_code 和 child_code
            
        Returns:
            bool: 是否成功
        """
        try:
            if not relationship_pairs:
                logger.info("没有区域关系需要创建")
                return True
            
            logger.info(f"开始从 {len(relationship_pairs)} 个关系对创建区域层级关系")
            
            # 构建关系数据
            relationship_data = []
            for pair in relationship_pairs:
                parent_code = str(pair['parent_code'])
                child_code = str(pair['child_code'])
                
                # 跳过自循环关系
                if parent_code == child_code:
                    logger.debug(f"跳过自循环关系: {parent_code} -> {child_code}")
                    continue
                
                relationship_data.append({
                    'parent_code': parent_code,
                    'child_code': child_code,
                    'remarks': f"区域层级关系: {parent_code} -> {child_code}",
                    'hierarchy_level': 1,
                    'management_type': '行政管辖',
                    'data_source_node': 'Area',
                    'relationship_strength': 9
                })
            
            # 直接创建关系
            if relationship_data:
                success = self._create_relationships_directly(relationship_data)
                
                if success:
                    logger.info(f"成功创建 {len(relationship_data)} 个区域层级关系")
                    return True
                else:
                    logger.error("批量创建区域关系失败")
                    return False
            else:
                logger.warning("没有有效的区域关系数据需要创建")
                return True
            
        except Exception as e:
            logger.error(f"从关系对创建区域层级关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有区域层级关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有区域层级关系")
            return True
            
        except Exception as e:
            logger.error(f"删除区域层级关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取区域层级关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按层级统计
            level_query = """
            MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
            WHERE r.parent_level IS NOT NULL AND r.child_level IS NOT NULL
            RETURN r.parent_level as parent_level, r.child_level as child_level, count(r) as count
            ORDER BY parent_level, child_level
            """
            level_result = self._safe_execute_query(level_query)
            level_stats = {f"level_{record['parent_level']}_to_{record['child_level']}": record['count'] for record in level_result}
            
            # 按关系类型统计
            relationship_type_query = """
            MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
            WHERE r.relationship_type IS NOT NULL
            RETURN r.relationship_type as type, count(r) as count
            ORDER BY type
            """
            relationship_type_result = self._safe_execute_query(relationship_type_query)
            relationship_type_stats = {record['type']: record['count'] for record in relationship_type_result}
            
            # 数据源统计
            source_query = """
            MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="区域层级关系",
                additional_stats={
                    "by_level": level_stats,
                    "by_relationship_type": relationship_type_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取区域层级关系统计失败: {str(e)}")
            return {}
    
    def search_hierarchy_relationships(self, 
                                     parent_code: Optional[str] = None,
                                     child_code: Optional[str] = None,
                                     parent_level: Optional[int] = None,
                                     child_level: Optional[int] = None,
                                     include_node_details: bool = True,
                                     limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索区域层级关系
        
        Args:
            parent_code: 父区域编码过滤条件
            child_code: 子区域编码过滤条件
            parent_level: 父层级过滤条件
            child_level: 子层级过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if parent_code:
                conditions.append("parent.code = $parent_code")
                parameters['parent_code'] = parent_code
            
            if child_code:
                conditions.append("child.code = $child_code")
                parameters['child_code'] = child_code
            
            if parent_level is not None:
                conditions.append("r.parent_level = $parent_level")
                parameters['parent_level'] = parent_level
            
            if child_level is not None:
                conditions.append("r.child_level = $child_level")
                parameters['child_level'] = child_level
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
                WHERE {where_clause}
                RETURN r,
                       {{code: parent.code, name: parent.name, level: parent.level}} as parent_info,
                       {{code: child.code, name: child.name, level: child.level}} as child_info
                ORDER BY parent.code, child.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (parent:Area)-[r:PARENT_OF]->(child:Area)
                WHERE {where_clause}
                RETURN r, parent.code as parent_code, child.code as child_code
                ORDER BY parent.code, child.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                rel_data = dict(record['r'])
                
                if include_node_details:
                    rel_data['parent'] = record['parent_info']
                    rel_data['child'] = record['child_info']
                else:
                    rel_data['parent_code'] = record['parent_code']
                    rel_data['child_code'] = record['child_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个区域层级关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索区域层级关系失败: {str(e)}")
            return [] 