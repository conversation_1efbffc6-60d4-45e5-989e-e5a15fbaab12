"""
保险销售渠道地理关系服务

专门负责保险销售渠道与区域节点之间地理关系的创建和管理
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceSalesChannelAreaRelationshipService(BaseRelationshipService):
    """保险销售渠道地理关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保险销售渠道与区域的地理关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保险销售渠道地理关系")
            
            # 1. 从Hive查询保险销售渠道地理数据
            hive_data = self._query_geographic_data_from_hive(limit, where_conditions)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保险销售渠道地理数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保险销售渠道地理关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险销售渠道地理关系")
                return True
            else:
                logger.error("批量创建保险销售渠道地理关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保险销售渠道地理关系失败: {str(e)}")
            return False
    
    def _query_geographic_data_from_hive(self, 
                                       limit: Optional[int] = None,
                                       where_conditions: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从Hive查询保险销售渠道地理数据
        
        Args:
            limit: 记录数限制
            where_conditions: 额外的WHERE条件
            
        Returns:
            Optional[pd.DataFrame]: 查询结果
        """
        try:
            # 构建查询条件
            conditions = [
                "id IS NOT NULL",
                "org_name IS NOT NULL",
                "(province_code IS NOT NULL OR city_code IS NOT NULL)"
            ]
            
            if where_conditions:
                conditions.append(where_conditions)
            
            where_clause = " AND ".join(conditions)
            
            # 构建LIMIT子句
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            SELECT
                CAST(id AS STRING) as id,
                org_name,
                org_type,
                level,
                CAST(parent_id AS STRING) as parent_id,
                CAST(head AS STRING) as head,
                CAST(bd_user_id AS STRING) as bd_user_id,
                CAST(bds_user_id AS STRING) as bds_user_id,
                status,
                province_code,
                province_name,
                city_code,
                city_name
            FROM dwddb.dwd_c_channel_market
            WHERE {where_clause}
            ORDER BY id ASC
            {limit_clause}
            """
            
            return self.query_hive_data(query)
            
        except Exception as e:
            logger.error(f"查询Hive保险销售渠道地理数据失败: {str(e)}")
            return None
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保险销售渠道与区域的地理关系数据
        
        Args:
            hive_data: Hive地理数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        
        for _, row in hive_data.iterrows():
            channel_code = str(row.get('id', '')).strip()
            city_code = str(row.get('city_code', '')).strip()
            province_code = str(row.get('province_code', '')).strip()
            city_name = str(row.get('city_name', '')).strip()
            province_name = str(row.get('province_name', '')).strip()
            
            # 跳过无效数据
            if not channel_code or channel_code == 'nan':
                continue
            
            # 确定目标区域编码（优先使用city_code）
            target_area_code = None
            target_area_name = None
            
            if city_code and city_code != 'nan':
                target_area_code = city_code
                target_area_name = city_name
            elif province_code and province_code != 'nan':
                target_area_code = province_code
                target_area_name = province_name
            
            if not target_area_code:
                logger.debug(f"跳过关系，渠道 {channel_code} 没有有效的区域编码")
                continue
            
            relationship_data.append({
                'channel_code': channel_code,
                'area_code': target_area_code,
                'remarks': f"保险销售渠道地理关系: {channel_code} 位于 {target_area_name}({target_area_code})",
                'data_source_node': 'InsuranceSalesChannel',
                'relationship_strength': 7,
                'geographic_type': '行政区域'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保险销售渠道地理关系")
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code匹配节点
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (channel:InsuranceSalesChannel {code: rel.channel_code})
                MATCH (area:Area {code: rel.area_code})
                MERGE (channel)-[r:LINKS_TO_REGION]->(area)
                SET r.remarks = rel.remarks,
                    r.channel_name = rel.channel_name,
                    r.area_name = rel.area_name,
                    r.area_type = rel.area_type,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.relationship_status = 'active',
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险销售渠道地理关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险销售渠道区域关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险销售渠道区域关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险销售渠道区域关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险销售渠道区域关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按区域类型统计
            area_type_query = """
            MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
            WHERE r.area_type IS NOT NULL
            RETURN r.area_type as area_type, count(r) as count
            ORDER BY count DESC
            """
            area_type_result = self._safe_execute_query(area_type_query)
            area_type_stats = {record['area_type']: record['count'] for record in area_type_result}
            
            # 按关系类型统计
            relationship_type_query = """
            MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
            WHERE r.relationship_type IS NOT NULL
            RETURN r.relationship_type as type, count(r) as count
            ORDER BY type
            """
            relationship_type_result = self._safe_execute_query(relationship_type_query)
            relationship_type_stats = {record['type']: record['count'] for record in relationship_type_result}
            
            # 数据源统计
            source_query = """
            MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险销售渠道区域关系",
                additional_stats={
                    "by_area_type": area_type_stats,
                    "by_relationship_type": relationship_type_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险销售渠道区域关系统计失败: {str(e)}")
            return {}
    
    def search_area_relationships(self, 
                                channel_code: Optional[str] = None,
                                area_code: Optional[str] = None,
                                area_type: Optional[str] = None,
                                include_node_details: bool = True,
                                limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险销售渠道区域关系
        
        Args:
            channel_code: 渠道编码过滤条件
            area_code: 区域编码过滤条件
            area_type: 区域类型过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if channel_code:
                conditions.append("channel.code = $channel_code")
                parameters['channel_code'] = channel_code
            
            if area_code:
                conditions.append("area.code = $area_code")
                parameters['area_code'] = area_code
            
            if area_type:
                conditions.append("r.area_type = $area_type")
                parameters['area_type'] = area_type
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
                WHERE {where_clause}
                RETURN r,
                       {{code: channel.code, name: channel.name, org_type: channel.org_type, level: channel.level}} as channel_info,
                       {{code: area.code, name: area.name, area_type: area.area_type, level: area.level}} as area_info
                ORDER BY channel.code, area.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (channel:InsuranceSalesChannel)-[r:LINKS_TO_REGION]->(area:Area)
                WHERE {where_clause}
                RETURN r, channel.code as channel_code, area.code as area_code
                ORDER BY channel.code, area.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                rel_data = dict(record['r'])
                
                if include_node_details:
                    rel_data['channel'] = record['channel_info']
                    rel_data['area'] = record['area_info']
                else:
                    rel_data['channel_code'] = record['channel_code']
                    rel_data['area_code'] = record['area_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险销售渠道区域关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险销售渠道区域关系失败: {str(e)}")
            return [] 