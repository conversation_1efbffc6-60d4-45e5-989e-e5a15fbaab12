"""
保险理赔公司投保关系服务

专门负责保险理赔 InsuranceClaims 节点与投保公司 PolicyCompany 节点之间投保关系的创建和管理
通过 insure_code (投保公司代码) 进行精确匹配
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsCompanyInsureRelationshipService(BaseRelationshipService):
    """保险理赔公司投保关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None,
                                     custom_day: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保险理赔与投保公司的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期（格式：YYYY-MM-DD），默认使用昨天
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保险理赔保单投保关系")
            
            # 1. 从Hive查询保险理赔投保数据
            hive_data = self._query_claims_insure_data_from_hive(limit, where_conditions, custom_day)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保险理赔投保关系数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保险理赔投保关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保险理赔投保关系")
                return True
            else:
                logger.error("批量创建保险理赔投保关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保险理赔投保关系失败: {str(e)}")
            return False
    
    def _query_claims_insure_data_from_hive(self, 
                                          limit: Optional[int] = None,
                                          where_conditions: Optional[str] = None,
                                          custom_day: Optional[str] = None) -> pd.DataFrame:
        """
        从Hive查询保险理赔投保数据
        
        Args:
            limit: 限制查询数量
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期，默认使用昨天
            
        Returns:
            pd.DataFrame: 保险理赔投保数据
        """
        try:
            # 确定日期
            if custom_day:
                day_value = custom_day
            else:
                day_value = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            limit_clause = f"LIMIT {limit}" if limit else ""
            where_clause = f"AND {where_conditions}" if where_conditions else ""
            
            query = f"""
            SELECT fcd.case_no,                                            -- 报案号
                   fcd.policy_code,                                        -- 保单号
                   pct.insure_code,                                        -- 投保公司代码
                   pct.insure_name                                         -- 投保公司名称
            FROM dwtdb.dwt_fnol_claim_detail fcd
                     INNER JOIN dwadb.dwa_insured_person_common_data_test pct
                                ON pct.policy_code = fcd.policy_code AND pct.insured_id = fcd.insured_id AND pct.day = '{day_value}'
                     INNER JOIN dimdb.dim_tyc_company_base tyc ON fcd.insurance_name = tyc.system_matched_company_name
                     LEFT JOIN dwddb.dwd_b_policy_central pc ON fcd.policy_code = pc.policy_code
            WHERE pc.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
              AND fcd.case_no IS NOT NULL
              AND fcd.case_no != ''
              AND fcd.policy_code IS NOT NULL
              AND fcd.policy_code != ''
              AND pct.insure_code IS NOT NULL
              AND pct.insure_code != ''
              {where_clause}
            ORDER BY fcd.case_no DESC
            {limit_clause}
            """
            
            logger.info(f"查询保险理赔投保数据，使用日期: {day_value}")
            data = self.query_hive_data(query)
            logger.info(f"从Hive查询到 {len(data)} 条保险理赔投保记录")
            return data
            
        except Exception as e:
            logger.error(f"从Hive查询保险理赔投保数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保险理赔与保单的投保关系数据
        
        Args:
            hive_data: Hive保险理赔投保数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        skipped_count = 0
        
        for index, row in hive_data.iterrows():
            case_no = str(row.get('case_no', '')).strip()
            policy_code = str(row.get('policy_code', '')).strip()
            insure_code = str(row.get('insure_code', '')).strip()
            insure_name = str(row.get('insure_name', '')).strip()
            
            # 跳过无效数据
            if not case_no or case_no == 'nan':
                skipped_count += 1
                continue
            
            if not self._is_valid_value(policy_code) or not self._is_valid_value(insure_code):
                skipped_count += 1
                continue
            
            relationship_data.append({
                'case_no': case_no,
                'policy_code': policy_code,
                'insure_code': insure_code,
                'insure_name': insure_name,
                'remarks': f"保险理赔投保关系: {case_no} 关联保单 {policy_code} 由 {insure_name}({insure_code}) 投保",
                'data_source_node': 'InsuranceClaims',
                'relationship_strength': 8,
                'relationship_type': '投保关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保险理赔投保关系，跳过了 {skipped_count} 个无效记录")
        
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过保单号进行精确匹配
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询，通过投保公司代码匹配PolicyCompany节点
                query = """
                UNWIND $relationships as rel
                MATCH (ic:InsuranceClaims)
                WHERE ic.case_no = rel.case_no
                MATCH (pc:PolicyCompany)
                WHERE pc.unified_social_credit_code = rel.insure_code
                   OR pc.code = rel.insure_code
                   OR pc.business_id = rel.insure_code
                MERGE (ic)-[r:INSURED_BY]->(pc)
                SET r.remarks = rel.remarks,
                    r.insure_code = rel.insure_code,
                    r.insure_name = rel.insure_name,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险理赔投保关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def _is_valid_value(self, value: str) -> bool:
        """
        判断值是否有效
        
        Args:
            value: 待检查的值
            
        Returns:
            bool: 是否有效
        """
        if not value:
            return False
        
        value_str = str(value).strip().lower()
        invalid_values = ['', 'nan', 'null', 'none', 'n/a', 'na']
        return value_str not in invalid_values
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险理赔投保关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险理赔投保关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险理赔投保关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔投保关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按投保公司统计（Top 10）
            insure_query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            WHERE r.insure_name IS NOT NULL
            RETURN r.insure_name as insure_name, count(r) as claims_count
            ORDER BY claims_count DESC
            LIMIT 10
            """
            insure_result = self._safe_execute_query(insure_query)
            insure_stats = {record['insure_name']: record['claims_count'] for record in insure_result}
            
            # 按投保公司代码统计（Top 10）
            code_query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            WHERE r.insure_code IS NOT NULL
            RETURN r.insure_code as insure_code, count(r) as claims_count
            ORDER BY claims_count DESC
            LIMIT 10
            """
            code_result = self._safe_execute_query(code_query)
            code_stats = {record['insure_code']: record['claims_count'] for record in code_result}
            
            # 数据源统计
            source_query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            # 关系强度统计
            strength_query = """
            MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
            WHERE r.relationship_strength IS NOT NULL
            RETURN r.relationship_strength as strength, count(r) as count
            ORDER BY strength DESC
            """
            strength_result = self._safe_execute_query(strength_query)
            strength_stats = {record['strength']: record['count'] for record in strength_result}
            
            # 使用基类的标准格式化方法
            additional_stats = {
                "by_insure_name": insure_stats,
                "by_insure_code": code_stats,
                "by_data_source_node": source_stats,
                "by_relationship_strength": strength_stats
            }
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险理赔投保关系",
                additional_stats=additional_stats
            )
            
        except Exception as e:
            logger.error(f"获取保险理赔投保关系统计失败: {str(e)}")
            return self._format_standard_statistics(
                total_count=0,
                relationship_type="保险理赔投保关系",
                additional_stats={
                    "by_insure_name": {},
                    "by_insure_code": {},
                    "by_data_source_node": {},
                    "by_relationship_strength": {}
                }
            )
    
    def search_claims_policy_insure_relationships(self, 
                                                case_no: Optional[str] = None,
                                                policy_code: Optional[str] = None,
                                                insure_name: Optional[str] = None,
                                                insure_code: Optional[str] = None,
                                                include_node_details: bool = True,
                                                limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔投保关系
        
        Args:
            case_no: 案件号过滤条件
            policy_code: 保单号过滤条件
            insure_name: 投保公司名称过滤条件
            insure_code: 投保公司代码过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if case_no:
                conditions.append("ic.case_no CONTAINS $case_no")
                parameters['case_no'] = case_no
            
            if policy_code:
                conditions.append("p.policy_code CONTAINS $policy_code")
                parameters['policy_code'] = policy_code
            
            if insure_name:
                conditions.append("r.insure_name CONTAINS $insure_name")
                parameters['insure_name'] = insure_name
            
            if insure_code:
                conditions.append("r.insure_code = $insure_code")
                parameters['insure_code'] = insure_code
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 构建查询语句
            if include_node_details:
                query = f"""
                MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
                {where_clause}
                RETURN r.insure_code as insure_code,
                       r.insure_name as insure_name,
                       r.relationship_type as relationship_type,
                       r.relationship_strength as relationship_strength,
                       r.data_source_node as data_source_node,
                       pc.code as company_code,
                       pc.name as company_name,
                       ic.case_no as case_no, 
                       ic.name as claims_name
                ORDER BY ic.case_no, pc.name
                LIMIT $limit
                """
            else:
                query = f"""
                MATCH (ic:InsuranceClaims)-[r:INSURED_BY]->(pc:PolicyCompany)
                {where_clause}
                RETURN r.insure_code as insure_code,
                       r.insure_name as insure_name,
                       r.relationship_type as relationship_type,
                       r.relationship_strength as relationship_strength,
                       r.data_source_node as data_source_node,
                       pc.code as company_code,
                       pc.name as company_name
                ORDER BY ic.case_no, pc.name
                LIMIT $limit
                """
            
            parameters['limit'] = limit
            
            # 使用基类的安全查询方法
            results = self._safe_execute_query(query, parameters)
            
            # 使用基类的安全记录处理方法
            relationships = []
            for i, record in enumerate(results):
                processed_record = self._handle_search_record_safely(record, i)
                if processed_record:
                    # 只保留需要的字段
                    rel_data = {
                        'insure_code': processed_record.get('insure_code'),
                        'insure_name': processed_record.get('insure_name'),
                        'relationship_type': processed_record.get('relationship_type'),
                        'relationship_strength': processed_record.get('relationship_strength'),
                        'data_source_node': processed_record.get('data_source_node'),
                        'company_code': processed_record.get('company_code'),
                        'company_name': processed_record.get('company_name')
                    }
                    
                    # 如果需要包含节点详情
                    if include_node_details:
                        rel_data['case_no'] = processed_record.get('case_no')
                        rel_data['claims_name'] = processed_record.get('claims_name')
                    
                    relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险理赔投保关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险理赔投保关系失败: {str(e)}")
            return [] 