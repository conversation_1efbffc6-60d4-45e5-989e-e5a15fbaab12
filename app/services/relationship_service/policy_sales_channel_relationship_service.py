"""
保单销售渠道关系服务

专门负责保单 Policy 节点与保险销售渠道 InsuranceSalesChannel 节点之间关系的创建和管理
通过 channel_id 字段建立 DISTRIBUTED_BY 关系
"""

from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicySalesChannelRelationshipService(BaseRelationshipService):
    """保单销售渠道关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                                     limit: Optional[int] = None,
                                     where_conditions: Optional[str] = None,
                                     custom_day: Optional[str] = None) -> bool:
        """
        从Hive数据源创建保单与销售渠道的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期（格式：YYYY-MM-DD），默认使用昨天
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Hive数据源创建保单与销售渠道关系")
            
            # 1. 从Hive查询保单销售渠道数据
            hive_data = self._query_policy_sales_channel_data_from_hive(limit, where_conditions, custom_day)
            if hive_data is None or hive_data.empty:
                logger.warning("未找到有效的保单销售渠道数据")
                return True
            
            # 2. 构建关系数据
            relationship_data = self._build_relationship_data(hive_data)
            
            if not relationship_data:
                logger.warning("没有有效的保单销售渠道关系数据需要创建")
                return True
            
            # 3. 直接批量创建关系
            success = self._create_relationships_directly(relationship_data)
            
            if success:
                logger.info(f"成功创建 {len(relationship_data)} 个保单销售渠道关系")
                return True
            else:
                logger.error("批量创建保单销售渠道关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Hive创建保单销售渠道关系失败: {str(e)}")
            return False
    
    def _query_policy_sales_channel_data_from_hive(self, 
                                                 limit: Optional[int] = None,
                                                 where_conditions: Optional[str] = None,
                                                 custom_day: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从Hive查询保单销售渠道数据
        
        Args:
            limit: 记录数限制
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期，默认使用昨天
            
        Returns:
            Optional[pd.DataFrame]: 查询结果
        """
        try:
            # 确定日期
            if custom_day:
                day_value = custom_day
            else:
                day_value = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 构建查询条件
            conditions = [
                "policy_code IS NOT NULL",
                "channel_id IS NOT NULL",
                "channel_name IS NOT NULL",
                f"`day` = '{day_value}'"
            ]
            
            # 添加产品代码过滤
            conditions.append("product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')")
            
            if where_conditions:
                conditions.append(where_conditions)
            
            where_clause = " AND ".join(conditions)
            
            # 构建LIMIT子句
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            SELECT
                policy_code,
                policy_start_date,
                policy_end_date,
                policy_status,
                insurance_code,
                insurance_name,
                insure_code,
                insure_name,
                insured_code,
                insured_name,
                product_code,
                product_name,
                CAST(channel_id AS STRING) as channel_id,
                channel_name,
                sale_code,
                sale_name,
                sale_mobile,
                assure_time,
                sku_ratio,
                policy_coverage,
                industry
            FROM dwadb.dwa_policy_common_data_test
            WHERE {where_clause}
            ORDER BY policy_code ASC
            {limit_clause}
            """
            
            logger.info(f"查询保单销售渠道数据，使用日期: {day_value}")
            return self.query_hive_data(query)
            
        except Exception as e:
            logger.error(f"查询Hive保单销售渠道数据失败: {str(e)}")
            return None
    
    def _build_relationship_data(self, hive_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        构建保单与销售渠道的关系数据
        
        Args:
            hive_data: Hive保单销售渠道数据
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        relationship_data = []
        
        for _, row in hive_data.iterrows():
            policy_code = str(row.get('policy_code', '')).strip()
            channel_id = str(row.get('channel_id', '')).strip()
            channel_name = str(row.get('channel_name', '')).strip()
            product_code = str(row.get('product_code', '')).strip()
            product_name = str(row.get('product_name', '')).strip()
            
            # 跳过无效数据
            if not policy_code or policy_code == 'nan' or not channel_id or channel_id == 'nan':
                continue
            
            relationship_data.append({
                'policy_code': policy_code,
                'channel_id': channel_id,
                'remarks': f"保单销售渠道关系: 保单 {policy_code} 通过销售渠道 {channel_name}({channel_id}) 销售",
                'channel_name': channel_name,
                'channel_id_rel': channel_id,
                'product_code': product_code,
                'product_name': product_name,
                'data_source_node': 'Policy',
                'relationship_strength': 7,
                'relationship_type': '销售渠道关系'
            })
        
        logger.info(f"构建了 {len(relationship_data)} 个有效的保单销售渠道关系")
        return relationship_data
    
    def _create_relationships_directly(self, relationship_data: List[Dict[str, Any]]) -> bool:
        """
        直接创建关系，通过code匹配节点
        
        Args:
            relationship_data: 关系数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(relationship_data), batch_size):
                batch_data = relationship_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (p:Policy {code: rel.policy_code})
                MATCH (c:InsuranceSalesChannel {code: rel.channel_id})
                MERGE (p)-[r:DISTRIBUTED_BY]->(c)
                SET r.remarks = rel.remarks,
                    r.channel_name = rel.channel_name,
                    r.channel_id = rel.channel_id_rel,
                    r.product_code = rel.product_code,
                    r.product_name = rel.product_name,
                    r.data_source_node = rel.data_source_node,
                    r.relationship_strength = rel.relationship_strength,
                    r.relationship_type = rel.relationship_type,
                    r.relationship_status = 'active',
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保单销售渠道关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"直接创建关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> bool:
        """
        删除所有保单销售渠道关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保单销售渠道关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保单销售渠道关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单销售渠道关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按销售渠道统计
            channel_query = """
            MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
            WHERE r.channel_name IS NOT NULL
            RETURN r.channel_name as channel_name, count(r) as policy_count
            ORDER BY policy_count DESC
            LIMIT 10
            """
            channel_result = self._safe_execute_query(channel_query)
            channel_stats = {record['channel_name']: record['policy_count'] for record in channel_result}
            
            # 按产品代码统计
            product_query = """
            MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
            WHERE r.product_code IS NOT NULL
            RETURN r.product_code as product_code, count(r) as count
            ORDER BY count DESC
            """
            product_result = self._safe_execute_query(product_query)
            product_stats = {record['product_code']: record['count'] for record in product_result}
            
            # 数据源统计
            source_query = """
            MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保单销售渠道关系",
                additional_stats={
                    "by_sales_channel": channel_stats,
                    "by_product_code": product_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保单销售渠道关系统计失败: {str(e)}")
            return {}
    
    def search_sales_channel_relationships(self, 
                                         policy_code: Optional[str] = None,
                                         channel_id: Optional[str] = None,
                                         channel_name: Optional[str] = None,
                                         product_code: Optional[str] = None,
                                         include_node_details: bool = True,
                                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单销售渠道关系
        
        Args:
            policy_code: 保单编码过滤条件
            channel_id: 销售渠道ID过滤条件
            channel_name: 销售渠道名称过滤条件
            product_code: 产品代码过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if policy_code:
                conditions.append("policy.code = $policy_code")
                parameters['policy_code'] = policy_code
            
            if channel_id:
                conditions.append("r.channel_id = $channel_id")
                parameters['channel_id'] = channel_id
            
            if channel_name:
                conditions.append("r.channel_name CONTAINS $channel_name")
                parameters['channel_name'] = channel_name
            
            if product_code:
                conditions.append("r.product_code = $product_code")
                parameters['product_code'] = product_code
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r,
                       {{code: policy.code, policy_start_date: policy.policy_start_date, policy_end_date: policy.policy_end_date, policy_status: policy.policy_status}} as policy_info,
                       {{code: channel.code, name: channel.name, short_name: channel.short_name}} as channel_info
                ORDER BY policy.code, channel.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (policy:Policy)-[r:DISTRIBUTED_BY]->(channel:InsuranceSalesChannel)
                WHERE {where_clause}
                RETURN r, policy.code as policy_code, channel.code as channel_id
                ORDER BY policy.code, channel.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'DISTRIBUTED_BY',
                    'relationship_status': 'active'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['policy'] = record['policy_info']
                    rel_data['sales_channel'] = record['channel_info']
                else:
                    rel_data['policy_code'] = record['policy_code']
                    rel_data['channel_id'] = record['channel_id']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保单销售渠道关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保单销售渠道关系失败: {str(e)}")
            return [] 