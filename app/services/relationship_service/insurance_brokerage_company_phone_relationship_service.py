"""
保险经纪公司联系电话关系服务

专门负责 InsuranceBrokerageCompany 节点与 ContactNumber 节点之间关系的创建和管理
从Neo4j查询保险经纪公司的phone属性，创建或关联对应的联系电话节点
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceBrokerageCompanyPhoneRelationshipService(BaseRelationshipService):
    """保险经纪公司联系电话关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, 
                           limit: Optional[int] = None,
                           company_code_filter: Optional[str] = None) -> bool:
        """
        从Neo4j查询保险经纪公司数据，创建或关联ContactNumber节点，并建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            company_code_filter: 保险经纪公司编码过滤条件
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保险经纪公司与联系电话关系")
            
            # 1. 从Neo4j查询保险经纪公司数据
            company_data = self._query_company_phone_data_from_neo4j(limit, company_code_filter)
            if not company_data:
                logger.warning("未找到有效的保险经纪公司电话数据")
                return True
            
            # 2. 创建缺失的ContactNumber节点
            created_contacts = self._create_missing_contact_numbers(company_data)
            logger.info(f"创建了 {created_contacts} 个ContactNumber节点")
            
            # 3. 创建关系
            success = self._create_company_phone_relationships(company_data)
            
            if success:
                logger.info(f"成功处理 {len(company_data)} 个保险经纪公司联系电话关系")
                return True
            else:
                logger.error("创建保险经纪公司联系电话关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保险经纪公司联系电话关系失败: {str(e)}")
            return False
    
    def _query_company_phone_data_from_neo4j(self, 
                                            limit: Optional[int] = None,
                                            company_code_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险经纪公司的电话数据
        
        Args:
            limit: 记录数限制
            company_code_filter: 保险经纪公司编码过滤条件
            
        Returns:
            List[Dict[str, Any]]: 保险经纪公司电话数据列表
        """
        try:
            # 构建查询条件
            conditions = ["company.phone IS NOT NULL", "company.phone <> ''"]
            parameters = {}
            
            if company_code_filter:
                conditions.append("company.code CONTAINS $company_code_filter")
                parameters['company_code_filter'] = company_code_filter
            
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (company:InsuranceBrokerageCompany)
            WHERE {where_clause}
            RETURN company.code as company_code,
                   company.name as company_name,
                   company.phone as phone,
                   company.business_id as company_business_id
            ORDER BY company.code
            {limit_clause}
            """
            
            logger.info(f"查询保险经纪公司电话数据，条件: {where_clause}")
            results = self._safe_execute_query(query, parameters)
            
            if not results:
                return []
            
            # 过滤掉无效的电话号码
            valid_data = []
            for record in results:
                phone = str(record.get('phone', '')).strip()
                company_code = str(record.get('company_code', '')).strip()
                
                if phone and phone != 'nan' and phone != 'None' and company_code:
                    valid_data.append({
                        'company_code': company_code,
                        'company_name': record.get('company_name', ''),
                        'phone': phone,
                        'company_business_id': record.get('company_business_id', company_code)
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保险经纪公司电话记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险经纪公司电话数据失败: {str(e)}")
            return []
    
    def _create_missing_contact_numbers(self, company_data: List[Dict[str, Any]]) -> int:
        """
        创建缺失的ContactNumber节点
        
        Args:
            company_data: 保险经纪公司数据列表
            
        Returns:
            int: 创建的节点数量
        """
        try:
            # 提取所有电话号码
            phones = [data['phone'] for data in company_data]
            
            if not phones:
                return 0
            
            # 使用MERGE确保不会创建重复的ContactNumber节点
            # MERGE会先查找，如果不存在才创建
            create_query = """
            UNWIND $phones as phone
            MERGE (contact:ContactNumber {code: phone})
            ON CREATE SET 
                contact.business_id = phone,
                contact.created_at = datetime(),
                contact.updated_at = datetime()
            ON MATCH SET 
                contact.updated_at = datetime()
            RETURN count(contact) as processed_count
            """
            
            result = self._safe_execute_query(create_query, {'phones': phones})
            processed_count = result[0]['processed_count'] if result else 0
            
            logger.info(f"处理了 {processed_count} 个ContactNumber节点（包含新创建和已存在的）")
            return processed_count
            
        except Exception as e:
            logger.error(f"创建ContactNumber节点失败: {str(e)}")
            return 0
    
    def _create_company_phone_relationships(self, company_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险经纪公司与联系电话的关系
        
        Args:
            company_data: 保险经纪公司数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(company_data), batch_size):
                batch_data = company_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (company:InsuranceBrokerageCompany {code: rel.company_code})
                MATCH (contact:ContactNumber {code: rel.phone})
                MERGE (company)-[r:HAS_PHONE]->(contact)
                SET r.relationship_type = 'HAS_PHONE',
                    r.relationship_status = 'active',
                    r.relationship_strength = 8,
                    r.data_source_node = 'InsuranceBrokerageCompany',
                    r.remarks = '保险经纪公司联系电话: ' + rel.company_name + ' - ' + rel.phone,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险经纪公司联系电话关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险经纪公司联系电话关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险经纪公司联系电话关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险经纪公司联系电话关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险经纪公司联系电话关系失败: {str(e)}")
            return False
    
    def delete_orphaned_contact_numbers(self) -> int:
        """
        删除孤立的ContactNumber节点（没有任何关系的节点）
        
        Returns:
            int: 删除的节点数量
        """
        try:
            query = """
            MATCH (contact:ContactNumber)
            WHERE NOT (contact)--()
            DELETE contact
            RETURN count(contact) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = result[0]['deleted_count'] if result else 0
            
            logger.info(f"删除了 {deleted_count} 个孤立的ContactNumber节点")
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除孤立ContactNumber节点失败: {str(e)}")
            return 0
    
    def cleanup_duplicate_contact_numbers(self) -> int:
        """
        清理重复的ContactNumber节点
        对于相同code的节点，保留最早创建的节点，删除其他重复节点
        
        Returns:
            int: 删除的重复节点数量
        """
        try:
            # 查找重复的ContactNumber节点
            find_duplicates_query = """
            MATCH (contact:ContactNumber)
            WITH contact.code as code, collect(contact) as contacts
            WHERE size(contacts) > 1
            RETURN code, contacts
            """
            
            duplicates_result = self._safe_execute_query(find_duplicates_query)
            
            if not duplicates_result:
                logger.info("没有发现重复的ContactNumber节点")
                return 0
            
            total_deleted = 0
            
            for record in duplicates_result:
                code = record['code']
                contacts = record['contacts']
                
                if len(contacts) <= 1:
                    continue
                
                logger.info(f"发现重复的ContactNumber节点，code: {code}，共 {len(contacts)} 个")
                
                # 对于每组重复节点，保留最早创建的节点，删除其他
                cleanup_query = """
                MATCH (contact:ContactNumber {code: $code})
                WITH contact 
                ORDER BY COALESCE(contact.created_at, datetime()) ASC
                WITH collect(contact) as contacts
                WHERE size(contacts) > 1
                UNWIND contacts[1..] as duplicate_contact
                
                // 将所有关系转移到第一个（最早的）节点
                OPTIONAL MATCH (duplicate_contact)-[r]-()
                WITH duplicate_contact, collect(r) as rels, contacts[0] as keep_contact
                
                // 重新创建关系到保留的节点
                UNWIND rels as rel
                WITH duplicate_contact, rel, keep_contact,
                     startNode(rel) as start_node, 
                     endNode(rel) as end_node,
                     type(rel) as rel_type,
                     properties(rel) as rel_props
                
                // 如果重复节点是起始节点
                FOREACH (ignore IN CASE WHEN start_node = duplicate_contact THEN [1] ELSE [] END |
                    MERGE (keep_contact)-[new_rel:HAS_PHONE]->(end_node)
                    SET new_rel = rel_props
                )
                
                // 如果重复节点是结束节点  
                FOREACH (ignore IN CASE WHEN end_node = duplicate_contact THEN [1] ELSE [] END |
                    MERGE (start_node)-[new_rel:HAS_PHONE]->(keep_contact)
                    SET new_rel = rel_props
                )
                
                // 删除原关系和重复节点
                DELETE rel, duplicate_contact
                RETURN count(duplicate_contact) as deleted_count
                """
                
                cleanup_result = self._safe_execute_query(cleanup_query, {'code': code})
                deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
                total_deleted += deleted_count
                
                logger.info(f"清理了 {deleted_count} 个重复的ContactNumber节点，code: {code}")
            
            logger.info(f"总共清理了 {total_deleted} 个重复的ContactNumber节点")
            return total_deleted
            
        except Exception as e:
            logger.error(f"清理重复ContactNumber节点失败: {str(e)}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险经纪公司联系电话关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有电话的保险经纪公司数量
            company_with_phone_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
            RETURN count(DISTINCT company) as company_count
            """
            company_result = self._safe_execute_query(company_with_phone_query)
            company_count = company_result[0]['company_count'] if company_result else 0
            
            # ContactNumber节点总数
            contact_total_query = """
            MATCH (contact:ContactNumber)
            RETURN count(contact) as contact_total
            """
            contact_result = self._safe_execute_query(contact_total_query)
            contact_total = contact_result[0]['contact_total'] if contact_result else 0
            
            # 孤立的ContactNumber节点数量
            orphaned_query = """
            MATCH (contact:ContactNumber)
            WHERE NOT (contact)--()
            RETURN count(contact) as orphaned_count
            """
            orphaned_result = self._safe_execute_query(orphaned_query)
            orphaned_count = orphaned_result[0]['orphaned_count'] if orphaned_result else 0
            
            # 重复的ContactNumber节点统计
            duplicate_query = """
            MATCH (contact:ContactNumber)
            WITH contact.code as code, count(contact) as contact_count
            WHERE contact_count > 1
            RETURN count(code) as duplicate_codes, sum(contact_count - 1) as duplicate_nodes
            """
            duplicate_result = self._safe_execute_query(duplicate_query)
            duplicate_codes = duplicate_result[0]['duplicate_codes'] if duplicate_result else 0
            duplicate_nodes = duplicate_result[0]['duplicate_nodes'] if duplicate_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险经纪公司联系电话关系",
                additional_stats={
                    "companies_with_phone": company_count,
                    "total_contact_numbers": contact_total,
                    "orphaned_contact_numbers": orphaned_count,
                    "duplicate_phone_codes": duplicate_codes,
                    "duplicate_contact_nodes": duplicate_nodes,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险经纪公司联系电话关系统计失败: {str(e)}")
            return {}
    
    def search_company_phone_relationships(self, 
                                         company_code: Optional[str] = None,
                                         company_name: Optional[str] = None,
                                         phone: Optional[str] = None,
                                         include_node_details: bool = True,
                                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险经纪公司联系电话关系
        
        Args:
            company_code: 保险经纪公司编码过滤条件
            company_name: 保险经纪公司名称过滤条件
            phone: 电话号码过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if company_code:
                conditions.append("company.code CONTAINS $company_code")
                parameters['company_code'] = company_code
            
            if company_name:
                conditions.append("company.name CONTAINS $company_name")
                parameters['company_name'] = company_name
            
            if phone:
                conditions.append("contact.code CONTAINS $phone")
                parameters['phone'] = phone
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
                WHERE {where_clause}
                RETURN r,
                       {{code: company.code, name: company.name, business_id: company.business_id}} as company_info,
                       {{code: contact.code, name: contact.name, business_id: contact.business_id}} as contact_info
                ORDER BY company.code, contact.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (company:InsuranceBrokerageCompany)-[r:HAS_PHONE]->(contact:ContactNumber)
                WHERE {where_clause}
                RETURN r, company.code as company_code, contact.code as phone
                ORDER BY company.code, contact.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'HAS_PHONE',
                    'relationship_status': 'active'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['company'] = record['company_info']
                    rel_data['contact'] = record['contact_info']
                else:
                    rel_data['company_code'] = record['company_code']
                    rel_data['phone'] = record['phone']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险经纪公司联系电话关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险经纪公司联系电话关系失败: {str(e)}")
            return [] 