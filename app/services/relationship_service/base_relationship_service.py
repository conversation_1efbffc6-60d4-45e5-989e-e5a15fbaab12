"""
关系服务基类

提供关系创建、管理和维护的通用功能
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd

from app.services.data_storage.neo4j_storage import Neo4jDataStorage
from app.services.data_extraction.impala_extractor import ImpalaDataExtractor
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BaseRelationshipService(ABC):
    """关系服务基类"""
    
    def __init__(self):
        self.neo4j_storage = Neo4jDataStorage()
        self.impala_extractor = ImpalaDataExtractor()
    
    @abstractmethod
    def create_relationships(self, **kwargs) -> bool:
        """
        从Hive数据源创建关系（抽象方法，子类必须实现）
        
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def delete_all_relationships(self) -> bool:
        """
        删除所有相关关系（抽象方法，子类必须实现）
        
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取关系统计信息（抽象方法，子类必须实现）
        
        Returns:
            Dict[str, Any]: 统计信息，必须包含以下标准字段：
                - total_count: 总关系数量
                - relationship_type: 关系类型描述
        """
        pass
    
    def find_node_business_id_by_code(self, node_label: str, code: str) -> Optional[str]:
        """
        根据节点标签和编码查找business_id
        
        Args:
            node_label: 节点标签
            code: 节点编码
            
        Returns:
            Optional[str]: business_id，如果未找到返回None
        """
        try:
            query = f"""
            MATCH (n:{node_label})
            WHERE n.code = $code
            RETURN n.business_id as business_id
            LIMIT 1
            """
            
            result = self.neo4j_storage.execute_query(query, {'code': code})
            
            if result:
                return result[0]['business_id']
            
            return None
            
        except Exception as e:
            logger.error(f"查找节点业务ID失败 (label={node_label}, code={code}): {str(e)}")
            return None
    
    def check_nodes_exist(self, node_label: str, codes: List[str]) -> Dict[str, str]:
        """
        批量检查节点是否存在，返回存在的节点的code->business_id映射
        
        Args:
            node_label: 节点标签
            codes: 节点编码列表
            
        Returns:
            Dict[str, str]: 存在的节点的code->business_id映射
        """
        try:
            if not codes:
                return {}
            
            query = f"""
            MATCH (n:{node_label})
            WHERE n.code IN $codes
            RETURN n.code as code, n.business_id as business_id
            """
            
            results = self.neo4j_storage.execute_query(query, {'codes': codes})
            
            existing_nodes = {}
            for record in results:
                existing_nodes[record['code']] = record['business_id']
            
            logger.info(f"在 {len(codes)} 个节点中找到 {len(existing_nodes)} 个已存在的节点")
            return existing_nodes
            
        except Exception as e:
            logger.error(f"批量检查节点存在性失败: {str(e)}")
            return {}
    
    def execute_batch_relationships_unwind(self, 
                                         batch_data: List[Dict[str, Any]], 
                                         relationship_type: str,
                                         source_label: str,
                                         target_label: str,
                                         additional_properties: Dict[str, Any] = None) -> bool:
        """
        使用UNWIND语法批量创建关系的通用方法
        
        Args:
            batch_data: 批量数据，每个元素需包含start_business_id和end_business_id
            relationship_type: 关系类型
            source_label: 源节点标签
            target_label: 目标节点标签
            additional_properties: 额外的关系属性
            
        Returns:
            bool: 是否成功
        """
        try:
            # 构建额外属性的SET子句
            additional_props = additional_properties or {}
            
            # 构建ON CREATE SET子句
            create_props = [
                "r.relationship_type = rel_data.relationship_type",
                "r.relationship_status = 'active'",
                "r.created_at = datetime()",
                "r.updated_at = datetime()"
            ]
            
            # 构建ON MATCH SET子句
            match_props = [
                "r.updated_at = datetime()"
            ]
            
            # 添加自定义属性
            for prop_name in additional_props.keys():
                create_props.append(f"r.{prop_name} = rel_data.{prop_name}")
                match_props.append(f"r.{prop_name} = rel_data.{prop_name}")
            
            create_clause = ",\n                ".join(create_props)
            match_clause = ",\n                ".join(match_props)
            
            query = f"""
            UNWIND $batch_data as rel_data
            MATCH (source_node:{source_label} {{business_id: rel_data.start_business_id}})
            MATCH (target_node:{target_label} {{business_id: rel_data.end_business_id}})
            MERGE (source_node)-[r:{relationship_type}]->(target_node)
            ON CREATE SET 
                {create_clause}
            ON MATCH SET 
                {match_clause}
            """
            
            # 为每个关系添加类型信息
            for data in batch_data:
                data['relationship_type'] = relationship_type
                # 添加自定义属性
                for prop_name, prop_value in additional_props.items():
                    if prop_name not in data:
                        data[prop_name] = prop_value
            
            parameters = {
                'batch_data': batch_data
            }
            
            # 执行查询
            result = self.neo4j_storage.execute_query(query, parameters)
            
            logger.debug(f"成功执行批量关系创建，类型: {relationship_type}, 数量: {len(batch_data)}")
            return True
            
        except Exception as e:
            logger.error(f"UNWIND批量关系创建失败 (类型={relationship_type}): {str(e)}")
            return False
    
    def store_relationships_in_batches(self, 
                                     batch_data_list: List[Dict[str, Any]], 
                                     relationship_type: str,
                                     source_label: str,
                                     target_label: str,
                                     batch_size: int = 50,
                                     additional_properties: Dict[str, Any] = None) -> bool:
        """
        分批次存储关系的通用方法
        
        Args:
            batch_data_list: 全部关系数据列表
            relationship_type: 关系类型
            source_label: 源节点标签
            target_label: 目标节点标签
            batch_size: 批次大小
            additional_properties: 额外的关系属性
            
        Returns:
            bool: 是否成功
        """
        try:
            if not batch_data_list:
                logger.info("没有关系需要创建")
                return True
            
            total_batches = (len(batch_data_list) + batch_size - 1) // batch_size
            
            logger.info(f"开始批量存储 {len(batch_data_list)} 个 {relationship_type} 关系，分 {total_batches} 个批次")
            
            for i in range(0, len(batch_data_list), batch_size):
                batch = batch_data_list[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                logger.debug(f"处理第 {batch_num}/{total_batches} 批次，{len(batch)} 个关系")
                
                success = self.execute_batch_relationships_unwind(
                    batch, relationship_type, source_label, target_label, additional_properties
                )
                
                if not success:
                    logger.error(f"第 {batch_num} 批次关系存储失败")
                    return False
                
                logger.debug(f"第 {batch_num} 批次关系存储成功")
            
            logger.info(f"所有 {len(batch_data_list)} 个 {relationship_type} 关系存储完成")
            return True
            
        except Exception as e:
            logger.error(f"批量关系存储失败: {str(e)}")
            return False
    
    def query_hive_data(self, query: str) -> Optional[pd.DataFrame]:
        """
        查询Hive数据的通用方法
        
        Args:
            query: SQL查询语句
            
        Returns:
            Optional[pd.DataFrame]: 查询结果，失败返回None
        """
        try:
            logger.debug(f"执行Hive查询: {query}")
            
            df = self.impala_extractor.query_to_dataframe(query)
            
            if df is not None and not df.empty:
                logger.info(f"从Hive获取到 {len(df)} 条记录")
                return df
            else:
                logger.warning("Hive查询结果为空")
                return None
            
        except Exception as e:
            logger.error(f"Hive查询失败: {str(e)}")
            return None
    
    def validate_relationship_data(self, data: List[Dict[str, Any]], required_fields: List[str]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        验证关系数据的有效性
        
        Args:
            data: 关系数据列表
            required_fields: 必需字段列表
            
        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (有效数据, 错误信息列表)
        """
        valid_data = []
        errors = []
        
        for i, item in enumerate(data):
            item_errors = []
            
            # 检查必需字段
            for field in required_fields:
                if field not in item or item[field] is None or str(item[field]).strip() == '':
                    item_errors.append(f"缺少或无效的字段: {field}")
            
            if item_errors:
                errors.extend([f"记录 {i+1}: {error}" for error in item_errors])
            else:
                valid_data.append(item)
        
        if errors:
            logger.warning(f"数据验证发现 {len(errors)} 个错误，有效数据 {len(valid_data)} 条")
        
        return valid_data, errors
    
    def _handle_search_record_safely(self, record: Dict[str, Any], record_index: int = 0) -> Optional[Dict[str, Any]]:
        """
        安全处理搜索记录，避免字典更新错误
        
        Args:
            record: 数据库记录
            record_index: 记录索引
            
        Returns:
            Optional[Dict[str, Any]]: 处理后的记录，如果处理失败返回None
        """
        try:
            # 初始化结果字典
            result = {}
            
            # 安全获取字段值
            if hasattr(record, 'get'):
                # 如果是字典类型
                for key in record.keys():
                    result[key] = record.get(key)
            elif hasattr(record, 'keys'):
                # 如果是Neo4j Record类型
                for key in record.keys():
                    result[key] = record[key]
            else:
                # 如果是其他类型，转换为字典
                result = dict(record)
            
            return result
            
        except Exception as e:
            logger.error(f"处理第 {record_index + 1} 条记录失败: {str(e)}")
            return None
    
    def _safe_execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        安全执行Neo4j查询
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            if parameters is None:
                parameters = {}
            
            results = self.neo4j_storage.execute_query(query, parameters)
            return results if results else []
            
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            logger.debug(f"查询语句: {query}")
            logger.debug(f"查询参数: {parameters}")
            return []
    
    def _format_standard_statistics(self, 
                                   total_count: int, 
                                   relationship_type: str,
                                   additional_stats: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        格式化标准统计信息
        
        Args:
            total_count: 总关系数量
            relationship_type: 关系类型
            additional_stats: 额外的统计信息
            
        Returns:
            Dict[str, Any]: 标准格式的统计信息
        """
        base_stats = {
            "total_count": total_count,
            "relationship_type": relationship_type
        }
        
        if additional_stats:
            base_stats.update(additional_stats)
            
        return base_stats 