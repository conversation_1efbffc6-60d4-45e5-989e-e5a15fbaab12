"""
保险理赔与被保险人关系服务

专门负责 InsuranceClaims 节点与 InsuredPerson 节点之间关系的创建和管理
通过 InsuranceClaims.insured_user_cert_no 与 InsuredPerson.code 建立关联
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsInsuredPersonRelationshipService(BaseRelationshipService):
    """保险理赔与被保险人关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询保险理赔数据，与被保险人节点建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始创建保险理赔与被保险人关系")
            
            # 1. 从Neo4j查询保险理赔数据
            claims_data = self._query_claims_insured_person_data_from_neo4j(limit)
            if not claims_data:
                logger.warning("未找到有效的保险理赔与被保险人关联数据")
                return True
            
            # 2. 创建关系
            success = self._create_claims_insured_person_relationships(claims_data)
            
            if success:
                logger.info(f"成功处理 {len(claims_data)} 个保险理赔与被保险人关系")
                return True
            else:
                logger.error("创建保险理赔与被保险人关系失败")
                return False
            
        except Exception as e:
            logger.error(f"创建保险理赔与被保险人关系失败: {str(e)}")
            return False
    
    def _query_claims_insured_person_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保险理赔与被保险人的关联数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关联数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "claims.insured_user_cert_no IS NOT NULL", 
                "claims.insured_user_cert_no <> ''"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (claims:InsuranceClaims)
            WHERE {where_clause}
            RETURN claims.code as claims_code,
                   claims.case_no as case_no,
                   claims.insured_user_cert_no as insured_user_cert_no,
                   claims.insured_user_name as insured_user_name,
                   claims.policy_code as policy_code
            ORDER BY claims.code
            {limit_clause}
            """
            
            logger.info(f"查询保险理赔与被保险人关联数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 处理查询结果
            claims_data = []
            for record in results:
                insured_cert_no = str(record.get('insured_user_cert_no', '')).strip()
                claims_code = str(record.get('claims_code', '')).strip()
                
                if insured_cert_no and insured_cert_no != 'nan' and insured_cert_no != 'None' and claims_code:
                    claims_data.append({
                        'claims_code': claims_code,
                        'case_no': record.get('case_no', ''),
                        'insured_user_cert_no': insured_cert_no,
                        'insured_user_name': record.get('insured_user_name', ''),
                        'policy_code': record.get('policy_code', '')
                    })
            
            logger.info(f"找到 {len(claims_data)} 个有效的保险理赔与被保险人关联记录")
            return claims_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保险理赔与被保险人关联数据失败: {str(e)}")
            return []
    
    def _create_claims_insured_person_relationships(self, claims_data: List[Dict[str, Any]]) -> bool:
        """
        创建保险理赔与被保险人的关系
        
        Args:
            claims_data: 保险理赔数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            failed_count = 0
            
            for i in range(0, len(claims_data), batch_size):
                batch_data = claims_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (claims:InsuranceClaims {code: rel.claims_code})
                MATCH (person:InsuredPerson {code: rel.insured_user_cert_no})
                MERGE (claims)-[r:INVOLVES_INSURED_PERSON]->(person)
                SET r.relationship_type = 'INVOLVES_INSURED_PERSON',
                    r.relationship_status = 'active',
                    r.relationship_strength = 10,
                    r.data_source_node = 'InsuranceClaims',
                    r.remarks = '保险理赔涉及被保险人: ' + rel.case_no + ' - ' + COALESCE(rel.insured_user_name, ''),
                    r.policy_code = rel.policy_code,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    failed_count += len(batch_data) - batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系，失败 {len(batch_data) - batch_count} 个")
                else:
                    failed_count += len(batch_data)
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个保险理赔与被保险人关系，失败 {failed_count} 个")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保险理赔与被保险人关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保险理赔与被保险人关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有保险理赔与被保险人关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保险理赔与被保险人关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔与被保险人关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有被保险人关系的理赔案件数量
            claims_with_person_query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            RETURN count(DISTINCT claims) as claims_count
            """
            claims_result = self._safe_execute_query(claims_with_person_query)
            claims_count = claims_result[0]['claims_count'] if claims_result else 0
            
            # 涉及理赔的被保险人数量
            persons_with_claims_query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            RETURN count(DISTINCT person) as persons_count
            """
            persons_result = self._safe_execute_query(persons_with_claims_query)
            persons_count = persons_result[0]['persons_count'] if persons_result else 0
            
            # 未匹配的理赔记录统计
            unmatched_claims_query = """
            MATCH (claims:InsuranceClaims)
            WHERE claims.insured_user_cert_no IS NOT NULL 
              AND claims.insured_user_cert_no <> ''
              AND NOT (claims)-[:INVOLVES_INSURED_PERSON]->(:InsuredPerson)
            RETURN count(claims) as unmatched_claims
            """
            unmatched_result = self._safe_execute_query(unmatched_claims_query)
            unmatched_claims = unmatched_result[0]['unmatched_claims'] if unmatched_result else 0
            
            # 多次理赔的被保险人统计
            multiple_claims_query = """
            MATCH (person:InsuredPerson)<-[r:INVOLVES_INSURED_PERSON]-(claims:InsuranceClaims)
            WITH person, count(r) as claims_count
            WHERE claims_count > 1
            RETURN count(person) as persons_with_multiple_claims,
                   max(claims_count) as max_claims_per_person,
                   avg(claims_count) as avg_claims_per_person
            """
            multiple_result = self._safe_execute_query(multiple_claims_query)
            multiple_stats = multiple_result[0] if multiple_result else {
                'persons_with_multiple_claims': 0,
                'max_claims_per_person': 0,
                'avg_claims_per_person': 0
            }
            
            # 关系强度统计
            strength_query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            WHERE r.relationship_strength IS NOT NULL
            RETURN r.relationship_strength as strength, count(r) as count
            ORDER BY strength
            """
            strength_result = self._safe_execute_query(strength_query)
            strength_stats = {str(record['strength']): record['count'] for record in strength_result}
            
            # 数据源统计
            source_query = """
            MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="保险理赔与被保险人关系",
                additional_stats={
                    "claims_with_insured_person": claims_count,
                    "insured_persons_with_claims": persons_count,
                    "unmatched_claims": unmatched_claims,
                    "persons_with_multiple_claims": multiple_stats['persons_with_multiple_claims'],
                    "max_claims_per_person": multiple_stats['max_claims_per_person'],
                    "avg_claims_per_person": round(float(multiple_stats['avg_claims_per_person']), 2) if multiple_stats['avg_claims_per_person'] else 0,
                    "by_relationship_strength": strength_stats,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取保险理赔与被保险人关系统计失败: {str(e)}")
            return {}
    
    def search_claims_insured_person_relationships(self, 
                                                  claims_code: Optional[str] = None,
                                                  case_no: Optional[str] = None,
                                                  insured_cert_no: Optional[str] = None,
                                                  insured_name: Optional[str] = None,
                                                  include_node_details: bool = True,
                                                  limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔与被保险人关系
        
        Args:
            claims_code: 理赔编码过滤条件
            case_no: 案件号过滤条件
            insured_cert_no: 被保险人证件号过滤条件
            insured_name: 被保险人姓名过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if claims_code:
                conditions.append("claims.code CONTAINS $claims_code")
                parameters['claims_code'] = claims_code
            
            if case_no:
                conditions.append("claims.case_no CONTAINS $case_no")
                parameters['case_no'] = case_no
            
            if insured_cert_no:
                conditions.append("person.code CONTAINS $insured_cert_no")
                parameters['insured_cert_no'] = insured_cert_no
            
            if insured_name:
                conditions.append("person.name CONTAINS $insured_name")
                parameters['insured_name'] = insured_name
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
                WHERE {where_clause}
                RETURN r,
                       {{code: claims.code, case_no: claims.case_no, policy_code: claims.policy_code, 
                         insured_user_name: claims.insured_user_name}} as claims_info,
                       {{code: person.code, name: person.name, gender: person.gender}} as person_info
                ORDER BY claims.case_no, person.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (claims:InsuranceClaims)-[r:INVOLVES_INSURED_PERSON]->(person:InsuredPerson)
                WHERE {where_clause}
                RETURN r, claims.case_no as case_no, person.code as insured_cert_no
                ORDER BY claims.case_no, person.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'INVOLVES_INSURED_PERSON'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['claims'] = record['claims_info']
                    rel_data['insured_person'] = record['person_info']
                else:
                    rel_data['case_no'] = record['case_no']
                    rel_data['insured_cert_no'] = record['insured_cert_no']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个保险理赔与被保险人关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索保险理赔与被保险人关系失败: {str(e)}")
            return []
    
    def check_unmatched_claims(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        检查未匹配到被保险人的理赔记录
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 未匹配的理赔记录列表
        """
        try:
            query = f"""
            MATCH (claims:InsuranceClaims)
            WHERE claims.insured_user_cert_no IS NOT NULL 
              AND claims.insured_user_cert_no <> ''
              AND NOT (claims)-[:INVOLVES_INSURED_PERSON]->(:InsuredPerson)
            RETURN claims.code as claims_code,
                   claims.case_no as case_no,
                   claims.insured_user_cert_no as insured_user_cert_no,
                   claims.insured_user_name as insured_user_name,
                   claims.policy_code as policy_code
            ORDER BY claims.case_no
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            unmatched_claims = []
            for record in results:
                unmatched_claims.append({
                    'claims_code': record['claims_code'],
                    'case_no': record['case_no'],
                    'insured_user_cert_no': record['insured_user_cert_no'],
                    'insured_user_name': record['insured_user_name'],
                    'policy_code': record['policy_code']
                })
            
            logger.info(f"找到 {len(unmatched_claims)} 个未匹配的理赔记录")
            return unmatched_claims
            
        except Exception as e:
            logger.error(f"检查未匹配理赔记录失败: {str(e)}")
            return [] 