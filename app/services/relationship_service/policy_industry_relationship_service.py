"""
保单行业关系服务

专门负责 Policy 节点与 BznStandardIndustry 节点之间关系的创建和管理
根据Policy的industry属性关联到BznStandardIndustry的name字段
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyIndustryRelationshipService(BaseRelationshipService):
    """保单行业关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询Policy数据，根据industry关联到BznStandardIndustry节点
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建保单与bzn标准行业关系")
            
            # 1. 从Neo4j查询保单数据
            policy_data = self._query_policy_industry_data_from_neo4j(limit)
            if not policy_data:
                logger.warning("未找到有效的保单行业数据")
                return True
            
            # 2. 创建关系
            success = self._create_policy_industry_relationships(policy_data)
            
            if success:
                logger.info(f"成功处理 {len(policy_data)} 个保单行业关系")
                return True
            else:
                logger.error("创建保单行业关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建保单行业关系失败: {str(e)}")
            return False
    
    def _query_policy_industry_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询保单的行业数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保单行业数据列表
        """
        try:
            # 构建查询条件
            conditions = [
                "policy.industry IS NOT NULL", 
                "policy.industry <> ''",
                "policy.industry <> 'NULL'"
            ]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (policy:Policy)
            WHERE {where_clause}
            RETURN policy.code as policy_code,
                   policy.policy_id as policy_id,
                   policy.industry as industry,
                   policy.business_id as policy_business_id
            ORDER BY policy.code
            {limit_clause}
            """
            
            logger.info(f"查询保单行业数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                policy_code = str(record.get('policy_code', '')).strip()
                industry = str(record.get('industry', '')).strip()
                
                if policy_code and industry and industry != 'nan' and industry != 'None':
                    valid_data.append({
                        'policy_code': policy_code,
                        'policy_id': record.get('policy_id', ''),
                        'industry': industry,
                        'policy_business_id': record.get('policy_business_id', policy_code)
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的保单行业记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j保单行业数据失败: {str(e)}")
            return []
    
    def _create_policy_industry_relationships(self, policy_data: List[Dict[str, Any]]) -> bool:
        """
        创建保单与bzn标准行业的关系
        
        Args:
            policy_data: 保单数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            failed_count = 0
            
            for i in range(0, len(policy_data), batch_size):
                batch_data = policy_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (policy:Policy {code: rel.policy_code})
                MATCH (industry:Industry:BznStandardIndustry {name: rel.industry})
                MERGE (policy)-[r:BELONGS_TO_INDUSTRY]->(industry)
                SET r.relationship_type = 'BELONGS_TO_INDUSTRY',
                    r.relationship_status = 'active',
                    r.relationship_strength = 9,
                    r.data_source_node = 'Policy',
                    r.remarks = '保单所属行业: ' + rel.policy_id + ' -> ' + rel.industry,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    batch_failed = len(batch_data) - batch_count
                    failed_count += batch_failed
                    
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系，失败 {batch_failed} 个")
                    
                    if batch_failed > 0:
                        # 记录失败的行业名称用于调试
                        failed_industries = [data['industry'] for data in batch_data]
                        logger.warning(f"部分行业名称未找到匹配的BznStandardIndustry节点: {set(failed_industries)}")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
                    failed_count += len(batch_data)
            
            logger.info(f"总共成功创建 {success_count} 个保单行业关系，失败 {failed_count} 个")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建保单行业关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有保单行业关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (policy:Policy)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:BznStandardIndustry)
            DELETE r
            RETURN count(r) as deleted_count
            """
            
            result = self._safe_execute_query(query)
            deleted_count = 0
            if result:
                deleted_count = result[0].get('deleted_count', 0)
            
            logger.info(f"删除了 {deleted_count} 个保单行业关系")
            return True
            
        except Exception as e:
            logger.error(f"删除保单行业关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单行业关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数量
            total_query = """
            MATCH (policy:Policy)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:BznStandardIndustry)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 没有行业关联的保单数量
            unmatched_query = """
            MATCH (policy:Policy)
            WHERE policy.industry IS NOT NULL 
            AND policy.industry <> ''
            AND NOT EXISTS {
                MATCH (policy)-[:BELONGS_TO_INDUSTRY]->(industry:Industry:BznStandardIndustry)
            }
            RETURN count(policy) as unmatched_count
            """
            unmatched_result = self._safe_execute_query(unmatched_query)
            unmatched_count = unmatched_result[0]['unmatched_count'] if unmatched_result else 0
            
            return {
                "total_count": total_count,
                "relationship_type": "BELONGS_TO_INDUSTRY",
                "unmatched_policies": unmatched_count,
                "description": "保单行业关系统计"
            }
            
        except Exception as e:
            logger.error(f"获取保单行业关系统计失败: {str(e)}")
            return {
                "total_count": 0,
                "relationship_type": "BELONGS_TO_INDUSTRY",
                "error": str(e)
            }
    
    def get_unmatched_industries(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取未匹配的行业名称（Policy中有但BznStandardIndustry中没有的）
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 未匹配的行业数据
        """
        try:
            query = f"""
            MATCH (policy:Policy)
            WHERE policy.industry IS NOT NULL 
            AND policy.industry <> ''
            AND NOT EXISTS {{
                MATCH (industry:Industry:BznStandardIndustry {{name: policy.industry}})
            }}
            RETURN policy.industry as industry_name, 
                   count(policy) as policy_count
            ORDER BY policy_count DESC
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            unmatched_data = []
            for record in results:
                unmatched_data.append({
                    'industry_name': record.get('industry_name'),
                    'policy_count': record.get('policy_count', 0)
                })
            
            logger.info(f"找到 {len(unmatched_data)} 个未匹配的行业名称")
            return unmatched_data
            
        except Exception as e:
            logger.error(f"获取未匹配行业失败: {str(e)}")
            return []
    
    def search_policy_industry_relationships(self, 
                                           policy_code: Optional[str] = None,
                                           policy_id: Optional[str] = None,
                                           industry_name: Optional[str] = None,
                                           limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单行业关系
        
        Args:
            policy_code: 保单编码过滤条件
            policy_id: 保单ID过滤条件
            industry_name: 行业名称过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系数据列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if policy_code:
                conditions.append(f"policy.code CONTAINS '{policy_code}'")
            
            if policy_id:
                conditions.append(f"policy.policy_id CONTAINS '{policy_id}'")
            
            if industry_name:
                conditions.append(f"industry.name CONTAINS '{industry_name}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (policy:Policy)-[r:BELONGS_TO_INDUSTRY]->(industry:Industry:BznStandardIndustry)
            WHERE {where_clause}
            RETURN policy.code as policy_code,
                   policy.policy_id as policy_id,
                   industry.code as industry_code,
                   industry.name as industry_name,
                   r.created_at as relationship_created_at
            ORDER BY policy.code
            LIMIT {limit}
            """
            
            results = self._safe_execute_query(query)
            
            relationship_data = []
            for record in results:
                relationship_data.append({
                    'policy_code': record.get('policy_code'),
                    'policy_id': record.get('policy_id'),
                    'industry_code': record.get('industry_code'),
                    'industry_name': record.get('industry_name'),
                    'created_at': record.get('relationship_created_at')
                })
            
            logger.info(f"搜索到 {len(relationship_data)} 个保单行业关系")
            return relationship_data
            
        except Exception as e:
            logger.error(f"搜索保单行业关系失败: {str(e)}")
            return [] 