"""
被保险人保单关系服务

专门负责 InsuredPerson 节点与 Policy 节点之间关系的创建和管理
根据InsuredPerson的data_source_id属性关联到Policy的policy_code字段
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_relationship_service import BaseRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuredPersonPolicyRelationshipService(BaseRelationshipService):
    """被保险人保单关系服务"""
    
    def __init__(self):
        super().__init__()
    
    def create_relationships(self, limit: Optional[int] = None) -> bool:
        """
        从Neo4j查询被保险人数据，根据data_source_id关联到Policy节点，并建立关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始从Neo4j创建被保险人与保单关系")
            
            # 1. 从Neo4j查询被保险人数据
            insured_data = self._query_insured_policy_data_from_neo4j(limit)
            if not insured_data:
                logger.warning("未找到有效的被保险人保单数据")
                return True
            
            # 2. 创建关系
            success = self._create_insured_policy_relationships(insured_data)
            
            if success:
                logger.info(f"成功处理 {len(insured_data)} 个被保险人保单关系")
                return True
            else:
                logger.error("创建被保险人保单关系失败")
                return False
            
        except Exception as e:
            logger.error(f"从Neo4j创建被保险人保单关系失败: {str(e)}")
            return False
    
    def _query_insured_policy_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        从Neo4j查询被保险人的保单数据
        
        Args:
            limit: 记录数限制
            
        Returns:
            List[Dict[str, Any]]: 被保险人保单数据列表
        """
        try:
            # 构建查询条件
            conditions = ["insured.data_source_id IS NOT NULL", "insured.data_source_id <> ''"]
            where_clause = " AND ".join(conditions)
            limit_clause = f"LIMIT {limit}" if limit else ""
            
            query = f"""
            MATCH (insured:InsuredPerson)
            WHERE {where_clause}
            RETURN insured.code as insured_code,
                   insured.name as insured_name,
                   insured.data_source_id as policy_code,
                   insured.business_id as insured_business_id,
                   insured.id_card_number as id_card_number
            ORDER BY insured.code
            {limit_clause}
            """
            
            logger.info(f"查询被保险人保单数据，条件: {where_clause}")
            results = self._safe_execute_query(query)
            
            if not results:
                return []
            
            # 过滤掉无效的数据
            valid_data = []
            for record in results:
                policy_code = str(record.get('policy_code', '')).strip()
                insured_code = str(record.get('insured_code', '')).strip()
                
                if policy_code and policy_code != 'nan' and policy_code != 'None' and insured_code:
                    valid_data.append({
                        'insured_code': insured_code,
                        'insured_name': record.get('insured_name', ''),
                        'policy_code': policy_code,
                        'insured_business_id': record.get('insured_business_id', insured_code),
                        'id_card_number': record.get('id_card_number', '')
                    })
            
            logger.info(f"找到 {len(valid_data)} 个有效的被保险人保单记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"查询Neo4j被保险人保单数据失败: {str(e)}")
            return []
    
    def _create_insured_policy_relationships(self, insured_data: List[Dict[str, Any]]) -> bool:
        """
        创建被保险人与保单的关系
        
        Args:
            insured_data: 被保险人数据列表
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_size = 100
            success_count = 0
            
            for i in range(0, len(insured_data), batch_size):
                batch_data = insured_data[i:i + batch_size]
                
                # 构建批量创建关系的Cypher查询
                query = """
                UNWIND $relationships as rel
                MATCH (insured:InsuredPerson {code: rel.insured_code})
                MATCH (policy:Policy {policy_code: rel.policy_code})
                MERGE (policy)-[r:COVERS_INSURED]->(insured)
                SET r.relationship_type = 'COVERS_INSURED',
                    r.relationship_status = 'active',
                    r.relationship_strength = 9,
                    r.data_source_node = 'InsuredPerson',
                    r.remarks = '保单覆盖被保险人: ' + policy.policy_code + ' -> ' + rel.insured_name,
                    r.policy_code = rel.policy_code,
                    r.insured_code = rel.insured_code,
                    r.insured_name = rel.insured_name,
                    r.id_card_number = rel.id_card_number,
                    r.created_at = datetime(),
                    r.updated_at = datetime()
                RETURN count(r) as created_count
                """
                
                result = self._safe_execute_query(query, {'relationships': batch_data})
                if result:
                    batch_count = result[0].get('created_count', 0)
                    success_count += batch_count
                    logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
                else:
                    logger.error(f"批次 {i//batch_size + 1} 关系创建失败")
            
            logger.info(f"总共成功创建 {success_count} 个被保险人保单关系")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"创建被保险人保单关系失败: {str(e)}")
            return False
    
    def delete_all_relationships(self) -> bool:
        """
        删除所有被保险人保单关系
        
        Returns:
            bool: 是否成功
        """
        try:
            query = """
            MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
            DELETE r
            """
            
            self._safe_execute_query(query)
            logger.info("已删除所有被保险人保单关系")
            return True
            
        except Exception as e:
            logger.error(f"删除被保险人保单关系失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取被保险人保单关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总关系数统计
            total_query = """
            MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
            RETURN count(r) as total_count
            """
            total_result = self._safe_execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有保单关系的被保险人数量
            insured_with_policy_query = """
            MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
            RETURN count(DISTINCT insured) as insured_count
            """
            insured_result = self._safe_execute_query(insured_with_policy_query)
            insured_count = insured_result[0]['insured_count'] if insured_result else 0
            
            # 有被保险人关系的保单数量
            policy_with_insured_query = """
            MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
            RETURN count(DISTINCT policy) as policy_count
            """
            policy_result = self._safe_execute_query(policy_with_insured_query)
            policy_count = policy_result[0]['policy_count'] if policy_result else 0
            
            # 数据源统计
            source_query = """
            MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
            WHERE r.data_source_node IS NOT NULL
            RETURN r.data_source_node as source, count(r) as count
            ORDER BY source
            """
            source_result = self._safe_execute_query(source_query)
            source_stats = {record['source']: record['count'] for record in source_result}
            
            return self._format_standard_statistics(
                total_count=total_count,
                relationship_type="被保险人保单关系",
                additional_stats={
                    "insured_persons_with_policy": insured_count,
                    "policies_with_insured": policy_count,
                    "by_data_source_node": source_stats,
                }
            )
            
        except Exception as e:
            logger.error(f"获取被保险人保单关系统计失败: {str(e)}")
            return {}
    
    def search_insured_policy_relationships(self, 
                                           insured_code: Optional[str] = None,
                                           insured_name: Optional[str] = None,
                                           policy_code: Optional[str] = None,
                                           include_node_details: bool = True,
                                           limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索被保险人保单关系
        
        Args:
            insured_code: 被保险人编码过滤条件
            insured_name: 被保险人姓名过滤条件
            policy_code: 保单号过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        try:
            # 构建查询条件
            conditions = []
            parameters = {}
            
            if insured_code:
                conditions.append("insured.code CONTAINS $insured_code")
                parameters['insured_code'] = insured_code
            
            if insured_name:
                conditions.append("insured.name CONTAINS $insured_name")
                parameters['insured_name'] = insured_name
            
            if policy_code:
                conditions.append("policy.policy_code CONTAINS $policy_code")
                parameters['policy_code'] = policy_code
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            if include_node_details:
                query = f"""
                MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
                WHERE {where_clause}
                RETURN r,
                       {{code: policy.code, policy_code: policy.policy_code, name: policy.name}} as policy_info,
                       {{code: insured.code, name: insured.name, business_id: insured.business_id}} as insured_info
                ORDER BY policy.policy_code, insured.code
                LIMIT {limit}
                """
            else:
                query = f"""
                MATCH (policy:Policy)-[r:COVERS_INSURED]->(insured:InsuredPerson)
                WHERE {where_clause}
                RETURN r, policy.policy_code as policy_code, insured.code as insured_code
                ORDER BY policy.policy_code, insured.code
                LIMIT {limit}
                """
            
            results = self._safe_execute_query(query, parameters)
            
            relationships = []
            for record in results:
                # 创建基础关系数据
                rel_data = {
                    'relationship_type': 'COVERS_INSURED'
                }
                
                # 安全地添加关系属性
                try:
                    if record.get('r') and hasattr(record['r'], 'items'):
                        for key, value in record['r'].items():
                            rel_data[key] = value
                except (TypeError, AttributeError):
                    pass
                
                if include_node_details:
                    rel_data['policy'] = record['policy_info']
                    rel_data['insured_person'] = record['insured_info']
                else:
                    rel_data['policy_code'] = record['policy_code']
                    rel_data['insured_code'] = record['insured_code']
                
                relationships.append(rel_data)
            
            logger.info(f"搜索到 {len(relationships)} 个被保险人保单关系")
            return relationships
            
        except Exception as e:
            logger.error(f"搜索被保险人保单关系失败: {str(e)}")
            return [] 