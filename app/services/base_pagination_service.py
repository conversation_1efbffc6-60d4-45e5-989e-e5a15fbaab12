"""
分页数据提取服务基类

为各种数据提取服务提供通用的分页处理逻辑
"""

import pandas as pd
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.services.data_extraction.impala_extractor import ImpalaDataExtractor
from app.services.data_storage.neo4j_storage import Neo4jDataStorage
from app.utils.logger import get_logger
from app.config.settings import get_settings

logger = get_logger(__name__)


class BasePaginationService(ABC):
    """分页数据提取服务基类"""
    
    def __init__(self):
        self.impala_extractor = ImpalaDataExtractor()
        self.neo4j_storage = Neo4jDataStorage()
        self.settings = get_settings()
        
        # 从配置文件获取分页参数
        self.page_size = self.settings.data_page_size
        self.batch_size = self.settings.data_batch_size
    
    def extract_and_store_data(self, data_type: str) -> Dict[str, Any]:
        """
        分页提取并存储数据（处理全部数据直到结束）
        
        Args:
            data_type: 数据类型名称（用于日志）
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = datetime.now()
        logger.info(f"开始分页提取{data_type}数据，页大小: {self.page_size}, 存储批大小: {self.batch_size}")
        
        try:
            total_extracted = 0
            total_transformed = 0
            total_stored = 0
            total_failed = 0
            current_offset = 0
            page_num = 1
            
            while True:  # 无限循环，直到查询不到数据
                logger.info(f"开始处理第 {page_num} 页，偏移量: {current_offset}, 页大小: {self.page_size}")
                
                try:
                    # 执行分页查询（由子类实现）
                    query = self._build_query(limit=self.page_size, offset=current_offset)
                    df = self.impala_extractor.query_to_dataframe(query)
                    
                    if df.empty:
                        logger.info(f"第 {page_num} 页未查询到数据，所有数据处理完成")
                        break
                    
                    current_extracted = len(df)
                    total_extracted += current_extracted
                    logger.info(f"第 {page_num} 页提取 {current_extracted} 条记录，累计: {total_extracted}")
                    
                    # 分批处理当前页的数据
                    page_data = []
                    page_failed = 0
                    
                    for i in range(0, len(df), self.batch_size):
                        batch_df = df.iloc[i:i + self.batch_size]
                        
                        try:
                            # 转换数据（由子类实现）
                            transformed_data = self._transform_data(batch_df)
                            page_data.extend(transformed_data)
                            
                            # 立即存储数据（由子类实现）
                            if transformed_data:
                                store_result = self._store_data(transformed_data)
                                success_count, failed_count = self._parse_store_result(store_result, len(transformed_data))
                                
                                total_stored += success_count
                                page_failed += failed_count
                                
                                if failed_count == 0:
                                    logger.info(f"第 {page_num} 页批次 {i//self.batch_size + 1} 成功存储 {success_count} 个{data_type}节点")
                                else:
                                    logger.warning(f"第 {page_num} 页批次 {i//self.batch_size + 1} 部分失败: 成功 {success_count}, 失败 {failed_count}")
                            
                        except Exception as e:
                            logger.error(f"第 {page_num} 页批次 {i//self.batch_size + 1} 处理失败: {str(e)}")
                            page_failed += len(batch_df)
                            continue
                    
                    total_transformed += len(page_data)
                    total_failed += page_failed
                    
                    # 如果查询的数据少于页大小，说明已经是最后一页
                    if current_extracted < self.page_size:
                        logger.info(f"第 {page_num} 页是最后一页（{current_extracted}<{self.page_size}），处理完成")
                        break
                    
                    # 准备下一页
                    current_offset += self.page_size
                    page_num += 1
                    
                    # 简单的进度报告
                    logger.info(f"已处理 {page_num-1} 页，累计提取 {total_extracted} 条记录")
                    
                except Exception as e:
                    logger.error(f"第 {page_num} 页处理失败: {str(e)}")
                    total_failed += self.page_size  # 估算失败数量
                    current_offset += self.page_size
                    page_num += 1
                    continue
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"{data_type}数据分页提取完成，总计处理 {page_num-1} 页，累计提取 {total_extracted} 条，耗时: {duration:.2f}秒")
            
            return {
                "success": True,
                "extracted_count": total_extracted,
                "transformed_count": total_transformed,
                "stored_count": total_stored,
                "failed_count": total_failed,
                "pages_processed": page_num - 1,
                "duration": duration,
                "start_time": start_time,
                "end_time": datetime.now(),
                "errors": []
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"分页提取{data_type}数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "extracted_count": total_extracted if 'total_extracted' in locals() else 0,
                "transformed_count": total_transformed if 'total_transformed' in locals() else 0,
                "stored_count": total_stored if 'total_stored' in locals() else 0,
                "failed_count": total_failed if 'total_failed' in locals() else 0,
                "pages_processed": page_num - 1 if 'page_num' in locals() else 0,
                "duration": duration,
                "start_time": start_time,
                "end_time": datetime.now(),
                "errors": [str(e)]
            }
    
    @abstractmethod
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页查询SQL（由子类实现）
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        pass
    
    @abstractmethod
    def _transform_data(self, df: pd.DataFrame) -> List[Any]:
        """
        转换数据为图模型（由子类实现）
        
        Args:
            df: 原始数据
            
        Returns:
            List[Any]: 转换后的节点列表
        """
        pass
    
    @abstractmethod
    def _store_data(self, data: List[Any]) -> Any:
        """
        存储数据到Neo4j（由子类实现）
        
        Args:
            data: 要存储的数据
            
        Returns:
            Any: 存储结果
        """
        pass
    
    @abstractmethod
    def _parse_store_result(self, store_result: Any, total_count: int) -> tuple[int, int]:
        """
        解析存储结果（由子类实现）
        
        Args:
            store_result: 存储方法的返回结果
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        pass 