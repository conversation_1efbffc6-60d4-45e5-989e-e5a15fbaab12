"""
被保险人服务

负责被保险人数据的提取、转换和存储
"""

import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insured_person_transformer import InsuredPersonTransformer
from app.models.nodes.insured_person_node import InsuredPersonNode
from app.services.relationship_service.insured_person_policy_relationship_service import InsuredPersonPolicyRelationshipService
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuredPersonService(BasePaginationService):
    """被保险人服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = InsuredPersonTransformer()
        self.policy_relationship_service = InsuredPersonPolicyRelationshipService()
    
    def extract_and_store_insured_persons(self) -> Dict[str, Any]:
        """
        分页提取并存储被保险人数据（处理全部数据直到结束）
        
        使用昨天的日期作为分区值进行数据提取
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuredPerson")
    
    def create_policy_relationships(self, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        创建被保险人与保单的关系
        
        Args:
            limit: 限制处理的记录数（用于测试）
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info("开始创建被保险人与保单关系")
            
            success = self.policy_relationship_service.create_relationships(limit)
            
            if success:
                # 获取统计信息
                stats = self.policy_relationship_service.get_statistics()
                logger.info("被保险人与保单关系创建完成")
                return {
                    "success": True,
                    "message": "被保险人与保单关系创建成功",
                    "statistics": stats
                }
            else:
                logger.error("被保险人与保单关系创建失败")
                return {
                    "success": False,
                    "message": "被保险人与保单关系创建失败"
                }
                
        except Exception as e:
            error_msg = f"创建被保险人与保单关系时发生错误: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def delete_all_policy_relationships(self) -> Dict[str, Any]:
        """
        删除所有被保险人与保单的关系
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info("开始删除所有被保险人与保单关系")
            
            success = self.policy_relationship_service.delete_all_relationships()
            
            if success:
                logger.info("所有被保险人与保单关系删除完成")
                return {
                    "success": True,
                    "message": "所有被保险人与保单关系删除成功"
                }
            else:
                logger.error("删除被保险人与保单关系失败")
                return {
                    "success": False,
                    "message": "删除被保险人与保单关系失败"
                }
                
        except Exception as e:
            error_msg = f"删除被保险人与保单关系时发生错误: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def search_policy_relationships(self, 
                                   insured_code: Optional[str] = None,
                                   insured_name: Optional[str] = None,
                                   policy_code: Optional[str] = None,
                                   include_node_details: bool = True,
                                   limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索被保险人与保单的关系
        
        Args:
            insured_code: 被保险人编码过滤条件
            insured_name: 被保险人姓名过滤条件
            policy_code: 保单号过滤条件
            include_node_details: 是否包含节点详细信息
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 关系列表
        """
        return self.policy_relationship_service.search_insured_policy_relationships(
            insured_code=insured_code,
            insured_name=insured_name,
            policy_code=policy_code,
            include_node_details=include_node_details,
            limit=limit
        )
    
    def get_policy_relationship_statistics(self) -> Dict[str, Any]:
        """
        获取被保险人与保单关系统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.policy_relationship_service.get_statistics()
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页被保险人查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        # 使用昨天的日期作为分区值
        target_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        query = f"""
        SELECT 
            pct.policy_code as data_source_id,
            pct.insured_name as data_source_organization_name,
            pct.insured_user_name as name,
            pct.insured_cert_no as id_card_number,
            CASE 
                WHEN pct.gender = 1 THEN '男'
                WHEN pct.gender = 0 THEN '女'
                ELSE '未知'
            END as gender
        FROM dwadb.dwa_insured_person_common_data_test pct
        WHERE pct.day = '{target_date}'
        AND pct.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
        AND pct.insured_user_name IS NOT NULL
        AND pct.insured_user_name != ''
        ORDER BY pct.policy_code DESC, pct.insured_user_name DESC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuredPersonNode]:
        """
        转换被保险人数据为图模型
        
        Args:
            df: 原始被保险人数据
            
        Returns:
            List[InsuredPersonNode]: 转换后的被保险人节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[InsuredPersonNode]) -> bool:
        """
        存储被保险人数据到Neo4j
        
        Args:
            data: 要存储的被保险人数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_insured_persons(self, 
                              name_filter: Optional[str] = None,
                              id_card_filter: Optional[str] = None,
                              source_filter: Optional[str] = None,
                              limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索被保险人
        
        Args:
            name_filter: 姓名过滤条件
            id_card_filter: 身份证号过滤条件
            source_filter: 来源过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 被保险人列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if id_card_filter:
                conditions.append(f"n.id_card_number CONTAINS '{id_card_filter}'")
            
            if source_filter:
                conditions.append(f"n.data_source_id CONTAINS '{source_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:InsuredPerson)
            WHERE {where_clause}
            RETURN n, labels(n) as node_labels
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            insured_persons = []
            for record in results:
                from app.utils.serializers import serialize_neo4j_record
                person_data = serialize_neo4j_record(record["n"])
                person_data["node_labels"] = record["node_labels"]
                insured_persons.append(person_data)
            
            return insured_persons
            
        except Exception as e:
            logger.error(f"搜索被保险人失败: {str(e)}")
            return []
    
    def get_insured_person_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据编码获取单个被保险人
        
        Args:
            code: 人员编码
            
        Returns:
            Optional[Dict[str, Any]]: 被保险人详细信息
        """
        try:
            query = """
            MATCH (n:InsuredPerson)
            WHERE n.code = $code
            RETURN n, labels(n) as node_labels
            """
            
            results = self.neo4j_storage.execute_query(query, {"code": code})
            if results:
                from app.utils.serializers import serialize_neo4j_record
                person_data = serialize_neo4j_record(results[0]["n"])
                person_data["node_labels"] = results[0]["node_labels"]
                return person_data
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取被保险人失败: {str(e)}")
            return None
    
    def delete_all_insured_persons(self) -> bool:
        """
        删除所有被保险人数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = "MATCH (n:InsuredPerson) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有被保险人数据")
            return True
        except Exception as e:
            logger.error(f"删除被保险人数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取被保险人统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            query = """
            MATCH (n:InsuredPerson)
            RETURN count(n) as total_count,
                   count(DISTINCT n.data_source_id) as unique_source_count,
                   count(DISTINCT n.gender) as gender_types,
                   max(n.updated_at) as last_updated
            """
            
            results = self.neo4j_storage.execute_query(query)
            if results:
                from app.utils.serializers import serialize_neo4j_record
                return serialize_neo4j_record(results[0])
            else:
                return {
                    "total_count": 0, 
                    "unique_source_count": 0,
                    "gender_types": 0,
                    "last_updated": None
                }
                
        except Exception as e:
            logger.error(f"获取被保险人统计信息失败: {str(e)}")
            return {
                "total_count": 0, 
                "unique_source_count": 0,
                "gender_types": 0,
                "last_updated": None
            } 