"""
保险产品数据提取服务

从Hive数据库提取保险产品数据，转换后存储到Neo4j图数据库
"""

import pandas as pd
from typing import List, Optional, Dict, Any

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insurance_product_transformer import InsuranceProductTransformer
from app.models.nodes.insurance_product_node import InsuranceProductNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceProductService(BasePaginationService):
    """保险产品数据提取服务"""
    
    def __init__(self):
        super().__init__()
        self.insurance_product_transformer = InsuranceProductTransformer()
    
    def extract_and_store_insurance_products(self) -> Dict[str, Any]:
        """
        分页提取并存储保险产品数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuranceProduct")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保险产品查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT pd.product_id,
               pd.product_code,
               pd.product_name,
               pd.product_short_name,
               pd.product_desc,
               cp.com_code AS insurance_code,
               pd.insurance_name,
               tcb.unified_social_credit_code AS insurance_unified_social_credit_code,
               pd.one_level_pdt_cate,
               pd.two_level_pdt_cate,
               pd.product_type,
               pd.business_line,
               pd.system_source,
               pd.sale_category_1_desc AS sale_category
        FROM dimdb.dim_product_detail pd,
             dwddb.dwd_company_productsadmin cp,
             dimdb.dim_tyc_company_base tcb
        WHERE pd.product_code IS NOT NULL
          AND pd.product_name IS NOT NULL
          AND pd.system_source = 'official'
          AND cp.com_type = 1
          AND cp.com_name = pd.insurance_name
          AND cp.com_name = tcb.system_matched_company_name
        ORDER BY pd.product_id ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuranceProductNode]:
        """
        转换保险产品数据为图模型
        
        Args:
            df: 原始保险产品数据
            
        Returns:
            List[InsuranceProductNode]: 转换后的保险产品节点列表
        """
        try:
            insurance_products = self.insurance_product_transformer.transform_data(df)
            logger.debug(f"成功转换保险产品数据 {len(insurance_products)} 条")
            return insurance_products
        except Exception as e:
            logger.error(f"转换保险产品数据失败: {str(e)}")
            raise
    
    def _store_data(self, data: List[InsuranceProductNode]) -> int:
        """
        存储保险产品数据到Neo4j
        
        Args:
            data: 要存储的保险产品数据
            
        Returns:
            int: 成功存储的数量
        """
        return self.neo4j_storage.batch_create_nodes(data)
    
    def _parse_store_result(self, store_result: int, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的成功数量
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        success_count = store_result
        failed_count = total_count - success_count
        return success_count, failed_count
    
    def get_insurance_product_statistics(self) -> Dict[str, Any]:
        """
        获取Neo4j中保险产品数据统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            stats = self.neo4j_storage.get_node_statistics("InsuranceProduct")
            logger.info(f"获取保险产品统计信息: {stats}")
            return stats
        except Exception as e:
            logger.error(f"获取保险产品统计信息失败: {str(e)}")
            return {}
    
    def get_product_by_code(self, product_code: str) -> Optional[Dict[str, Any]]:
        """
        根据产品编码获取单个产品
        
        Args:
            product_code: 产品编码
            
        Returns:
            Optional[Dict[str, Any]]: 产品信息，如果不存在则返回None
        """
        try:
            query = """
            MATCH (n:InsuranceProduct)
            WHERE n.product_code = $product_code
            RETURN n
            LIMIT 1
            """
            
            results = self.neo4j_storage.execute_query(query, {"product_code": product_code})
            
            if results:
                product_data = dict(results[0]['n'])
                logger.info(f"成功获取产品: {product_code}")
                return product_data
            else:
                logger.warning(f"未找到产品: {product_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取产品失败: {str(e)}")
            return None

    def search_products(self, 
                       product_code_filter: Optional[str] = None,
                       product_name_filter: Optional[str] = None,
                       insurance_code_filter: Optional[str] = None,
                       product_type_filter: Optional[str] = None,
                       status_filter: Optional[str] = None,
                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索产品
        
        Args:
            product_code_filter: 产品编码过滤条件
            product_name_filter: 产品名称过滤条件
            insurance_code_filter: 保险公司编码过滤条件
            product_type_filter: 产品类型过滤条件
            status_filter: 产品状态过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 产品列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if product_code_filter:
                conditions.append(f"n.product_code CONTAINS '{product_code_filter}'")
            if product_name_filter:
                conditions.append(f"n.product_name CONTAINS '{product_name_filter}'")
            if insurance_code_filter:
                conditions.append(f"n.insurance_code = '{insurance_code_filter}'")
            if product_type_filter:
                conditions.append(f"n.product_type = '{product_type_filter}'")
            if status_filter:
                conditions.append(f"n.status = '{status_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:InsuranceProduct)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.product_code
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            products = []
            for record in results:
                node_data = dict(record['n'])
                products.append(node_data)
            
            logger.info(f"搜索到 {len(products)} 个产品")
            return products
            
        except Exception as e:
            logger.error(f"搜索产品失败: {str(e)}")
            return []

    def search_insurance_products(self, product_code: str = None, product_name: str = None, 
                                business_source: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        搜索保险产品信息
        
        Args:
            product_code: 产品代码
            product_name: 产品名称（模糊搜索）
            business_source: 业务来源
            limit: 结果限制
            
        Returns:
            List[Dict]: 保险产品信息列表
        """
        try:
            # 构建查询条件
            conditions = []
            if product_code:
                conditions.append(f"n.product_code = '{product_code}'")
            if product_name:
                conditions.append(f"n.product_name CONTAINS '{product_name}'")
            if business_source:
                conditions.append(f"n.business_source = '{business_source}'")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f"""
            MATCH (n:InsuranceProduct)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.updated_at DESC
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            insurance_products = [record["n"] for record in results]
            
            logger.info(f"搜索到 {len(insurance_products)} 个保险产品")
            return insurance_products
            
        except Exception as e:
            logger.error(f"搜索保险产品失败: {str(e)}")
            return []
    
    def extract_and_store_products(self,
                                  limit: Optional[int] = None,
                                  where_conditions: Optional[str] = None,
                                  custom_day: Optional[str] = None) -> Dict[str, Any]:
        """
        提取并存储产品数据
        
        Args:
            limit: 导入记录数限制
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期
            
        Returns:
            Dict[str, Any]: 导入结果
        """
        try:
            result = self.extract_and_store_insurance_products()
            return result
        except Exception as e:
            logger.error(f"提取并存储产品数据失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def delete_all_products(self) -> bool:
        """
        删除所有产品数据
        
        Returns:
            bool: 是否成功
        """
        return self.delete_all_insurance_products()

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取产品统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.get_insurance_product_statistics()

    def delete_all_insurance_products(self) -> bool:
        """
        删除所有保险产品数据（谨慎使用）
        
        Returns:
            bool: 删除是否成功
        """
        try:
            logger.warning("开始删除所有保险产品数据")
            
            query = "MATCH (n:InsuranceProduct) DELETE n"
            self.neo4j_storage.execute_query(query)
            
            logger.info("成功删除所有保险产品数据")
            return True
            
        except Exception as e:
            logger.error(f"删除保险产品数据失败: {str(e)}")
            return False 