"""
BZN标准行业服务

负责BZN标准行业数据的提取、转换和存储
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.bzn_standard_industry_transformer import BznStandardIndustryTransformer
from app.models.nodes.bzn_standard_industry_node import BznStandardIndustryNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BznStandardIndustryService(BasePaginationService):
    """BZN标准行业服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = BznStandardIndustryTransformer()
    
    def extract_and_store_industries(self) -> Dict[str, Any]:
        """
        分页提取并存储BZN标准行业数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("BznStandardIndustry")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页BZN标准行业查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT id, industry_name, keywords
        FROM dimdb.dim_industry_tag_code
        ORDER BY id ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        logger.info(f"构建查询SQL")
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[BznStandardIndustryNode]:
        """
        转换BZN标准行业数据为图模型
        
        Args:
            df: 原始行业数据
            
        Returns:
            List[BznStandardIndustryNode]: 转换后的BZN标准行业节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[BznStandardIndustryNode]) -> bool:
        """
        存储BZN标准行业数据到Neo4j
        
        Args:
            data: 要存储的BZN标准行业数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_industries(self, 
                         code_filter: Optional[str] = None,
                         name_filter: Optional[str] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索BZN标准行业
        
        Args:
            code_filter: 行业编码过滤条件
            name_filter: 行业名称过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: BZN标准行业列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if code_filter:
                conditions.append(f"n.code CONTAINS '{code_filter}'")
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:Industry:BznStandardIndustry)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.code
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            industries = []
            for record in results:
                node_data = dict(record['n'])
                industries.append(node_data)
            
            logger.info(f"搜索到 {len(industries)} 个BZN标准行业")
            return industries
            
        except Exception as e:
            logger.error(f"搜索BZN标准行业失败: {str(e)}")
            return []
    
    def delete_all_industries(self) -> bool:
        """
        删除所有BZN标准行业数据
        
        Returns:
            bool: 是否成功
        """
        try:
            query = "MATCH (n:Industry:BznStandardIndustry) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有BZN标准行业数据")
            return True
            
        except Exception as e:
            logger.error(f"删除BZN标准行业数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取BZN标准行业统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:Industry:BznStandardIndustry) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 也统计单一BznStandardIndustry标签的数量（如果有的话）
            single_query = "MATCH (n:BznStandardIndustry) WHERE NOT n:Industry RETURN count(n) as single_count"
            single_result = self.neo4j_storage.execute_query(single_query)
            single_count = single_result[0]['single_count'] if single_result else 0
            
            # 最后更新时间
            update_query = """
            MATCH (n:Industry:BznStandardIndustry) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            return {
                "total_count": total_count,
                "single_label_count": single_count,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取BZN标准行业统计失败: {str(e)}")
            return {} 