"""
保险销售渠道服务

负责保险销售渠道节点数据的提取、转换和存储（仅节点操作）
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insurance_sales_channel_transformer import InsuranceSalesChannelTransformer
from app.models.nodes.insurance_sales_channel_node import InsuranceSalesChannelNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceSalesChannelService(BasePaginationService):
    """保险销售渠道服务（仅负责节点操作）"""
    
    def __init__(self):
        super().__init__()
        self.transformer = InsuranceSalesChannelTransformer()
    
    def extract_and_store_channels(self) -> Dict[str, Any]:
        """
        分页提取并存储保险销售渠道节点数据（仅节点，不包含关系）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuranceSalesChannel")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保险销售渠道查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT 
            id, 
            org_name, 
            org_type, 
            level, 
            parent_id,
            head,
            bd_user_id,
            bds_user_id,
            status,
            province_code,
            province_name,
            city_code,
            city_name
        FROM dwddb.dwd_c_channel_market
        WHERE id IS NOT NULL 
        AND org_name IS NOT NULL
        ORDER BY id ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuranceSalesChannelNode]:
        """
        转换保险销售渠道数据为图模型
        
        Args:
            df: 原始保险销售渠道数据
            
        Returns:
            List[InsuranceSalesChannelNode]: 转换后的保险销售渠道节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[InsuranceSalesChannelNode]) -> bool:
        """
        存储保险销售渠道节点到Neo4j（仅节点，UPSERT模式）
        
        Args:
            data: 要存储的保险销售渠道数据
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始存储 {len(data)} 个保险销售渠道节点")
            
            # 存储节点（Neo4j存储已经支持UPSERT）
            success = self.neo4j_storage.store_nodes(data)
            if not success:
                logger.error("存储保险销售渠道节点失败")
                return False
            
            logger.info(f"成功存储 {len(data)} 个保险销售渠道节点")
            return True
            
        except Exception as e:
            logger.error(f"存储保险销售渠道数据失败: {str(e)}")
            return False
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def get_channel_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据编码获取单个保险销售渠道
        
        Args:
            code: 渠道编码
            
        Returns:
            Optional[Dict[str, Any]]: 渠道信息，如果未找到返回None
        """
        try:
            query = """
            MATCH (n:InsuranceSalesChannel)
            WHERE n.code = $code
            RETURN n
            LIMIT 1
            """
            
            results = self.neo4j_storage.execute_query(query, {'code': code})
            
            if results:
                return dict(results[0]['n'])
            
            return None
            
        except Exception as e:
            logger.error(f"根据编码查询渠道失败 (code={code}): {str(e)}")
            return None
    
    def get_channels_by_codes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """
        根据编码列表批量获取保险销售渠道
        
        Args:
            codes: 渠道编码列表
            
        Returns:
            List[Dict[str, Any]]: 渠道列表
        """
        try:
            if not codes:
                return []
            
            query = """
            MATCH (n:InsuranceSalesChannel)
            WHERE n.code IN $codes
            RETURN n
            ORDER BY n.code
            """
            
            results = self.neo4j_storage.execute_query(query, {'codes': codes})
            
            channels = []
            for record in results:
                node_data = dict(record['n'])
                channels.append(node_data)
            
            logger.info(f"批量查询到 {len(channels)} 个保险销售渠道")
            return channels
            
        except Exception as e:
            logger.error(f"批量查询渠道失败: {str(e)}")
            return []
    
    def search_channels(self, 
                       name_filter: Optional[str] = None,
                       org_type: Optional[int] = None,
                       status: Optional[int] = None,
                       province_name: Optional[str] = None,
                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险销售渠道
        
        Args:
            name_filter: 渠道名称过滤条件
            org_type: 组织类型过滤条件
            status: 状态过滤条件
            province_name: 省份名称过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保险销售渠道列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if org_type is not None:
                conditions.append(f"n.org_type = {org_type}")
            
            if status is not None:
                conditions.append(f"n.status = {status}")
            
            if province_name:
                conditions.append(f"n.province_name CONTAINS '{province_name}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:InsuranceSalesChannel)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            channels = []
            for record in results:
                node_data = dict(record['n'])
                channels.append(node_data)
            
            logger.info(f"搜索到 {len(channels)} 个保险销售渠道")
            return channels
            
        except Exception as e:
            logger.error(f"搜索保险销售渠道失败: {str(e)}")
            return []
    
    def delete_all_channels(self) -> bool:
        """
        删除所有保险销售渠道数据（仅节点，关系应由关系服务处理）
        
        Returns:
            bool: 是否成功
        """
        try:
            # 删除节点（关系会自动删除）
            node_query = "MATCH (n:InsuranceSalesChannel) DELETE n"
            self.neo4j_storage.execute_query(node_query)
            logger.info("已删除所有保险销售渠道节点")
            return True
            
        except Exception as e:
            logger.error(f"删除保险销售渠道节点失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险销售渠道统计信息（仅节点统计）
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:InsuranceSalesChannel) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按组织类型统计
            type_query = """
            MATCH (n:InsuranceSalesChannel) 
            WHERE n.org_type IS NOT NULL
            RETURN n.org_type as org_type, count(n) as count
            ORDER BY org_type
            """
            type_result = self.neo4j_storage.execute_query(type_query)
            type_stats = {record['org_type']: record['count'] for record in type_result}
            
            # 按状态统计
            status_query = """
            MATCH (n:InsuranceSalesChannel) 
            WHERE n.status IS NOT NULL
            RETURN n.status as status, count(n) as count
            ORDER BY status
            """
            status_result = self.neo4j_storage.execute_query(status_query)
            status_stats = {record['status']: record['count'] for record in status_result}
            
            # 最后更新时间
            update_query = """
            MATCH (n:InsuranceSalesChannel) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            return {
                "total_count": total_count,
                "by_org_type": type_stats,
                "by_status": status_stats,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取保险销售渠道统计失败: {str(e)}")
            return {} 