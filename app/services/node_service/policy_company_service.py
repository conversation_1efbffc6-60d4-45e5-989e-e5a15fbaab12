"""
保单企业服务

负责保单企业数据的提取、转换和存储
"""

import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.policy_company_transformer import PolicyCompanyTransformer
from app.models.nodes.policy_company_node import PolicyCompanyNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyCompanyService(BasePaginationService):
    """保单企业服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = PolicyCompanyTransformer()
    
    def extract_and_store_companies(self) -> Dict[str, Any]:
        """
        分页提取并存储保单企业数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("PolicyCompany")
    
    def _build_query(self, limit: int, offset: int) -> str:
        """
        构建保单企业查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        # 获取昨天的日期作为分区值
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        query = f"""
        SELECT tcb.system_matched_company_name,
               tcb.unified_social_credit_code,
               tcb.legal_representative,
               tcb.phone,
               tcb.additional_phones,
               tcb.email,
               tcb.additional_emails,
               area_province.city_code AS province_code,
               tcb.province,
               area_city.city_code     AS city_code,
               tcb.city,
               area_district.city_code AS district_code,
               tcb.district,
               tcb.industry_category,
               tcb.industry_major,
               tcb.industry_medium,
               tcb.industry_minor,
               tcb.company_address
        FROM dimdb.dim_tyc_company_base tcb
        LEFT JOIN dimdb.dim_area_code area_province
                  ON tcb.province = area_province.city_name AND area_province.city_level = '1'
        LEFT JOIN dimdb.dim_area_code area_city
                  ON tcb.city = area_city.city_name AND area_city.city_parent_code = area_province.city_code AND
                     area_city.city_level = '2'
        LEFT JOIN dimdb.dim_area_code area_district
                  ON tcb.district = area_district.city_name AND
                     area_district.city_parent_code = area_city.city_code AND
                     area_district.city_level = '3'
        WHERE tcb.system_matched_company_name IN (
            SELECT company_name
            FROM (
                SELECT pcd1.insure_name as company_name
                FROM dwadb.dwa_policy_common_data_test pcd1
                WHERE pcd1.day = '{yesterday}'
                    AND pcd1.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
                UNION
                SELECT pcd2.insured_name as company_name
                FROM dwadb.dwa_policy_common_data_test pcd2
                WHERE pcd2.day = '{yesterday}'
                    AND pcd2.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
            ) tmp 
            ORDER BY company_name ASC
        )
        AND tcb.system_matched_company_name IS NOT NULL
        ORDER BY tcb.system_matched_company_name ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[PolicyCompanyNode]:
        """
        转换保单企业数据为图模型
        
        Args:
            df: 原始保单企业数据
            
        Returns:
            List[PolicyCompanyNode]: 转换后的保单企业节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[PolicyCompanyNode]) -> bool:
        """
        存储保单企业数据到Neo4j
        
        Args:
            data: 要存储的保单企业数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_companies(self, 
                        name_filter: Optional[str] = None,
                        credit_code_filter: Optional[str] = None,
                        limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单企业
        
        Args:
            name_filter: 名称过滤条件
            credit_code_filter: 信用代码过滤条件  
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保单企业列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if credit_code_filter:
                conditions.append(f"n.unified_social_credit_code = '{credit_code_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:PolicyCompany)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            companies = []
            for record in results:
                node_data = dict(record['n'])
                companies.append(node_data)
            
            logger.info(f"搜索到 {len(companies)} 个保单企业")
            return companies
            
        except Exception as e:
            logger.error(f"搜索保单企业失败: {str(e)}")
            return []
    
    def delete_all_companies(self) -> bool:
        """
        删除所有保单企业数据
        
        Returns:
            bool: 是否成功
        """
        try:
            query = "MATCH (n:PolicyCompany) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保单企业数据")
            return True
            
        except Exception as e:
            logger.error(f"删除保单企业数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单企业统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:PolicyCompany) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 有信用代码的统计
            credit_query = """
            MATCH (n:PolicyCompany) 
            WHERE n.unified_social_credit_code <> '' 
            RETURN count(n) as credit_code_count
            """
            credit_result = self.neo4j_storage.execute_query(credit_query)
            credit_code_count = credit_result[0]['credit_code_count'] if credit_result else 0
            
            # 最后更新时间
            update_query = """
            MATCH (n:PolicyCompany) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            return {
                "total_count": total_count,
                "credit_code_count": credit_code_count,
                "no_credit_code_count": total_count - credit_code_count,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取保单企业统计失败: {str(e)}")
            return {} 