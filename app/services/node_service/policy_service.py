"""
保单服务

负责保单数据的提取、转换和存储
"""

import pandas as pd
from datetime import date, timedelta
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.policy_transformer import PolicyTransformer
from app.models.nodes.policy_node import PolicyNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyService(BasePaginationService):
    """保单服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = PolicyTransformer()
    
    def extract_and_store_policies(self,
                                  limit: Optional[int] = None,
                                  where_conditions: Optional[str] = None,
                                  custom_day: Optional[str] = None) -> Dict[str, Any]:
        """
        分页提取并存储保单数据（处理全部数据直到结束）
        
        Args:
            limit: 导入记录数限制
            where_conditions: 额外的WHERE条件
            custom_day: 自定义日期
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("Policy")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保单查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        # 获取当前日期的前一天作为分区值
        yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        query = f"""
        SELECT 
            policy_code, 
            policy_start_date, 
            policy_end_date,
            policy_status,
            insurance_code,
            insurance_name,
            insure_code,
            insure_name,
            insured_code, 
            insured_name,
            product_code,
            product_name,
            channel_id,
            channel_name,
            sale_code,
            sale_name,
            sale_mobile,
            assure_time,
            sku_ratio,
            policy_coverage,
            industry
        FROM dwadb.dwa_policy_common_data_test
        WHERE product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
        AND policy_code IS NOT NULL
        AND `day` = '{yesterday}'
        ORDER BY policy_code ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        logger.info(f"构建保单查询SQL，使用分区日期: {yesterday}")
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[PolicyNode]:
        """
        转换保单数据为图模型
        
        Args:
            df: 原始保单数据
            
        Returns:
            List[PolicyNode]: 转换后的保单节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[PolicyNode]) -> bool:
        """
        存储保单数据到Neo4j
        
        Args:
            data: 要存储的保单数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_policies(self, 
                       policy_code_filter: Optional[str] = None,
                       insured_name_filter: Optional[str] = None,
                       insurance_code_filter: Optional[str] = None,
                       product_code_filter: Optional[str] = None,
                       policy_status_filter: Optional[str] = None,
                       start_date_filter: Optional[str] = None,
                       end_date_filter: Optional[str] = None,
                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保单
        
        Args:
            policy_code_filter: 保单编码过滤条件
            insured_name_filter: 被保险人姓名过滤条件
            insurance_code_filter: 保险公司编码过滤条件
            product_code_filter: 产品编码过滤条件
            policy_status_filter: 保单状态过滤条件
            start_date_filter: 起保日期过滤条件
            end_date_filter: 终保日期过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保单列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if policy_code_filter:
                conditions.append(f"n.policy_code CONTAINS '{policy_code_filter}'")
            if insured_name_filter:
                conditions.append(f"n.insured_name CONTAINS '{insured_name_filter}'")
            if insurance_code_filter:
                conditions.append(f"n.insurance_code = '{insurance_code_filter}'")
            if product_code_filter:
                conditions.append(f"n.product_code = '{product_code_filter}'")
            if policy_status_filter:
                conditions.append(f"n.policy_status = '{policy_status_filter}'")
            if start_date_filter:
                conditions.append(f"n.policy_start_date >= date('{start_date_filter}')")
            if end_date_filter:
                conditions.append(f"n.policy_end_date <= date('{end_date_filter}')")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:Policy)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.policy_code
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            policies = []
            for record in results:
                node_data = dict(record['n'])
                policies.append(node_data)
            
            logger.info(f"搜索到 {len(policies)} 个保单")
            return policies
            
        except Exception as e:
            logger.error(f"搜索保单失败: {str(e)}")
            return []
    
    def delete_all_policies(self) -> bool:
        """
        删除所有保单数据
        
        Returns:
            bool: 是否成功
        """
        try:
            query = "MATCH (n:Policy) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保单数据")
            return True
            
        except Exception as e:
            logger.error(f"删除保单数据失败: {str(e)}")
            return False
    
    def get_policy_by_code(self, policy_code: str) -> Optional[Dict[str, Any]]:
        """
        根据保单编码获取单个保单
        
        Args:
            policy_code: 保单编码
            
        Returns:
            Optional[Dict[str, Any]]: 保单信息，如果不存在则返回None
        """
        try:
            query = """
            MATCH (n:Policy)
            WHERE n.policy_code = $policy_code
            RETURN n
            LIMIT 1
            """
            
            results = self.neo4j_storage.execute_query(query, {"policy_code": policy_code})
            
            if results:
                policy_data = dict(results[0]['n'])
                logger.info(f"成功获取保单: {policy_code}")
                return policy_data
            else:
                logger.warning(f"未找到保单: {policy_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取保单失败: {str(e)}")
            return None

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保单统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:Policy) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 最后更新时间
            update_query = """
            MATCH (n:Policy) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            return {
                "total_count": total_count,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取保单统计失败: {str(e)}")
            return {} 