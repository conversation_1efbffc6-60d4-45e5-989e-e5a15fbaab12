"""
BZN标准职业服务

提供BZN标准职业数据的提取、转换和存储功能
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.bzn_standard_occupation_transformer import BznStandardOccupationTransformer
from app.models.nodes.bzn_standard_occupation_node import BznStandardOccupationNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BznStandardOccupationService(BasePaginationService):
    """BZN标准职业服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = BznStandardOccupationTransformer()

    def extract_and_store_occupations(self) -> Dict[str, Any]:
        """
        分页提取并存储BZN标准职业数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("BznStandardOccupation")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页BZN标准职业查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT 
            bzn_work_name,
            bzn_industry_large,
            bzn_industry_medium,
            bzn_industry_small,
            bzn_work_type_level
        FROM dimdb.dim_bzn_insurance_work_type
        WHERE product_code = 'PP00000715'
        ORDER BY bzn_industry_small ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[BznStandardOccupationNode]:
        """
        转换BZN标准职业数据为图模型
        
        Args:
            df: 原始BZN标准职业数据
            
        Returns:
            List[BznStandardOccupationNode]: 转换后的BZN标准职业节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[BznStandardOccupationNode]) -> bool:
        """
        存储BZN标准职业数据到Neo4j
        
        Args:
            data: 要存储的BZN标准职业数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_occupations(
        self,
        name: Optional[str] = None,
        code: Optional[str] = None,
        industry_large: Optional[str] = None,
        industry_medium: Optional[str] = None,
        industry_small: Optional[str] = None,
        work_type_level: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        搜索BZN标准职业
        
        Args:
            name: 职业名称
            code: 职业编码
            industry_large: 行业大类
            industry_medium: 行业中类
            industry_small: 行业小类
            work_type_level: 职业类型等级
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, Any]]: BZN标准职业列表
        """
        try:
            conditions = []
            params = {}
            
            if name:
                conditions.append("n.name CONTAINS $name")
                params["name"] = name
            
            if code:
                conditions.append("n.code = $code")
                params["code"] = code
            
            if industry_large:
                conditions.append("n.industry_large = $industry_large")
                params["industry_large"] = industry_large
            
            if industry_medium:
                conditions.append("n.industry_medium = $industry_medium")
                params["industry_medium"] = industry_medium
            
            if industry_small:
                conditions.append("n.industry_small = $industry_small")
                params["industry_small"] = industry_small
            
            if work_type_level:
                conditions.append("n.work_type_level = $work_type_level")
                params["work_type_level"] = work_type_level
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f"""
            MATCH (n:BznStandardOccupation)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query, params)
            occupations = [record['n'] for record in results]
            
            logger.info(f"搜索到 {len(occupations)} 个BZN标准职业")
            return occupations
            
        except Exception as e:
            logger.error(f"搜索BZN标准职业失败: {str(e)}")
            return []
    
    def get_occupation_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据职业编码获取BZN标准职业
        
        Args:
            code: 职业编码
            
        Returns:
            Optional[Dict[str, Any]]: BZN标准职业信息
        """
        occupations = self.search_occupations(
            code=code,
            limit=1
        )
        return occupations[0] if occupations else None
    
    def get_occupation_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据职业名称获取BZN标准职业
        
        Args:
            name: 职业名称
            
        Returns:
            Optional[Dict[str, Any]]: BZN标准职业信息
        """
        occupations = self.search_occupations(
            name=name,
            limit=1
        )
        return occupations[0] if occupations else None
    
    def delete_all_occupations(self) -> bool:
        """
        删除所有BZN标准职业数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = "MATCH (n:BznStandardOccupation) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有BZN标准职业数据")
            return True
        except Exception as e:
            logger.error(f"删除BZN标准职业数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取BZN标准职业统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            count_query = "MATCH (n:BznStandardOccupation) RETURN count(n) as total_count"
            count_result = self.neo4j_storage.execute_query(count_query)
            total_count = count_result[0]['total_count'] if count_result else 0
            
            # 按行业大类统计
            industry_large_query = """
            MATCH (n:BznStandardOccupation) 
            WHERE n.industry_large <> '' AND n.industry_large IS NOT NULL
            RETURN n.industry_large as industry_large, count(n) as count
            ORDER BY count DESC
            """
            industry_large_result = self.neo4j_storage.execute_query(industry_large_query)
            
            # 按职业等级统计
            work_level_query = """
            MATCH (n:BznStandardOccupation) 
            WHERE n.work_type_level <> '' AND n.work_type_level IS NOT NULL
            RETURN n.work_type_level as work_type_level, count(n) as count
            ORDER BY count DESC
            """
            work_level_result = self.neo4j_storage.execute_query(work_level_query)
            
            # 最新更新时间
            latest_query = """
            MATCH (n:BznStandardOccupation) 
            RETURN max(n.updated_at) as last_updated
            """
            latest_result = self.neo4j_storage.execute_query(latest_query)
            last_updated = latest_result[0]['last_updated'] if latest_result else None
            
            return {
                "total_count": total_count,
                "industry_large_distribution": industry_large_result,
                "work_type_level_distribution": work_level_result,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取BZN标准职业统计信息失败: {str(e)}")
            return {
                "total_count": 0,
                "industry_large_distribution": [],
                "work_type_level_distribution": [],
                "last_updated": None
            } 