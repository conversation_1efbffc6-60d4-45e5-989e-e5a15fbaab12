"""
保险公司服务

提供保险公司数据的提取、转换和存储功能
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insurance_company_transformer import InsuranceCompanyTransformer
from app.models.nodes.insurance_company_node import InsuranceCompanyNode
from app.utils.logger import get_logger
from app.utils.serializers import serialize_neo4j_value

logger = get_logger(__name__)


class InsuranceCompanyService(BasePaginationService):
    """保险公司服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = InsuranceCompanyTransformer()

    def extract_and_store_companies(self) -> Dict[str, Any]:
        """
        分页提取并存储保险公司数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuranceCompany")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保险公司查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT tcb.system_matched_company_name as org_name,
               cp.short_name                   as org_short_name,
               tcb.unified_social_credit_code,
               tcb.legal_representative,
               tcb.phone,
               tcb.additional_phones,
               tcb.email,
               tcb.additional_emails,
               area_province.city_code         AS province_code,
               tcb.province,
               area_city.city_code             AS city_code,
               tcb.city,
               area_district.city_code         AS district_code,
               tcb.district,
               tcb.industry_category,
               tcb.industry_major,
               tcb.industry_medium,
               tcb.industry_minor,
               tcb.company_address
        FROM dwddb.dwd_company_productsadmin cp
        INNER JOIN dimdb.dim_tyc_company_base tcb ON cp.com_name = tcb.system_matched_company_name AND cp.com_type = 1
        LEFT JOIN dimdb.dim_area_code area_province
                  ON tcb.province = area_province.city_name AND area_province.city_level = '1'
        LEFT JOIN dimdb.dim_area_code area_city
                  ON tcb.city = area_city.city_name AND area_city.city_parent_code = area_province.city_code AND
                     area_city.city_level = '2'
        LEFT JOIN dimdb.dim_area_code area_district
                  ON tcb.district = area_district.city_name AND
                     area_district.city_parent_code = area_city.city_code AND
                     area_district.city_level = '3'
        WHERE tcb.original_file_name IS NOT NULL
        ORDER BY tcb.original_file_name ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuranceCompanyNode]:
        """
        转换保险公司数据为图模型
        
        Args:
            df: 原始保险公司数据
            
        Returns:
            List[InsuranceCompanyNode]: 转换后的保险公司节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[InsuranceCompanyNode]) -> bool:
        """
        存储保险公司数据到Neo4j
        
        Args:
            data: 要存储的保险公司数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_companies(
        self,
        name: Optional[str] = None,
        short_name: Optional[str] = None,
        code: Optional[str] = None,
        unified_social_credit_code: Optional[str] = None,
        legal_representative: Optional[str] = None,
        phone: Optional[str] = None,
        province: Optional[str] = None,
        city: Optional[str] = None,
        industry_category: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        搜索保险公司
        
        Args:
            name: 机构名称
            short_name: 机构简称
            code: 组织编码
            unified_social_credit_code: 统一社会信用代码
            legal_representative: 法定代表人
            phone: 电话
            province: 省份
            city: 城市
            industry_category: 行业分类
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, Any]]: 保险公司列表
        """
        try:
            conditions = []
            params = {}
            
            if name:
                conditions.append("n.name CONTAINS $name")
                params["name"] = name
            
            if short_name:
                conditions.append("n.short_name CONTAINS $short_name")
                params["short_name"] = short_name
            
            if code:
                conditions.append("n.code = $code")
                params["code"] = code
            
            if unified_social_credit_code:
                conditions.append("n.unified_social_credit_code = $unified_social_credit_code")
                params["unified_social_credit_code"] = unified_social_credit_code
            
            if legal_representative:
                conditions.append("n.legal_representative CONTAINS $legal_representative")
                params["legal_representative"] = legal_representative
            
            if phone:
                conditions.append("(n.phone CONTAINS $phone OR n.additional_phones CONTAINS $phone)")
                params["phone"] = phone
            
            if province:
                conditions.append("n.province = $province")
                params["province"] = province
            
            if city:
                conditions.append("n.city = $city")
                params["city"] = city
            
            if industry_category:
                conditions.append("n.industry_category = $industry_category")
                params["industry_category"] = industry_category
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f"""
            MATCH (n:InsuranceCompany)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query, params)
            companies = [record['n'] for record in results]
            
            logger.info(f"搜索到 {len(companies)} 个保险公司")
            return companies
            
        except Exception as e:
            logger.error(f"搜索保险公司失败: {str(e)}")
            return []
    
    def get_company_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据组织编码获取保险公司
        
        Args:
            code: 组织编码
            
        Returns:
            Optional[Dict[str, Any]]: 保险公司信息
        """
        companies = self.search_companies(
            code=code,
            limit=1
        )
        return companies[0] if companies else None
    
    def get_company_by_unified_social_credit_code(self, unified_social_credit_code: str) -> Optional[Dict[str, Any]]:
        """
        根据统一社会信用代码获取保险公司
        
        Args:
            unified_social_credit_code: 统一社会信用代码
            
        Returns:
            Optional[Dict[str, Any]]: 保险公司信息
        """
        companies = self.search_companies(
            unified_social_credit_code=unified_social_credit_code,
            limit=1
        )
        return companies[0] if companies else None
    
    def delete_all_companies(self) -> bool:
        """
        删除所有保险公司数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = "MATCH (n:InsuranceCompany) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保险公司数据")
            return True
        except Exception as e:
            logger.error(f"删除保险公司数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险公司统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            count_query = "MATCH (n:InsuranceCompany) RETURN count(n) as total_count"
            count_result = self.neo4j_storage.execute_query(count_query)
            total_count = count_result[0]['total_count'] if count_result else 0
            
            # 有组织编码的公司数量
            code_query = """
            MATCH (n:InsuranceCompany) 
            WHERE n.code <> '' AND n.code IS NOT NULL
            RETURN count(n) as code_count
            """
            code_result = self.neo4j_storage.execute_query(code_query)
            code_count = code_result[0]['code_count'] if code_result else 0
            
            # 有信用代码的公司数量
            credit_code_query = """
            MATCH (n:InsuranceCompany) 
            WHERE n.unified_social_credit_code <> '' AND n.unified_social_credit_code IS NOT NULL
            RETURN count(n) as credit_code_count
            """
            credit_result = self.neo4j_storage.execute_query(credit_code_query)
            credit_code_count = credit_result[0]['credit_code_count'] if credit_result else 0
            
            # 有法定代表人的公司数量
            legal_rep_query = """
            MATCH (n:InsuranceCompany) 
            WHERE n.legal_representative <> '' AND n.legal_representative IS NOT NULL
            RETURN count(n) as legal_rep_count
            """
            legal_rep_result = self.neo4j_storage.execute_query(legal_rep_query)
            legal_rep_count = legal_rep_result[0]['legal_rep_count'] if legal_rep_result else 0
            
            # 最新更新时间
            latest_query = """
            MATCH (n:InsuranceCompany) 
            RETURN max(n.updated_at) as last_updated
            """
            latest_result = self.neo4j_storage.execute_query(latest_query)
            last_updated = latest_result[0]['last_updated'] if latest_result else None
            
            # 序列化DateTime对象
            last_updated_str = serialize_neo4j_value(last_updated) if last_updated else None
            
            return {
                "total_count": total_count,
                "with_code": code_count,
                "without_code": total_count - code_count,
                "with_credit_code": credit_code_count,
                "without_credit_code": total_count - credit_code_count,
                "with_legal_representative": legal_rep_count,
                "without_legal_representative": total_count - legal_rep_count,
                "last_updated": last_updated_str
            }
            
        except Exception as e:
            logger.error(f"获取保险公司统计信息失败: {str(e)}")
            return {
                "total_count": 0,
                "with_code": 0,
                "without_code": 0,
                "with_credit_code": 0,
                "without_credit_code": 0,
                "with_legal_representative": 0,
                "without_legal_representative": 0,
                "last_updated": None
            } 