"""
保险代理人服务

负责保险代理人数据的提取、转换和存储
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insurance_agent_person_transformer import InsuranceAgentPersonTransformer
from app.models.nodes.insurance_agent_person_node import InsuranceAgentPersonNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceAgentPersonService(BasePaginationService):
    """保险代理人服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = InsuranceAgentPersonTransformer()
    
    def extract_and_store_insurance_agent_persons(self) -> Dict[str, Any]:
        """
        分页提取并存储保险代理人数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuranceAgentPerson")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保险代理人查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT
            id AS code,
            sale_name AS name,
            sale_mobile AS phone,
            email,
            CASE
                WHEN gender = 1 THEN '男'
                WHEN gender = 0 THEN '女'
                ELSE '未知'
            END as gender,
            province_code,
            province_name,
            city_code,
            city_name,
            county_code,
            county_name,
            CAST(referrer_user_id AS STRING) AS referrer_user_id,
            CAST(bdm_user_id AS STRING) AS bdm_user_id,
            CAST(channel_id AS STRING) AS channel_id,
            CAST(team_id AS STRING) AS team_id,
            status
        FROM dwddb.dwd_u_user_market
        WHERE user_type = 4
        AND sale_name IS NOT NULL
        AND sale_name != ''
        ORDER BY id ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuranceAgentPersonNode]:
        """
        转换保险代理人数据为图模型
        
        Args:
            df: 原始保险代理人数据
            
        Returns:
            List[InsuranceAgentPersonNode]: 转换后的保险代理人节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[InsuranceAgentPersonNode]) -> bool:
        """
        存储保险代理人数据到Neo4j
        
        Args:
            data: 要存储的保险代理人数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_insurance_agent_persons(self, 
                                      name_filter: Optional[str] = None,
                                      code_filter: Optional[str] = None,
                                      phone_filter: Optional[str] = None,
                                      city_filter: Optional[str] = None,
                                      status_filter: Optional[str] = None,
                                      limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险代理人
        
        Args:
            name_filter: 姓名过滤条件
            code_filter: 代理人编码过滤条件
            phone_filter: 手机号过滤条件
            city_filter: 城市过滤条件
            status_filter: 状态过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 保险代理人列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if code_filter:
                conditions.append(f"n.code CONTAINS '{code_filter}'")
            
            if phone_filter:
                conditions.append(f"n.phone CONTAINS '{phone_filter}'")
            
            if city_filter:
                conditions.append(f"n.city_name CONTAINS '{city_filter}'")
            
            if status_filter:
                conditions.append(f"n.status = '{status_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:InsuranceAgentPerson)
            WHERE {where_clause}
            RETURN n, labels(n) as node_labels
            ORDER BY n.name
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            insurance_agent_persons = []
            for record in results:
                from app.utils.serializers import serialize_neo4j_record
                person_data = serialize_neo4j_record(record["n"])
                person_data["node_labels"] = record["node_labels"]
                insurance_agent_persons.append(person_data)
            
            return insurance_agent_persons
            
        except Exception as e:
            logger.error(f"搜索保险代理人失败: {str(e)}")
            return []
    
    def get_insurance_agent_person_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据编码获取单个保险代理人
        
        Args:
            code: 人员编码
            
        Returns:
            Optional[Dict[str, Any]]: 保险代理人详细信息
        """
        try:
            query = """
            MATCH (n:InsuranceAgentPerson)
            WHERE n.code = $code
            RETURN n, labels(n) as node_labels
            """
            
            results = self.neo4j_storage.execute_query(query, {"code": code})
            if results:
                from app.utils.serializers import serialize_neo4j_record
                person_data = serialize_neo4j_record(results[0]["n"])
                person_data["node_labels"] = results[0]["node_labels"]
                return person_data
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取保险代理人失败: {str(e)}")
            return None
    
    def delete_all_insurance_agent_persons(self) -> bool:
        """
        删除所有保险代理人数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = "MATCH (n:InsuranceAgentPerson) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保险代理人数据")
            return True
        except Exception as e:
            logger.error(f"删除保险代理人数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险代理人统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:InsuranceAgentPerson) RETURN count(n) as total_count"
            
            results = self.neo4j_storage.execute_query(total_query)
            if results:
                total_count = results[0]["total_count"]
            else:
                total_count = 0
            
            # 省份统计
            unique_province_query = "MATCH (n:InsuranceAgentPerson) RETURN count(DISTINCT n.province_name) as unique_province_count"
            
            results = self.neo4j_storage.execute_query(unique_province_query)
            if results:
                unique_province_count = results[0]["unique_province_count"]
            else:
                unique_province_count = 0
            
            # 城市统计
            unique_city_query = "MATCH (n:InsuranceAgentPerson) RETURN count(DISTINCT n.city_name) as unique_city_count"
            
            results = self.neo4j_storage.execute_query(unique_city_query)
            if results:
                unique_city_count = results[0]["unique_city_count"]
            else:
                unique_city_count = 0
            
            # 状态统计
            status_types_query = "MATCH (n:InsuranceAgentPerson) RETURN count(DISTINCT n.status) as status_types"
            
            results = self.neo4j_storage.execute_query(status_types_query)
            if results:
                status_types = results[0]["status_types"]
            else:
                status_types = 0
            
            # 最后更新时间
            last_updated_query = "MATCH (n:InsuranceAgentPerson) RETURN max(n.updated_at) as last_updated"
            
            results = self.neo4j_storage.execute_query(last_updated_query)
            if results:
                from app.utils.serializers import serialize_neo4j_value
                last_updated = serialize_neo4j_value(results[0]["last_updated"])
            else:
                last_updated = None
            
            return {
                "total_count": total_count,
                "unique_province_count": unique_province_count,
                "unique_city_count": unique_city_count,
                "status_types": status_types,
                "last_updated": last_updated
            }
                
        except Exception as e:
            logger.error(f"获取保险代理人统计信息失败: {str(e)}")
            return {
                "total_count": 0, 
                "unique_province_count": 0,
                "unique_city_count": 0,
                "status_types": 0,
                "last_updated": None
            } 