"""
保险理赔服务

负责保险理赔数据的提取、转换和存储
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.insurance_claims_transformer import InsuranceClaimsTransformer
from app.models.nodes.insurance_claims_node import InsuranceClaimsNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class InsuranceClaimsService(BasePaginationService):
    """保险理赔服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = InsuranceClaimsTransformer()
    
    def extract_and_store_claims(self) -> Dict[str, Any]:
        """
        分页提取并存储保险理赔数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("InsuranceClaims")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页保险理赔查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT fcd.case_no,                                            -- 报案号
               fcd.policy_code,                                        -- 保单号
               fcd.insured_id                 AS insured_user_id,      -- 被保险人员ID
               pct.insured_cert_no            as insured_user_cert_no, -- 被保险人员证件号码
               pct.insured_user_name,                                  -- 被保险人姓名
               pct.insure_code,                                        -- 投保公司代码
               pct.insure_name,                                        -- 投保公司名称
               pct.insured_code,                                       -- 被保公司代码
               pct.insured_name,                                       -- 被保公司名称
               tyc.unified_social_credit_code AS insurance_code,       -- 保险公司代码
               fcd.insurance_name,                                     -- 保险公司名称
               fcd.report_name,                                        -- 报案人姓名
               fcd.report_mobile,                                      -- 报案人电话
               fcd.report_date,                                        -- 报案时间
               fcd.risk_date,                                          -- 出险时间
               fcd.register_date,                                      -- 立案时间
               fcd.case_close_date,                                    -- 结案日期
               fcd.work_type_name,                                     -- 投保工种或运动项目
               fcd.work_type_level,                                    -- 工种或场馆等级
               area_province.city_code        AS risk_province_code,   --  出险省code
               fcd.risk_province,                                      -- 出险省
               area_city.city_code            AS risk_city_code,       --  出险市code
               fcd.risk_city,                                          -- 出险市
               area_county.city_code          AS risk_district_code,   -- 出险区code
               fcd.risk_county                AS risk_district,        -- 出险区
               fcd.risk_scene,                                         -- 出险场景
               fcd.injured_category,                                   -- 伤情类别
               fcd.disable_level,                                      -- 伤残等级
               fcd.visit_hospitals,                                    -- 就诊医院
               fcd.case_status,                                        -- 案件状态[已决、未决]
               fcd.compensate_result,                                  -- 案件结论
               fcd.register_amt,                                       -- 立案金额
               fcd.compensate_amt,                                     -- 赔付金额
               fcd.closed_injured_category,                            -- 结案伤情类型
               fcd.closed_disable_level,                               -- 结案伤残等级
               fcd.injured_part,                                       -- 受伤部位
               fcd.risk_scene_one,                                     -- 出险场景大类
               fcd.risk_scene_two,                                     -- 出险场景中类
               fcd.risk_scene_three,                                   -- 出险场景小类
               fcd.report_is_agent                                     -- 报案人是否代理
        FROM dwtdb.dwt_fnol_claim_detail fcd
                 INNER JOIN dwadb.dwa_insured_person_common_data_test pct
                            ON pct.policy_code = fcd.policy_code AND pct.insured_id = fcd.insured_id AND pct.day = '2025-06-11'
                 INNER JOIN dimdb.dim_tyc_company_base tyc ON fcd.insurance_name = tyc.system_matched_company_name
                 LEFT JOIN dwddb.dwd_b_policy_central pc ON fcd.policy_code = pc.policy_code
                 LEFT JOIN dimdb.dim_area_code area_province
                           ON fcd.risk_province = area_province.city_name AND area_province.city_level = '1'
                 LEFT JOIN dimdb.dim_area_code area_city
                           ON fcd.risk_city = area_city.city_name AND area_city.city_parent_code = area_province.city_code AND
                              area_city.city_level = '2'
                 LEFT JOIN dimdb.dim_area_code area_county
                           ON fcd.risk_county = area_county.city_name AND area_county.city_parent_code = area_city.city_code AND
                              area_county.city_level = '3'
        WHERE pc.product_code IN ('PP00000715', 'PP00000810', 'PP00000592', 'PP00000832')
          AND fcd.case_no IS NOT NULL
          AND fcd.case_no != ''
        ORDER BY fcd.case_no DESC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[InsuranceClaimsNode]:
        """
        转换理赔数据为图模型
        
        Args:
            df: 原始理赔数据
            
        Returns:
            List[InsuranceClaimsNode]: 转换后的理赔节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[InsuranceClaimsNode]) -> bool:
        """
        存储理赔数据到Neo4j
        
        Args:
            data: 要存储的理赔数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_claims(self, 
                     case_no_filter: Optional[str] = None,
                     limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索保险理赔
        
        Args:
            case_no_filter: 案件号过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 理赔列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if case_no_filter:
                conditions.append(f"n.case_no CONTAINS '{case_no_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:InsuranceClaims)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.case_no
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            return [record["n"] for record in results]
            
        except Exception as e:
            logger.error(f"搜索保险理赔失败: {str(e)}")
            return []
    
    def delete_all_claims(self) -> bool:
        """
        删除所有保险理赔数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = "MATCH (n:InsuranceClaims) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有保险理赔数据")
            return True
        except Exception as e:
            logger.error(f"删除保险理赔数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取保险理赔统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            query = """
            MATCH (n:InsuranceClaims)
            RETURN count(n) as total_count,
                   max(n.updated_at) as last_updated
            """
            
            results = self.neo4j_storage.execute_query(query)
            if results:
                return results[0]
            else:
                return {"total_count": 0, "last_updated": None}
                
        except Exception as e:
            logger.error(f"获取保险理赔统计信息失败: {str(e)}")
            return {"total_count": 0, "last_updated": None} 