"""
特殊名单服务

负责特殊名单数据的提取、转换和存储
"""

import pandas as pd
from datetime import date, timedelta
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.special_list_transformer import SpecialListTransformer
from app.models.nodes.special_list_node import SpecialListNode
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SpecialListService(BasePaginationService):
    """特殊名单服务"""
    
    def __init__(self):
        super().__init__()
        self.transformer = SpecialListTransformer()
    
    def extract_and_store_special_lists(self) -> Dict[str, Any]:
        """
        分页提取并存储特殊名单数据（处理全部数据直到结束）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("SpecialList")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页特殊名单查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        # 获取当前日期的前一天作为分区值
        yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        query = f"""
        SELECT 
            id, 
            list_type,
            list_category, 
            cert_no AS code, 
            name, 
            data_source, 
            data_source_type, 
            risk_level, 
            status, 
            effective_day, 
            reason, 
            remark
        FROM odsdb.dm_risk_list_dmdb
        WHERE day = '{yesterday}'
          AND list_category = 2
          AND list_type = 1
          AND id IS NOT NULL
          AND cert_no IS NOT NULL
          AND cert_no != ''
        ORDER BY id ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        logger.info(f"构建特殊名单查询SQL，使用分区日期: {yesterday}")
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[SpecialListNode]:
        """
        转换特殊名单数据为图模型
        
        Args:
            df: 原始特殊名单数据
            
        Returns:
            List[SpecialListNode]: 转换后的特殊名单节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[SpecialListNode]) -> bool:
        """
        存储特殊名单数据到Neo4j
        
        Args:
            data: 要存储的特殊名单数据
            
        Returns:
            bool: 是否成功
        """
        return self.neo4j_storage.store_nodes(data)
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def search_special_lists(self, 
                            name_filter: Optional[str] = None,
                            code_filter: Optional[str] = None,
                            risk_level_filter: Optional[str] = None,
                            limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索特殊名单
        
        Args:
            name_filter: 姓名过滤条件
            code_filter: 代码过滤条件
            risk_level_filter: 风险等级过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 特殊名单列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if code_filter:
                conditions.append(f"n.code CONTAINS '{code_filter}'")
            
            if risk_level_filter:
                conditions.append(f"n.risk_level = '{risk_level_filter}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:SpecialList)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.list_id
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            special_lists = []
            for record in results:
                node_data = dict(record['n'])
                special_lists.append(node_data)
            
            logger.info(f"搜索到 {len(special_lists)} 个特殊名单")
            return special_lists
            
        except Exception as e:
            logger.error(f"搜索特殊名单失败: {str(e)}")
            return []
    
    def delete_all_special_lists(self) -> bool:
        """
        删除所有特殊名单数据
        
        Returns:
            bool: 是否成功
        """
        try:
            query = "MATCH (n:SpecialList) DELETE n"
            self.neo4j_storage.execute_query(query)
            logger.info("已删除所有特殊名单数据")
            return True
            
        except Exception as e:
            logger.error(f"删除特殊名单数据失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取特殊名单统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:SpecialList) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按风险等级统计
            risk_level_query = """
            MATCH (n:SpecialList) 
            WHERE n.risk_level IS NOT NULL
            RETURN n.risk_level as risk_level, count(n) as count
            ORDER BY count DESC
            """
            risk_level_result = self.neo4j_storage.execute_query(risk_level_query)
            risk_level_stats = {record['risk_level']: record['count'] for record in risk_level_result}
            
            # 按状态统计
            status_query = """
            MATCH (n:SpecialList) 
            WHERE n.status IS NOT NULL
            RETURN n.status as status, count(n) as count
            ORDER BY count DESC
            """
            status_result = self.neo4j_storage.execute_query(status_query)
            status_stats = {record['status']: record['count'] for record in status_result}
            
            # 最后更新时间
            update_query = """
            MATCH (n:SpecialList) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            return {
                "total_count": total_count,
                "risk_level_stats": risk_level_stats,
                "status_stats": status_stats,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取特殊名单统计失败: {str(e)}")
            return {} 