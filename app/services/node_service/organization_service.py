"""
组织服务

提供统一的组织查询和管理功能，支持所有类型的组织（保险公司、保险经纪公司等）
"""

from typing import List, Dict, Any, Optional
from app.services.data_storage.neo4j_storage import Neo4jDataStorage
from app.utils.logger import get_logger
from app.utils.serializers import serialize_neo4j_record, serialize_neo4j_records, serialize_neo4j_value

logger = get_logger(__name__)


class OrganizationService:
    """组织服务 - 统一管理所有类型的组织"""
    
    def __init__(self):
        self.storage = Neo4jDataStorage()
    
    def search_all_organizations(self, 
                                name_filter: Optional[str] = None,
                                organization_type: Optional[str] = None,
                                limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索所有组织
        
        Args:
            name_filter: 名称过滤条件
            organization_type: 组织类型过滤（InsuranceCompanyNode、InsuranceBrokerageCompany等）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 组织列表
        """
        logger.info(f"搜索组织: name_filter={name_filter}, type={organization_type}, limit={limit}")
        
        # 构建查询条件
        where_conditions = ["1=1"]
        
        if name_filter:
            where_conditions.append("(n.name CONTAINS $name_filter OR n.short_name CONTAINS $name_filter)")
        
        # 构建标签条件
        label_condition = "Organization"
        if organization_type:
            if organization_type in ["InsuranceCompany", "InsuranceBrokerageCompany"]:
                label_condition = f"Organization:{organization_type}"
            else:
                logger.warning(f"不支持的组织类型: {organization_type}")
        
        query = f"""
            MATCH (n:{label_condition})
            WHERE {' AND '.join(where_conditions)}
            RETURN n, labels(n) as node_labels
            ORDER BY n.updated_at DESC
            LIMIT {limit}
        """
        
        params = {}
        if name_filter:
            params['name_filter'] = name_filter
        
        try:
            results = self.storage.execute_query(query, params)
            organizations = []
            
            for record in results:
                # 序列化Neo4j记录
                org_data = serialize_neo4j_record(dict(record['n']))
                org_data['node_labels'] = record['node_labels']
                organizations.append(org_data)
            
            logger.info(f"找到 {len(organizations)} 个组织")
            return organizations
            
        except Exception as e:
            logger.error(f"搜索组织失败: {str(e)}")
            return []
    
    def get_organization_by_id(self, business_id: str) -> Optional[Dict[str, Any]]:
        """
        根据业务ID获取组织信息
        
        Args:
            business_id: 业务ID（统一社会信用代码或机构名称）
            
        Returns:
            Optional[Dict[str, Any]]: 组织信息
        """
        logger.info(f"获取组织信息: business_id={business_id}")
        
        query = """
            MATCH (n:Organization)
            WHERE n.business_id = $business_id
            RETURN n, labels(n) as node_labels
        """
        
        try:
            results = self.storage.execute_query(query, {"business_id": business_id})
            
            if results:
                # 序列化Neo4j记录
                org_data = serialize_neo4j_record(dict(results[0]['n']))
                org_data['node_labels'] = results[0]['node_labels']
                logger.info(f"找到组织: {org_data.get('name', 'N/A')}")
                return org_data
            else:
                logger.info(f"未找到组织: {business_id}")
                return None
                
        except Exception as e:
            logger.error(f"获取组织信息失败: {str(e)}")
            return None
    
    def get_organizations_by_type(self, organization_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        根据类型获取组织列表
        
        Args:
            organization_type: 组织类型（InsuranceCompany、InsuranceBrokerageCompany）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 组织列表
        """
        logger.info(f"按类型获取组织: type={organization_type}, limit={limit}")
        
        if organization_type not in ["InsuranceCompany", "InsuranceBrokerageCompany"]:
            logger.error(f"不支持的组织类型: {organization_type}")
            return []
        
        query = f"""
            MATCH (n:Organization:{organization_type})
            RETURN n, labels(n) as node_labels
            ORDER BY n.updated_at DESC
            LIMIT {limit}
        """
        
        try:
            results = self.storage.execute_query(query)
            organizations = []
            
            for record in results:
                # 序列化Neo4j记录
                org_data = serialize_neo4j_record(dict(record['n']))
                org_data['node_labels'] = record['node_labels']
                organizations.append(org_data)
            
            logger.info(f"找到 {len(organizations)} 个{organization_type}")
            return organizations
            
        except Exception as e:
            logger.error(f"按类型获取组织失败: {str(e)}")
            return []
    
    def get_organization_statistics(self) -> Dict[str, Any]:
        """
        获取组织统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        logger.info("获取组织统计信息")
        
        query = """
            MATCH (n:Organization)
            WITH labels(n) as node_labels, count(*) as count
            RETURN node_labels, count
            ORDER BY count DESC
        """
        
        try:
            results = self.storage.execute_query(query)
            
            stats = {
                "total_organizations": 0,
                "by_type": {},
                "last_updated": None
            }
            
            for record in results:
                labels = record['node_labels']
                count = record['count']
                stats["total_organizations"] += count
                
                # 提取具体类型标签
                org_type = "Organization"
                for label in labels:
                    if label != "Organization":
                        org_type = label
                        break
                
                stats["by_type"][org_type] = count
            
            # 获取最后更新时间
            last_update_query = """
                MATCH (n:Organization) 
                WHERE n.updated_at IS NOT NULL 
                RETURN max(n.updated_at) as last_updated
            """
            
            last_update_results = self.storage.execute_query(last_update_query)
            if last_update_results and last_update_results[0]['last_updated']:
                # 序列化最后更新时间
                stats["last_updated"] = serialize_neo4j_value(last_update_results[0]['last_updated'])
            
            logger.info(f"组织统计: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"获取组织统计失败: {str(e)}")
            return {
                "total_organizations": 0,
                "by_type": {},
                "last_updated": None,
                "error": str(e)
            } 