"""
区域服务

负责区域节点数据的提取、转换和存储（仅节点操作）
"""

import pandas as pd
from typing import List, Dict, Any, Optional

from app.services.base_pagination_service import BasePaginationService
from app.services.data_transform.node_transformers.area_transformer import AreaTransformer
from app.models.nodes.area_node import AreaNode
from app.utils.logger import get_logger
from app.utils.serializers import serialize_neo4j_record, serialize_neo4j_records

logger = get_logger(__name__)


class AreaService(BasePaginationService):
    """区域服务（仅负责节点操作）"""
    
    def __init__(self):
        super().__init__()
        self.transformer = AreaTransformer()
    
    def extract_and_store_areas(self) -> Dict[str, Any]:
        """
        分页提取并存储区域节点数据（仅节点，不包含关系）
        
        Returns:
            Dict[str, Any]: 处理结果
        """
        return self.extract_and_store_data("Area")
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """
        构建分页区域查询SQL
        
        Args:
            limit: 数据限制
            offset: 偏移量
            
        Returns:
            str: SQL查询语句
        """
        query = f"""
        SELECT 
            city_code as code, 
            city_name as name, 
            city_parent_code, 
            city_level
        FROM dimdb.dim_area_code
        WHERE city_code IS NOT NULL 
        AND city_name IS NOT NULL
        ORDER BY city_code ASC
        LIMIT {limit} OFFSET {offset}
        """
        
        return query
    
    def _transform_data(self, df: pd.DataFrame) -> List[AreaNode]:
        """
        转换区域数据为图模型
        
        Args:
            df: 原始区域数据
            
        Returns:
            List[AreaNode]: 转换后的区域节点列表
        """
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[AreaNode]) -> bool:
        """
        存储区域节点到Neo4j（仅节点，UPSERT模式）
        
        Args:
            data: 要存储的区域数据
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始存储 {len(data)} 个区域节点")
            
            # 存储节点（Neo4j存储已经支持UPSERT）
            success = self.neo4j_storage.store_nodes(data)
            if not success:
                logger.error("存储区域节点失败")
                return False
            
            logger.info(f"成功存储 {len(data)} 个区域节点")
            return True
            
        except Exception as e:
            logger.error(f"存储区域数据失败: {str(e)}")
            return False
    
    def _parse_store_result(self, store_result: bool, total_count: int) -> tuple[int, int]:
        """
        解析存储结果
        
        Args:
            store_result: 存储方法返回的布尔值
            total_count: 总数量
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        if store_result:
            return total_count, 0
        else:
            return 0, total_count
    
    def get_area_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        根据编码获取单个区域
        
        Args:
            code: 区域编码
            
        Returns:
            Optional[Dict[str, Any]]: 区域信息，如果未找到返回None
        """
        try:
            query = """
            MATCH (n:Area)
            WHERE n.code = $code
            RETURN n
            LIMIT 1
            """
            
            results = self.neo4j_storage.execute_query(query, {'code': code})
            
            if results:
                # 序列化Neo4j记录
                node_data = serialize_neo4j_record(dict(results[0]['n']))
                return node_data
            
            return None
            
        except Exception as e:
            logger.error(f"根据编码查询区域失败 (code={code}): {str(e)}")
            return None
    
    def get_areas_by_codes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """
        根据编码列表批量获取区域
        
        Args:
            codes: 区域编码列表
            
        Returns:
            List[Dict[str, Any]]: 区域列表
        """
        try:
            if not codes:
                return []
            
            query = """
            MATCH (n:Area)
            WHERE n.code IN $codes
            RETURN n
            ORDER BY n.code
            """
            
            results = self.neo4j_storage.execute_query(query, {'codes': codes})
            
            areas = []
            for record in results:
                # 序列化Neo4j记录
                node_data = serialize_neo4j_record(dict(record['n']))
                areas.append(node_data)
            
            logger.info(f"批量查询到 {len(areas)} 个区域")
            return areas
            
        except Exception as e:
            logger.error(f"批量查询区域失败: {str(e)}")
            return []
    
    def search_areas(self, 
                    name_filter: Optional[str] = None,
                    level: Optional[int] = None,
                    parent_code: Optional[str] = None,
                    limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索区域
        
        Args:
            name_filter: 区域名称过滤条件
            level: 区域层级过滤条件
            parent_code: 父级编码过滤条件
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 区域列表
        """
        try:
            # 构建查询条件
            conditions = ["1=1"]
            
            if name_filter:
                conditions.append(f"n.name CONTAINS '{name_filter}'")
            
            if level is not None:
                conditions.append(f"n.level = {level}")
            
            if parent_code:
                conditions.append(f"n.parent_code = '{parent_code}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
            MATCH (n:Area)
            WHERE {where_clause}
            RETURN n
            ORDER BY n.level, n.code
            LIMIT {limit}
            """
            
            results = self.neo4j_storage.execute_query(query)
            
            areas = []
            for record in results:
                # 序列化Neo4j记录
                node_data = serialize_neo4j_record(dict(record['n']))
                areas.append(node_data)
            
            logger.info(f"搜索到 {len(areas)} 个区域")
            return areas
            
        except Exception as e:
            logger.error(f"搜索区域失败: {str(e)}")
            return []
    
    def delete_all_areas(self) -> bool:
        """
        删除所有区域数据（仅节点，关系应由关系服务处理）
        
        Returns:
            bool: 是否成功
        """
        try:
            # 删除节点（关系会自动删除）
            node_query = "MATCH (n:Area) DELETE n"
            self.neo4j_storage.execute_query(node_query)
            logger.info("已删除所有区域节点")
            return True
            
        except Exception as e:
            logger.error(f"删除区域节点失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取区域统计信息（仅节点统计）
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 总数统计
            total_query = "MATCH (n:Area) RETURN count(n) as total_count"
            total_result = self.neo4j_storage.execute_query(total_query)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 按层级统计
            level_query = """
            MATCH (n:Area) 
            WHERE n.level IS NOT NULL
            RETURN n.level as level, count(n) as count
            ORDER BY level
            """
            level_result = self.neo4j_storage.execute_query(level_query)
            level_stats = {record['level']: record['count'] for record in level_result}
            
            # 层级总数统计
            unique_levels_query = """
            MATCH (n:Area) 
            WHERE n.level IS NOT NULL
            RETURN count(DISTINCT n.level) as unique_levels
            """
            unique_levels_result = self.neo4j_storage.execute_query(unique_levels_query)
            unique_levels = unique_levels_result[0]['unique_levels'] if unique_levels_result else 0
            
            # 最后更新时间
            update_query = """
            MATCH (n:Area) 
            RETURN max(n.updated_at) as last_updated
            """
            update_result = self.neo4j_storage.execute_query(update_query)
            last_updated = update_result[0]['last_updated'] if update_result and update_result[0]['last_updated'] else None
            
            # 序列化最后更新时间
            if last_updated:
                from app.utils.serializers import serialize_neo4j_value
                last_updated = serialize_neo4j_value(last_updated)
            
            return {
                "total_count": total_count,
                "by_level": level_stats,
                "unique_levels": unique_levels,
                "last_updated": last_updated
            }
            
        except Exception as e:
            logger.error(f"获取区域统计失败: {str(e)}")
            return {} 