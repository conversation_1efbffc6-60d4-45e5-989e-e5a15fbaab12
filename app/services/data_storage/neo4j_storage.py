"""
Neo4j数据存储服务
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from typing import Union
from app.models.base.base_model import BaseNode, BaseRelationship
from dataclasses import dataclass, field
from app.config import Neo4jConfig
from app.utils.logger import get_logger
from .neo4j_client import Neo4jClient

logger = get_logger(__name__)


@dataclass
class GraphEntity:
    """图实体包装器，包含节点和关系"""
    
    nodes: List[BaseNode] = field(default_factory=list)
    relationships: List[BaseRelationship] = field(default_factory=list)
    
    def add_node(self, node: BaseNode) -> None:
        """添加节点"""
        self.nodes.append(node)
    
    def add_relationship(self, relationship: BaseRelationship) -> None:
        """添加关系"""
        self.relationships.append(relationship)
    
    def get_nodes_by_label(self, label: str) -> List[BaseNode]:
        """根据标签获取节点"""
        return [node for node in self.nodes if node.get_label() == label]
    
    def get_relationships_by_type(self, rel_type: str) -> List[BaseRelationship]:
        """根据类型获取关系"""
        return [rel for rel in self.relationships if rel.get_type() == rel_type]


class Neo4jDataStorage:
    """Neo4j数据存储服务"""
    
    def __init__(self, config: Optional[Neo4jConfig] = None):
        """
        初始化数据存储服务
        
        Args:
            config: Neo4j配置
        """
        self.client = Neo4jClient(config)
        self.batch_size = 1000  # 批处理大小
    
    def initialize_database(self) -> bool:
        """
        初始化数据库（创建索引等）
        
        Returns:
            bool: 是否成功
        """
        try:
            if not self.client.connect():
                return False
            
            # 创建索引
            success = self.client.create_indexes()
            
            if success:
                logger.info("数据库初始化完成")
            else:
                logger.error("数据库初始化失败")
            
            return success
            
        except Exception as e:
            logger.error(f"数据库初始化异常: {str(e)}")
            return False
    
    def store_graph_entity(self, entity: GraphEntity) -> bool:
        """
        存储图实体（节点和关系）
        
        Args:
            entity: 图实体
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始存储图实体: 节点 {len(entity.nodes)} 个, 关系 {len(entity.relationships)} 个")
            
            # 先存储所有节点
            if entity.nodes:
                success = self.store_nodes(entity.nodes)
                if not success:
                    logger.error("存储节点失败")
                    return False
            
            # 再存储所有关系
            if entity.relationships:
                success = self.store_relationships(entity.relationships)
                if not success:
                    logger.error("存储关系失败")
                    return False
            
            logger.info("图实体存储完成")
            return True
            
        except Exception as e:
            logger.error(f"存储图实体失败: {str(e)}")
            return False
    
    def store_nodes(self, nodes: List[BaseNode]) -> bool:
        """
        批量存储节点
        
        Args:
            nodes: 节点列表
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始批量存储 {len(nodes)} 个节点")
            
            # 按类型分组节点
            nodes_by_type = {}
            for node in nodes:
                node_type = node.get_label()
                if node_type not in nodes_by_type:
                    nodes_by_type[node_type] = []
                nodes_by_type[node_type].append(node)
            
            # 按类型批量存储
            for node_type, type_nodes in nodes_by_type.items():
                success = self._store_nodes_by_type(node_type, type_nodes)
                if not success:
                    logger.error(f"存储 {node_type} 类型节点失败")
                    return False
            
            logger.info("节点批量存储完成")
            return True
            
        except Exception as e:
            logger.error(f"批量存储节点失败: {str(e)}")
            return False
    
    def _store_nodes_by_type(self, node_type: str, nodes: List[BaseNode]) -> bool:
        """
        按类型批量存储节点
        
        Args:
            node_type: 节点类型（主标签）
            nodes: 节点列表
            
        Returns:
            bool: 是否成功
        """
        try:
            # 分批处理
            for i in range(0, len(nodes), self.batch_size):
                batch = nodes[i:i + self.batch_size]
                
                # 构建批量插入查询
                queries = []
                for node in batch:
                    properties = node.to_dict()
                    # 确保有 business_id
                    if not properties.get('business_id'):
                        properties['business_id'] = node.business_id or node.get_unique_key()
                    
                    # 构建标签字符串 - 支持多标签
                    if hasattr(node, 'labels') and node.labels:
                        # 使用节点的完整标签列表
                        labels_str = ":".join(node.labels)
                    else:
                        # 回退到主标签
                        labels_str = node_type
                    
                    # 使用 business_id 作为唯一键，支持多标签
                    query = f"""
                    MERGE (n:{labels_str} {{business_id: $business_id}})
                    ON CREATE SET n = $properties, n.created_at = datetime()
                    ON MATCH SET n += $properties, n.updated_at = datetime()
                    """
                    
                    logger.debug(f"构建的查询: {query}")
                    
                    parameters = {
                        'business_id': properties['business_id'],
                        'properties': properties
                    }
                    
                    queries.append((query, parameters))
                
                # 执行批量事务
                success = self.client.execute_batch_transaction(queries)
                if not success:
                    logger.error(f"批次存储失败: {node_type}, 批次 {i//self.batch_size + 1}")
                    return False
                
                logger.debug(f"{node_type} 批次 {i//self.batch_size + 1} 存储完成，{len(batch)} 个节点")
            
            logger.info(f"{node_type} 类型节点存储完成，共 {len(nodes)} 个")
            return True
            
        except Exception as e:
            logger.error(f"按类型存储节点失败: {str(e)}")
            return False
    
    def store_relationships(self, relationships: List[BaseRelationship]) -> bool:
        """
        批量存储关系
        
        Args:
            relationships: 关系列表
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始批量存储 {len(relationships)} 个关系")
            
            # 按类型分组关系
            rels_by_type = {}
            for rel in relationships:
                rel_type = rel.get_type()
                if rel_type not in rels_by_type:
                    rels_by_type[rel_type] = []
                rels_by_type[rel_type].append(rel)
            
            # 按类型批量存储
            for rel_type, type_rels in rels_by_type.items():
                success = self._store_relationships_by_type(rel_type, type_rels)
                if not success:
                    logger.error(f"存储 {rel_type} 类型关系失败")
                    return False
            
            logger.info("关系批量存储完成")
            return True
            
        except Exception as e:
            logger.error(f"批量存储关系失败: {str(e)}")
            return False
    
    def _store_relationships_by_type(self, rel_type: str, relationships: List[BaseRelationship]) -> bool:
        """
        按类型批量存储关系
        
        Args:
            rel_type: 关系类型
            relationships: 关系列表
            
        Returns:
            bool: 是否成功
        """
        try:
            # 分批处理
            for i in range(0, len(relationships), self.batch_size):
                batch = relationships[i:i + self.batch_size]
                
                # 构建批量插入查询
                queries = []
                for rel in batch:
                    if not rel.start_business_id or not rel.end_business_id:
                        logger.warning(f"关系缺少业务ID: {rel}")
                        continue
                    
                    properties = rel.to_dict()
                    
                    query = f"""
                    MATCH (start_node), (end_node)
                    WHERE start_node.business_id = $start_business_id 
                      AND end_node.business_id = $end_business_id
                    MERGE (start_node)-[r:{rel_type}]->(end_node)
                    ON CREATE SET r = $properties, r.created_at = datetime()
                    ON MATCH SET r += $properties, r.updated_at = datetime()
                    """
                    
                    parameters = {
                        'start_business_id': rel.start_business_id,
                        'end_business_id': rel.end_business_id,
                        'properties': properties
                    }
                    
                    queries.append((query, parameters))
                
                if queries:  # 只有在有有效查询时才执行
                    success = self.client.execute_batch_transaction(queries)
                    if not success:
                        logger.error(f"批次存储失败: {rel_type}, 批次 {i//self.batch_size + 1}")
                        return False
                    
                    logger.debug(f"{rel_type} 批次 {i//self.batch_size + 1} 存储完成，{len(queries)} 个关系")
            
            logger.info(f"{rel_type} 类型关系存储完成，共 {len(relationships)} 个")
            return True
            
        except Exception as e:
            logger.error(f"按类型存储关系失败: {str(e)}")
            return False
    
    #         
    #         # 存储关系
    #         all_relations = []
    #         if policy_holder_relations:
    #             all_relations.extend(policy_holder_relations)
    #         if insured_relations:
    #             all_relations.extend(insured_relations)
    #         
    #         if all_relations:
    #             success = self.store_relationships(all_relations)
    #             if not success:
    #                 return False
    #         
    #         logger.info("保单相关数据存储完成")
    #         return True
    #         
    #     except Exception as e:
    #         logger.error(f"存储保单相关数据失败: {str(e)}")
    #         return False
    #     
    #         
    #         # 存储理赔关系
    #         if claim_relations:
    #             success = self.store_relationships(claim_relations)
    #             if not success:
    #                 return False
    #         
    #         logger.info("理赔相关数据存储完成")
    #         return True
    #         
    #     except Exception as e:
    #         logger.error(f"存储理赔相关数据失败: {str(e)}")
    #         return False
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self.client.driver:
                self.client.connect()
            
            stats = self.client.get_database_statistics()
            
            logger.info(f"数据库统计: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"获取存储统计失败: {str(e)}")
            return {}
    
    def clear_all_data(self) -> bool:
        """
        清空所有数据（谨慎使用）
        
        Returns:
            bool: 是否成功
        """
        try:
            logger.warning("即将清空所有数据")
            
            if not self.client.driver:
                self.client.connect()
            
            success = self.client.clear_database()
            
            if success:
                logger.warning("所有数据已清空")
            else:
                logger.error("清空数据失败")
            
            return success
            
        except Exception as e:
            logger.error(f"清空数据异常: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            return self.client.connect()
            
        except Exception as e:
            logger.error(f"连接测试失败: {str(e)}")
            return False
        finally:
            self.client.disconnect()
    
    def close(self):
        """关闭连接"""
        self.client.disconnect()
    
    def __enter__(self):
        """上下文管理器入口"""
        self.client.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.client.disconnect()
    
    def batch_create_nodes(self, nodes: List[BaseNode]) -> int:
        """
        批量创建节点
        
        Args:
            nodes: 节点列表
            
        Returns:
            int: 成功创建的节点数量
        """
        try:
            if not nodes:
                return 0
            
            success = self.store_nodes(nodes)
            if success:
                return len(nodes)
            else:
                return 0
                
        except Exception as e:
            logger.error(f"批量创建节点失败: {str(e)}")
            return 0
    
    def get_node_statistics(self, node_label: str) -> Dict[str, Any]:
        """
        获取指定标签节点的统计信息
        
        Args:
            node_label: 节点标签
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self.client.driver:
                self.client.connect()
            
            # 获取节点总数
            count_query = f"MATCH (n:{node_label}) RETURN count(n) as total_count"
            result = self.client.execute_query(count_query)
            total_count = result[0]['total_count'] if result else 0
            
            # 获取最新更新时间
            latest_query = f"""
            MATCH (n:{node_label}) 
            WHERE n.updated_at IS NOT NULL 
            RETURN max(n.updated_at) as last_updated
            """
            result = self.client.execute_query(latest_query)
            last_updated = result[0]['last_updated'] if result and result[0]['last_updated'] else None
            
            stats = {
                "total_count": total_count,
                "last_updated": last_updated
            }
            
            logger.info(f"{node_label} 节点统计: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"获取节点统计失败: {str(e)}")
            return {}
    
    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行Cypher查询
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        try:
            if not self.client.driver:
                self.client.connect()
            
            return self.client.execute_query(query, parameters)
            
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            return [] 