"""
Neo4j数据库客户端
"""

from typing import Dict, Any, List, Optional, Union
import time

from neo4j import GraphDatabase, Driver, Session, Transaction
from neo4j.exceptions import ServiceUnavailable, TransientError

from app.config import Neo4jConfig, load_neo4j_config
from app.utils.logger import get_logger

logger = get_logger(__name__)


class Neo4jClient:
    """Neo4j数据库客户端"""
    
    def __init__(self, config: Optional[Neo4jConfig] = None):
        """
        初始化Neo4j客户端
        
        Args:
            config: Neo4j配置，如果不提供将从环境变量加载
        """
        self.config = config if config else load_neo4j_config()
        self.driver: Optional[Driver] = None
        self._session: Optional[Session] = None
    
    def connect(self) -> bool:
        """
        建立与Neo4j数据库的连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 添加连接池配置，防止连接耗尽和假死
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                max_connection_lifetime=3 * 60,   # 3分钟连接生命周期，更频繁回收
                max_connection_pool_size=100,     # 增大连接池到100个连接
                connection_acquisition_timeout=5,   # 5秒获取连接超时，快速失败
                connection_timeout=3,            # 3秒连接超时，快速失败
                keep_alive=True                  # 保持连接活跃
            )
            
            # 测试连接，带重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    with self.driver.session(database=self.config.database) as session:
                        result = session.run("RETURN 1 AS test")
                        test_value = result.single()["test"]
                    break
                except (ServiceUnavailable, TransientError) as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"连接测试失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    time.sleep(2 ** attempt)  # 指数退避
                
            logger.info(f"成功连接到 Neo4j 数据库: {self.config.uri}")
            return True
            
        except Exception as e:
            logger.error(f"连接 Neo4j 数据库失败: {str(e)}")
            if self.driver:
                self.driver.close()
                self.driver = None
            return False
    
    def disconnect(self):
        """关闭与Neo4j数据库的连接"""
        try:
            if self._session:
                self._session.close()
                self._session = None
                
            if self.driver:
                self.driver.close()
                self.driver = None
                logger.info("已关闭 Neo4j 数据库连接")
        except Exception as e:
            logger.warning(f"关闭连接时出现错误: {str(e)}")
    
    def get_session(self) -> Session:
        """
        获取独立数据库会话（已废弃，建议直接使用driver.session()）
        
        Returns:
            Session: Neo4j会话对象
        """
        if not self.driver:
            if not self.connect():
                raise RuntimeError("无法连接到Neo4j数据库")
        
        # 总是返回新的会话，不复用
        return self.driver.session(database=self.config.database)
    
    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行Cypher查询，使用独立会话确保并发安全
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        if not parameters:
            parameters = {}
        
        # 确保驱动连接可用
        if not self.driver:
            if not self.connect():
                raise RuntimeError("无法连接到Neo4j数据库")
        
        try:
            # 每次查询都使用新的独立会话，确保并发安全
            with self.driver.session(database=self.config.database) as session:
                logger.debug(f"执行查询: {query[:50]}...")  # 只记录前50字符
                
                # 执行查询，立即消费结果
                result = session.run(query, parameters)
                records = []
                
                # 立即提取所有结果，不要延迟消费
                for record in result:
                    records.append(record.data())
                    # 对于大结果集，设置合理限制
                    if len(records) >= 10000:  # 防止内存溢出
                        logger.warning(f"查询结果过大，已截断到 {len(records)} 条记录")
                        break
                
                logger.debug(f"查询完成，返回 {len(records)} 条记录")
                return records
                
        except (ServiceUnavailable, TransientError) as e:
            logger.error(f"Neo4j服务不可用: {str(e)}")
            # 服务不可用时，尝试重新连接
            self.disconnect()
            return []
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            return []
    
    def execute_write_transaction(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行写事务
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        if not parameters:
            parameters = {}
        
        def _write_transaction(tx: Transaction) -> Dict[str, Any]:
            result = tx.run(query, parameters)
            summary = result.consume()
            
            return {
                "records_available": summary.result_available_after,
                "records_consumed": summary.result_consumed_after,
                "nodes_created": summary.counters.nodes_created,
                "nodes_deleted": summary.counters.nodes_deleted,
                "relationships_created": summary.counters.relationships_created,
                "relationships_deleted": summary.counters.relationships_deleted,
                "properties_set": summary.counters.properties_set,
                "labels_added": summary.counters.labels_added,
                "labels_removed": summary.counters.labels_removed,
                "indexes_added": summary.counters.indexes_added,
                "indexes_removed": summary.counters.indexes_removed,
                "constraints_added": summary.counters.constraints_added,
                "constraints_removed": summary.counters.constraints_removed
            }
        
        try:
            if not self.driver:
                if not self.connect():
                    raise RuntimeError("无法连接到Neo4j数据库")
            
            with self.driver.session(database=self.config.database) as session:
                logger.debug(f"执行写事务: {query}")
                logger.debug(f"查询参数: {parameters}")
                
                result = session.execute_write(_write_transaction)
                
                logger.info(f"写事务完成: {result}")
                return result
                
        except Exception as e:
            logger.error(f"执行写事务失败: {str(e)}")
            return {}
    
    def execute_batch_transaction(self, queries: List[tuple]) -> bool:
        """
        执行批量事务
        
        Args:
            queries: 查询列表，每项为 (query, parameters) 元组
            
        Returns:
            bool: 是否成功
        """
        def _batch_transaction(tx: Transaction) -> bool:
            try:
                for query, parameters in queries:
                    tx.run(query, parameters or {})
                return True
            except Exception as e:
                logger.error(f"批量事务中的查询失败: {str(e)}")
                raise
        
        try:
            if not self.driver:
                if not self.connect():
                    raise RuntimeError("无法连接到Neo4j数据库")
            
            with self.driver.session(database=self.config.database) as session:
                logger.info(f"开始执行批量事务，共 {len(queries)} 个查询")
                
                result = session.execute_write(_batch_transaction)
                
                logger.info("批量事务执行成功")
                return result
                
        except Exception as e:
            logger.error(f"批量事务执行失败: {str(e)}")
            return False
    
    def create_node(self, label: str, properties: Dict[str, Any], unique_key: str = None) -> Optional[int]:
        """
        创建节点
        
        Args:
            label: 节点标签
            properties: 节点属性
            unique_key: 唯一键属性名
            
        Returns:
            Optional[int]: 节点ID，如果失败返回None
        """
        try:
            if unique_key and unique_key in properties:
                # 使用 MERGE 避免重复创建
                query = f"""
                MERGE (n:{label} {{{unique_key}: $unique_value}})
                ON CREATE SET n = $properties
                ON MATCH SET n += $properties
                RETURN elementId(n) AS node_id
                """
                parameters = {
                    "unique_value": properties[unique_key],
                    "properties": properties
                }
            else:
                # 直接创建节点
                query = f"""
                CREATE (n:{label})
                SET n = $properties
                RETURN elementId(n) AS node_id
                """
                parameters = {"properties": properties}
            
            with self.get_session() as session:
                result = session.run(query, parameters)
                record = result.single()
                
                if record:
                    node_id = record["node_id"]
                    logger.debug(f"创建节点成功: {label}, ID: {node_id}")
                    return node_id
                
                return None
                
        except Exception as e:
            logger.error(f"创建节点失败: {str(e)}")
            return None
    
    def create_relationship(self, start_node_key: str, start_node_value: Any,
                          end_node_key: str, end_node_value: Any,
                          relationship_type: str, properties: Dict[str, Any] = None) -> bool:
        """
        创建关系
        
        Args:
            start_node_key: 起始节点查找键
            start_node_value: 起始节点查找值
            end_node_key: 结束节点查找键
            end_node_value: 结束节点查找值
            relationship_type: 关系类型
            properties: 关系属性
            
        Returns:
            bool: 是否成功
        """
        try:
            if not properties:
                properties = {}
            
            query = f"""
            MATCH (start_node), (end_node)
            WHERE start_node.{start_node_key} = $start_value 
              AND end_node.{end_node_key} = $end_value
            MERGE (start_node)-[r:{relationship_type}]->(end_node)
            ON CREATE SET r = $properties
            ON MATCH SET r += $properties
            RETURN elementId(r) AS rel_id
            """
            
            parameters = {
                "start_value": start_node_value,
                "end_value": end_node_value,
                "properties": properties
            }
            
            with self.get_session() as session:
                result = session.run(query, parameters)
                record = result.single()
                
                if record:
                    logger.debug(f"创建关系成功: {relationship_type}")
                    return True
                else:
                    logger.warning(f"未找到匹配的节点创建关系: {start_node_key}={start_node_value}, {end_node_key}={end_node_value}")
                    return False
                
        except Exception as e:
            logger.error(f"创建关系失败: {str(e)}")
            return False
    
    def create_indexes(self) -> bool:
        """
        创建推荐的索引
        
        Returns:
            bool: 是否成功
        """
        indexes = [
            "CREATE INDEX policy_business_id IF NOT EXISTS FOR (n:PolicyNode) ON (n.business_id)",
            "CREATE INDEX company_business_id IF NOT EXISTS FOR (n:Company) ON (n.business_id)",
            "CREATE INDEX person_business_id IF NOT EXISTS FOR (n:PersonNode) ON (n.business_id)",
            "CREATE INDEX claim_business_id IF NOT EXISTS FOR (n:Claim) ON (n.business_id)",
            "CREATE INDEX policy_no IF NOT EXISTS FOR (n:PolicyNode) ON (n.policy_no)",
            "CREATE INDEX company_code IF NOT EXISTS FOR (n:Company) ON (n.company_code)",
            "CREATE INDEX credit_code IF NOT EXISTS FOR (n:Company) ON (n.credit_code)",
            "CREATE INDEX claim_no IF NOT EXISTS FOR (n:Claim) ON (n.claim_no)"
        ]
        
        try:
            if not self.driver:
                if not self.connect():
                    raise RuntimeError("无法连接到Neo4j数据库")
            
            with self.driver.session(database=self.config.database) as session:
                for index_query in indexes:
                    try:
                        session.run(index_query)
                        logger.debug(f"创建索引: {index_query}")
                    except Exception as e:
                        logger.warning(f"创建索引失败: {index_query}, 错误: {str(e)}")
            
            logger.info("索引创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            return False
    
    def clear_database(self) -> bool:
        """
        清空数据库（谨慎使用）
        
        Returns:
            bool: 是否成功
        """
        try:
            if not self.driver:
                if not self.connect():
                    raise RuntimeError("无法连接到Neo4j数据库")
            
            with self.driver.session(database=self.config.database) as session:
                # 删除所有关系
                session.run("MATCH ()-[r]-() DELETE r")
                # 删除所有节点
                session.run("MATCH (n) DELETE n")
                
            logger.warning("数据库已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空数据库失败: {str(e)}")
            return False
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self.driver:
                if not self.connect():
                    raise RuntimeError("无法连接到Neo4j数据库")
            
            with self.driver.session(database=self.config.database) as session:
                # 节点统计
                node_result = session.run("""
                MATCH (n)
                RETURN labels(n) AS labels, count(n) AS count
                """)
                
                node_stats = {}
                for record in node_result:
                    labels = record["labels"]
                    count = record["count"]
                    if labels:
                        label = labels[0]  # 使用第一个标签
                        node_stats[label] = count
                
                # 关系统计
                rel_result = session.run("""
                MATCH ()-[r]->()
                RETURN type(r) AS type, count(r) AS count
                """)
                
                rel_stats = {}
                for record in rel_result:
                    rel_type = record["type"]
                    count = record["count"]
                    rel_stats[rel_type] = count
                
                return {
                    "nodes": node_stats,
                    "relationships": rel_stats,
                    "total_nodes": sum(node_stats.values()),
                    "total_relationships": sum(rel_stats.values())
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {str(e)}")
            return {}
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
    
    @classmethod
    def from_config(cls, config: Neo4jConfig):
        """从配置创建Neo4jClient实例"""
        return cls(config) 