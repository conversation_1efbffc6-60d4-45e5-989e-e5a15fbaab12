"""
Neo4j 简单客户端 - 使用连接池优化
使用单例模式的驱动，配置合理的连接池参数，避免连接泄露
"""

from typing import Dict, Any, List, Optional
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, TransientError

from app.config.neo4j_config import Neo4jConfig, load_neo4j_config
from app.utils.logger import get_logger

logger = get_logger(__name__)


class Neo4jSimpleClient:
    """
    Neo4j 简单客户端
    使用单例驱动和连接池，优化连接管理
    """

    def __init__(self, config: Optional[Neo4jConfig] = None):
        """
        初始化Neo4j简单客户端

        Args:
            config: Neo4j配置对象，如果为None则使用默认配置
        """
        self.config = config or load_neo4j_config()
        self._driver = None
        logger.info(f"初始化 Neo4j 简单客户端: {self.config.uri}")

    def _get_driver(self):
        """
        获取Neo4j驱动（单例模式）

        Returns:
            Neo4j驱动对象
        """
        if self._driver is None:
            self._driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                # 优化连接池配置
                max_connection_lifetime=30 * 60,      # 30分钟连接生命周期
                max_connection_pool_size=50,          # 最大50个连接
                connection_acquisition_timeout=10,    # 10秒获取连接超时
                connection_timeout=10,                # 10秒连接超时
                keep_alive=True,                      # 保持连接活跃
                # 修复连接验证配置
                encrypted=False
            )
        return self._driver

    def close(self):
        """
        关闭驱动和连接池
        """
        if self._driver:
            try:
                self._driver.close()
                self._driver = None
                logger.info("Neo4j 驱动已关闭")
            except Exception as e:
                logger.error(f"关闭 Neo4j 驱动失败: {str(e)}")

    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行Cypher查询，使用连接池

        Args:
            query: Cypher查询语句
            parameters: 查询参数

        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        if not parameters:
            parameters = {}

        session = None

        try:
            # 使用连接池中的驱动
            driver = self._get_driver()
            session = driver.session(database=self.config.database)

            logger.debug(f"执行查询: {query[:50]}...")

            # 执行查询并立即获取所有结果
            result = session.run(query, parameters)
            records = []

            for record in result:
                # 使用自定义方法保留关系属性
                record_data = self._extract_record_data(record)
                records.append(record_data)
                # 防止结果过大
                if len(records) >= 10000:
                    logger.warning(f"查询结果过大，已截断到 {len(records)} 条记录")
                    break

            logger.debug(f"查询完成，返回 {len(records)} 条记录")
            return records

        except (ServiceUnavailable, TransientError) as e:
            logger.error(f"Neo4j服务不可用: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            return []
        finally:
            # 只关闭session，不关闭driver（连接池管理）
            if session:
                try:
                    session.close()
                except:
                    pass

    def _extract_record_data(self, record) -> Dict[str, Any]:
        """
        提取记录数据，保留关系对象的完整属性信息

        Args:
            record: Neo4j记录对象

        Returns:
            Dict[str, Any]: 提取的记录数据
        """
        data = {}

        for key in record.keys():
            value = record[key]

            # 检查是否为关系对象
            if hasattr(value, 'type') and hasattr(value, 'start_node') and hasattr(value, 'end_node'):
                # 这是一个Neo4j关系对象，保留完整信息
                start_node_data = dict(value.start_node) if hasattr(value.start_node, '__iter__') else {}
                end_node_data = dict(value.end_node) if hasattr(value.end_node, '__iter__') else {}

                # 提取关系属性
                rel_properties = {}
                if hasattr(value, '_properties'):
                    rel_properties = dict(value._properties)
                elif hasattr(value, '__dict__'):
                    # 过滤掉内部属性
                    rel_properties = {k: v for k, v in value.__dict__.items()
                                    if not k.startswith('_') and k not in ['type', 'start_node', 'end_node', 'id', 'element_id']}

                # 创建包含完整关系信息的tuple，但将关系对象本身放在中间
                data[key] = (start_node_data, value, end_node_data)
                logger.debug(f"保留关系对象 {key}: type={value.type}, properties={rel_properties}")

            # 检查是否为节点对象
            elif hasattr(value, 'labels') or (hasattr(value, '__iter__') and not isinstance(value, str)):
                try:
                    data[key] = dict(value)
                except:
                    data[key] = value

            # 普通值
            else:
                data[key] = value

        return data

    def execute_write_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行写查询（CREATE, UPDATE, DELETE等）

        Args:
            query: Cypher写查询语句
            parameters: 查询参数

        Returns:
            Dict[str, Any]: 执行结果统计
        """
        if not parameters:
            parameters = {}

        session = None

        try:
            # 使用连接池中的驱动
            driver = self._get_driver()
            session = driver.session(database=self.config.database)

            logger.debug(f"执行写查询: {query[:50]}...")

            # 执行写查询
            result = session.run(query, parameters)
            summary = result.consume()

            # 构建结果统计
            stats = {
                "nodes_created": summary.counters.nodes_created,
                "nodes_deleted": summary.counters.nodes_deleted,
                "relationships_created": summary.counters.relationships_created,
                "relationships_deleted": summary.counters.relationships_deleted,
                "properties_set": summary.counters.properties_set,
                "labels_added": summary.counters.labels_added,
                "labels_removed": summary.counters.labels_removed
            }

            logger.debug(f"写查询完成: {stats}")
            return stats

        except (ServiceUnavailable, TransientError) as e:
            logger.error(f"Neo4j服务不可用: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"执行写查询失败: {str(e)}")
            return {}
        finally:
            # 只关闭session，不关闭driver（连接池管理）
            if session:
                try:
                    session.close()
                except:
                    pass
    
    def execute_transaction(self, queries: List[tuple]) -> bool:
        """
        执行事务（多个查询要么全部成功，要么全部失败）

        Args:
            queries: 查询列表，每项为 (query, parameters) 元组

        Returns:
            bool: 是否成功
        """
        session = None

        try:
            # 使用连接池中的驱动
            driver = self._get_driver()
            session = driver.session(database=self.config.database)

            logger.debug(f"开始执行事务，共 {len(queries)} 个查询")

            # 使用事务执行所有查询
            def _transaction_work(tx):
                for query, parameters in queries:
                    tx.run(query, parameters or {})

            session.execute_write(_transaction_work)

            logger.debug("事务执行成功")
            return True

        except Exception as e:
            logger.error(f"事务执行失败: {str(e)}")
            return False
        finally:
            # 只关闭session，不关闭driver（连接池管理）
            if session:
                try:
                    session.close()
                except:
                    pass
    
    def test_connection(self) -> bool:
        """
        测试数据库连接

        Returns:
            bool: 连接是否成功
        """
        session = None

        try:
            driver = self._get_driver()
            session = driver.session(database=self.config.database)

            # 执行简单测试查询
            result = session.run("RETURN 1 AS test")
            test_value = result.single()["test"]

            if test_value == 1:
                logger.info(f"成功连接到 Neo4j 数据库: {self.config.uri}")
                return True
            else:
                logger.error("连接测试失败")
                return False

        except Exception as e:
            logger.error(f"连接测试失败: {str(e)}")
            return False
        finally:
            # 只关闭session，不关闭driver（连接池管理）
            if session:
                try:
                    session.close()
                except:
                    pass
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库基本信息
        
        Returns:
            Dict[str, Any]: 数据库信息
        """
        # 简化版本，不依赖APOC，直接使用基本统计
        
        try:
            # 简化版本，不依赖APOC
            simple_stats = self.execute_query("""
            MATCH (n) 
            RETURN count(n) as total_nodes
            UNION ALL
            MATCH ()-[r]-() 
            RETURN count(r) as total_relationships
            """)
            
            info = {
                "database": self.config.database,
                "uri": self.config.uri,
                "stats": simple_stats
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据库信息失败: {str(e)}")
            return {"error": str(e)}


# 创建全局实例
neo4j_simple_client = Neo4jSimpleClient() 