"""
控制器基础类

统一处理异常、响应格式、日志记录等重复逻辑
"""

import functools
from typing import Any, Dict, Optional, Callable, Union, List
from fastapi import HTTPException
from datetime import datetime
import inspect

from app.utils.logger import get_logger


class ApiResponse:
    """标准化API响应构建器"""
    
    @staticmethod
    def success(
        data: Any = None, 
        message: str = "操作成功",
        total: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """构建成功响应"""
        response = {
            "success": True,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if data is not None:
            response["data"] = data
            
        if total is not None:
            response["total"] = total
            
        response.update(kwargs)
        return response
    
    @staticmethod
    def error(
        message: str,
        status_code: int = 500,
        error_code: Optional[str] = None,
        details: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """构建错误响应"""
        response = {
            "success": False,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if error_code:
            response["error_code"] = error_code
            
        if details:
            response["details"] = details
            
        return response


class ControllerBase:
    """控制器基础类"""
    
    def __init__(self, service_class=None, logger_name: str = None):
        self.service_class = service_class
        self.logger = get_logger(logger_name or self.__class__.__module__)
        self._service_instance = None
    
    @property
    def service(self):
        """懒加载服务实例"""
        if self._service_instance is None and self.service_class:
            self._service_instance = self.service_class()
        return self._service_instance
    
    def handle_service_call(
        self,
        service_method: Callable,
        *args,
        operation_name: str = "操作",
        success_message: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        统一处理服务调用
        
        Args:
            service_method: 服务方法
            operation_name: 操作名称，用于日志和错误消息
            success_message: 成功消息
            *args, **kwargs: 传递给服务方法的参数
            
        Returns:
            标准化的响应
        """
        try:
            self.logger.info(f"开始{operation_name}: {args[:2]}...")  # 只记录前2个参数避免日志过长
            
            result = service_method(*args, **kwargs)
            
            self.logger.info(f"{operation_name}完成")
            
            # 根据结果类型构建响应
            if isinstance(result, (list, tuple)):
                return ApiResponse.success(
                    data=result,
                    total=len(result),
                    message=success_message or f"{operation_name}成功"
                )
            elif isinstance(result, dict):
                return ApiResponse.success(
                    data=result,
                    message=success_message or f"{operation_name}成功"
                )
            elif isinstance(result, bool):
                if result:
                    return ApiResponse.success(
                        message=success_message or f"{operation_name}成功"
                    )
                else:
                    raise HTTPException(status_code=400, detail=f"{operation_name}失败")
            else:
                return ApiResponse.success(
                    data=result,
                    message=success_message or f"{operation_name}成功"
                )
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            self.logger.error(f"{operation_name}失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"{operation_name}失败: {str(e)}")


def api_handler(func=None, *, operation_name: str = None, success_message: str = None, error_status: int = 500):
    """
    API接口统一处理装饰器
    
    可以带参数或不带参数使用：
    @api_handler
    @api_handler(operation_name="搜索", success_message="搜索完成")
    
    Args:
        operation_name: 操作名称
        success_message: 成功消息
        error_status: 错误状态码
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        async def wrapper(*args, **kwargs):
            # 获取函数名作为默认操作名称
            op_name = operation_name or f.__name__.replace('_', ' ')
            logger = get_logger(f.__module__)
            
            try:
                logger.info(f"API调用: {op_name}")
                
                # 执行原函数
                if inspect.iscoroutinefunction(f):
                    result = await f(*args, **kwargs)
                else:
                    result = f(*args, **kwargs)
                
                # 如果结果已经是标准响应格式，直接返回
                if isinstance(result, dict) and "success" in result:
                    return result
                
                # 否则包装成标准响应
                if isinstance(result, dict) and "data" in result:
                    # 如果结果包含data字段，提取并合并其他字段
                    data = result.pop("data")
                    return ApiResponse.success(
                        data=data,
                        message=success_message or f"{op_name}成功",
                        **result  # 合并其他字段如total, person_type等
                    )
                else:
                    # 直接使用结果作为data
                    return ApiResponse.success(
                        data=result,
                        message=success_message or f"{op_name}成功"
                    )
                
            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                logger.error(f"{op_name}失败: {str(e)}")
                raise HTTPException(status_code=error_status, detail=f"{op_name}失败: {str(e)}")
                
        return wrapper
    
    # 支持不带参数的使用方式
    if func is None:
        return decorator
    else:
        return decorator(func)


class ServiceMapper:
    """服务映射器 - 统一管理服务实例映射"""
    
    def __init__(self, service_map: Dict[Any, Any]):
        self.service_map = service_map
        self._instances = {}
    
    def get_service(self, key: Any):
        """获取服务实例（懒加载）"""
        if key not in self._instances:
            service_class = self.service_map.get(key)
            if not service_class:
                raise HTTPException(status_code=400, detail=f"不支持的类型: {key}")
            self._instances[key] = service_class()
        return self._instances[key]
    
    def validate_key(self, key: Any) -> bool:
        """验证key是否有效"""
        return key in self.service_map


class PaginationHelper:
    """分页助手"""
    
    @staticmethod
    def apply_limit(data: List, limit: int, default_limit: int = 100) -> List:
        """应用限制"""
        if limit <= 0:
            limit = default_limit
        return data[:limit]
    
    @staticmethod
    def paginate_response(
        data: List,
        total: int = None,
        page: int = 1,
        page_size: int = 100,
        **kwargs
    ) -> Dict[str, Any]:
        """构建分页响应"""
        total = total or len(data)
        
        return {
            "success": True,
            "data": data,
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size
            },
            "timestamp": datetime.now().isoformat(),
            **kwargs
        } 