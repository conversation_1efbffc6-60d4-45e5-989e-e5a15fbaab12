"""
日志管理工具
"""

import os
import sys
from pathlib import Path
from loguru import logger

from app.config import get_settings


def setup_logger():
    """设置日志配置"""
    settings = get_settings()
    
    # 移除默认的控制台处理器
    logger.remove()
    
    # 添加控制台处理器（彩色输出）
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=settings.log_level
    )
    
    # 确保日志目录存在
    log_file_path = Path(settings.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加文件处理器
    logger.add(
        settings.log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level=settings.log_level,
        rotation="10 MB",  # 文件大小超过10MB时轮转
        retention="30 days",  # 保留30天的日志
        compression="zip",  # 压缩旧日志文件
        enqueue=True  # 异步写入
    )
    
    return logger


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger 