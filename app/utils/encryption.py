"""
数据加解密工具
"""

import base64
import hashlib
from typing import Optional

from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.config import get_settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class EncryptionService:
    """数据加解密服务"""
    
    def __init__(self, encryption_key: Optional[str] = None):
        """
        初始化加密服务
        
        Args:
            encryption_key: 加密密钥，如果不提供则从配置中获取
        """
        settings = get_settings()
        self.encryption_key = encryption_key or settings.encryption_key
        self._fernet = self._create_fernet_cipher()
    
    def _create_fernet_cipher(self) -> Fernet:
        """创建Fernet加密器"""
        # 使用PBKDF2从密钥字符串生成加密密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'bzn_graph_salt',  # 固定盐值，实际使用中可以配置
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.encryption_key.encode()))
        return Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """
        加密数据
        
        Args:
            data: 待加密的字符串
            
        Returns:
            str: 加密后的字符串（Base64编码）
        """
        if not data:
            return ""
        
        try:
            encrypted_data = self._fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"数据加密失败: {str(e)}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        解密数据
        
        Args:
            encrypted_data: 加密的数据（Base64编码）
            
        Returns:
            str: 解密后的字符串
        """
        if not encrypted_data:
            return ""
        
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"数据解密失败: {str(e)}")
            raise
    
    def hash_data(self, data: str) -> str:
        """
        生成数据哈希值（用于索引和比较）
        
        Args:
            data: 待哈希的数据
            
        Returns:
            str: SHA256哈希值
        """
        if not data:
            return ""
        
        return hashlib.sha256(data.encode()).hexdigest()
    
    def encrypt_sensitive_data(self, data: str, hash_for_index: bool = True) -> dict:
        """
        加密敏感数据并生成索引哈希
        
        Args:
            data: 敏感数据
            hash_for_index: 是否生成用于索引的哈希值
            
        Returns:
            dict: 包含加密数据和哈希值的字典
        """
        result = {
            "encrypted": self.encrypt(data)
        }
        
        if hash_for_index:
            result["hash"] = self.hash_data(data)
        
        return result
    
    @classmethod
    def generate_key(cls) -> str:
        """生成新的加密密钥"""
        return Fernet.generate_key().decode()


# 全局加密服务实例
_encryption_service = None


def get_encryption_service() -> EncryptionService:
    """获取全局加密服务实例"""
    global _encryption_service
    if _encryption_service is None:
        _encryption_service = EncryptionService()
    return _encryption_service 