"""
数据导入辅助工具

提供统一的数据覆盖确认、进度显示和结果统计功能
"""

import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from app.utils.logger import get_logger

logger = get_logger(__name__)


class ImportHelper:
    """数据导入辅助类"""
    
    def __init__(self, module_name: str):
        """
        初始化导入助手
        
        Args:
            module_name: 模块名称，用于日志和提示
        """
        self.module_name = module_name
        self.logger = get_logger(f"import.{module_name}")
    
    def confirm_overwrite(self, force: bool = False, data_type: str = "数据") -> bool:
        """
        统一的覆盖确认提示
        
        Args:
            force: 是否强制执行，跳过确认
            data_type: 数据类型描述
            
        Returns:
            bool: 是否确认覆盖
        """
        if force:
            self.logger.info(f"强制模式：跳过确认，直接清理{data_type}")
            return True
        
        print(f"\n⚠️  警告：即将清理所有现有的{data_type}")
        print(f"模块：{self.module_name}")
        print(f"时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        while True:
            confirm = input(f"\n是否确认清理所有{data_type}？(输入 'yes' 确认，'no' 取消): ").strip().lower()
            
            if confirm == 'yes':
                self.logger.info(f"用户确认清理{data_type}")
                return True
            elif confirm == 'no':
                self.logger.info(f"用户取消清理{data_type}")
                return False
            else:
                print("请输入 'yes' 或 'no'")
    
    def execute_with_progress(
        self, 
        operation_name: str, 
        operation_func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """
        带进度显示的操作执行
        
        Args:
            operation_name: 操作名称
            operation_func: 操作函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            操作结果
        """
        print(f"\n🔄 开始{operation_name}...")
        self.logger.info(f"开始{operation_name}")
        
        start_time = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ {operation_name}完成，耗时 {duration:.2f} 秒")
            self.logger.info(f"{operation_name}完成，耗时 {duration:.2f} 秒")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"❌ {operation_name}失败，耗时 {duration:.2f} 秒")
            self.logger.error(f"{operation_name}失败: {str(e)}")
            raise
    
    def print_import_result(self, result: Dict[str, Any], operation_name: str = "导入") -> None:
        """
        统一的导入结果显示
        
        Args:
            result: 导入结果字典
            operation_name: 操作名称
        """
        print(f"\n=== {operation_name}结果 ===")
        
        if result.get('success', False):
            print("✅ 状态: 成功")
        else:
            print("❌ 状态: 失败")
        
        # 显示数量统计
        for key, value in result.items():
            if key in ['extracted_count', 'transformed_count', 'stored_count', 'failed_count']:
                key_name = {
                    'extracted_count': '提取数量',
                    'transformed_count': '转换数量', 
                    'stored_count': '存储数量',
                    'failed_count': '失败数量'
                }.get(key, key)
                print(f"{key_name}: {value}")
        
        # 显示耗时
        if 'duration' in result:
            print(f"总耗时: {result['duration']:.2f}秒")
        
        # 显示错误信息
        if 'error' in result:
            print(f"错误信息: {result['error']}")
    
    def print_statistics(self, stats: Dict[str, Any], title: str = "统计信息") -> None:
        """
        统一的统计信息显示
        
        Args:
            stats: 统计信息字典
            title: 标题
        """
        print(f"\n=== {title} ===")
        
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"{key}:")
                for sub_key, sub_value in value.items():
                    print(f"  {sub_key}: {sub_value}")
            elif isinstance(value, list):
                print(f"{key}: {len(value)} 项")
                for i, item in enumerate(value[:5], 1):  # 只显示前5项
                    print(f"  {i}. {item}")
                if len(value) > 5:
                    print(f"  ... 还有 {len(value) - 5} 项")
            else:
                print(f"{key}: {value}")
    
    def validate_import(
        self, 
        validation_func: Callable, 
        min_count: int = 1,
        *args, 
        **kwargs
    ) -> bool:
        """
        验证导入结果
        
        Args:
            validation_func: 验证函数（通常是获取统计信息的函数）
            min_count: 最小预期数量
            *args: 验证函数参数
            **kwargs: 验证函数关键字参数
            
        Returns:
            bool: 验证是否通过
        """
        print(f"\n🔍 验证{self.module_name}导入结果...")
        self.logger.info(f"开始验证{self.module_name}导入结果")
        
        try:
            stats = validation_func(*args, **kwargs)
            
            # 统一使用 total_count 字段
            total_count = stats.get('total_count', 0)
            
            if total_count < min_count:
                print(f"❌ 验证失败: 数据量不足（实际: {total_count}, 预期: >= {min_count}）")
                self.logger.error(f"验证失败: 数据量不足（{total_count} < {min_count}）")
                return False
            
            print(f"✅ 验证通过: 找到 {total_count} 条记录")
            self.logger.info(f"验证通过: 找到 {total_count} 条记录")
            
            # 显示详细统计
            self.print_statistics(stats, f"{self.module_name}统计信息")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {str(e)}")
            self.logger.error(f"验证失败: {str(e)}")
            return False
    
    def print_header(self, title: str) -> None:
        """
        打印标准的脚本头部信息
        
        Args:
            title: 脚本标题
        """
        print("=" * 60)
        print(f"BZN 关系图谱系统 - {title}")
        print(f"模块: {self.module_name}")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
    
    def print_footer(self) -> None:
        """打印标准的脚本尾部信息"""
        print("\n" + "=" * 60)
        print(f"{self.module_name} 处理完成！")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60) 