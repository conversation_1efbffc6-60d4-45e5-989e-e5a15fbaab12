"""
数据验证工具
"""

import re
from typing import Optional


def validate_id_card(id_card: str) -> bool:
    """
    验证身份证号码格式
    
    Args:
        id_card: 身份证号码
        
    Returns:
        bool: 是否有效
    """
    if not id_card:
        return False
    
    # 18位身份证号码正则表达式
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    
    return bool(re.match(pattern, id_card))


def validate_phone(phone: str) -> bool:
    """
    验证手机号码格式
    
    Args:
        phone: 手机号码
        
    Returns:
        bool: 是否有效
    """
    if not phone:
        return False
    
    # 中国大陆手机号码正则表达式
    pattern = r'^1[3-9]\d{9}$'
    
    return bool(re.match(pattern, phone))


def validate_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        bool: 是否有效
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    return bool(re.match(pattern, email))


def validate_date_string(date_str: str, format_type: str = "yyyy-mm-dd") -> bool:
    """
    验证日期字符串格式
    
    Args:
        date_str: 日期字符串
        format_type: 日期格式类型
        
    Returns:
        bool: 是否有效
    """
    if not date_str:
        return False
    
    if format_type == "yyyy-mm-dd":
        pattern = r'^\d{4}-\d{2}-\d{2}$'
    elif format_type == "yyyy/mm/dd":
        pattern = r'^\d{4}/\d{2}/\d{2}$'
    else:
        return False
    
    return bool(re.match(pattern, date_str))