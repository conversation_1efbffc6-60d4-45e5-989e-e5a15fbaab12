"""
Neo4j数据序列化工具模块

使用优雅的方式处理Neo4j数据类型的序列化问题
"""

import json
from datetime import datetime, date
from typing import Any, Dict, List, Union
from decimal import Decimal
from neo4j.time import DateTime as Neo4jDateTime, Date as Neo4jDate, Time as Neo4jTime
from app.utils.logger import get_logger

logger = get_logger(__name__)


class Neo4jSerializer:
    """
    Neo4j数据序列化器 - 优雅处理各种Neo4j数据类型
    """
    
    @staticmethod
    def serialize_datetime(dt: Any) -> str:
        """序列化DateTime对象为ISO格式字符串"""
        if isinstance(dt, Neo4jDateTime):
            # Neo4j DateTime对象
            return datetime(
                year=dt.year, month=dt.month, day=dt.day,
                hour=dt.hour, minute=dt.minute, second=dt.second,
                microsecond=dt.nanosecond // 1000
            ).strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(dt, datetime):
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(dt, str):
            return dt
        else:
            return str(dt)
    
    @staticmethod
    def serialize_date(d: Any) -> str:
        """序列化Date对象为字符串"""
        if isinstance(d, Neo4jDate):
            return date(year=d.year, month=d.month, day=d.day).strftime("%Y-%m-%d")
        elif isinstance(d, date):
            return d.strftime("%Y-%m-%d")
        else:
            return str(d)
    
    @staticmethod
    def serialize_time(t: Any) -> str:
        """序列化Time对象为字符串"""
        if isinstance(t, Neo4jTime):
            return f"{t.hour:02d}:{t.minute:02d}:{t.second:02d}"
        else:
            return str(t)
    
    @classmethod
    def serialize_value(cls, value: Any) -> Any:
        """
        序列化单个值 - 核心序列化逻辑
        """
        # 处理None值
        if value is None:
            return None
        
        # 处理Neo4j时间类型
        if isinstance(value, Neo4jDateTime):
            return cls.serialize_datetime(value)
        elif isinstance(value, Neo4jDate):
            return cls.serialize_date(value)
        elif isinstance(value, Neo4jTime):
            return cls.serialize_time(value)
        
        # 处理Python标准时间类型
        elif isinstance(value, datetime):
            return cls.serialize_datetime(value)
        elif isinstance(value, date):
            return cls.serialize_date(value)
        
        # 处理数值类型
        elif isinstance(value, Decimal):
            return float(value)
        
        # 处理容器类型
        elif isinstance(value, dict):
            return cls.serialize_dict(value)
        elif isinstance(value, (list, tuple)):
            return cls.serialize_list(value)
        
        # 处理Neo4j对象（节点、关系）
        elif hasattr(value, '__dict__') and 'neo4j' in str(type(value)):
            return cls.serialize_neo4j_object(value)
        
        # 其他类型直接返回
        else:
            return value
    
    @classmethod
    def serialize_dict(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """序列化字典"""
        return {key: cls.serialize_value(val) for key, val in data.items()}
    
    @classmethod
    def serialize_list(cls, data: Union[List, tuple]) -> List[Any]:
        """序列化列表或元组"""
        return [cls.serialize_value(item) for item in data]
    
    @classmethod
    def serialize_neo4j_object(cls, obj: Any) -> Any:
        """序列化Neo4j对象（节点、关系等）"""
        try:
            # 尝试转换为字典
            if hasattr(obj, '_properties'):
                data = dict(obj._properties)
            else:
                data = dict(obj)
            return cls.serialize_dict(data)
        except Exception as e:
            logger.warning(f"Neo4j对象序列化失败: {e}")
            return str(obj)
    
    @classmethod
    def serialize(cls, data: Any) -> Any:
        """
        主序列化入口 - 处理任意数据类型
        """
        return cls.serialize_value(data)


class RelationshipResponseOptimizer:
    """
    关系查询响应优化器 - 解决数据冗余问题
    修复版本：处理节点ID冲突和关系类型丢失问题
    """
    
    @staticmethod
    def optimize_relationship_response(records: List[Dict[str, Any]], 
                                     source_key: str = "source", 
                                     target_key: str = "target",
                                     rel_key: str = "r",
                                     query_hint: str = None) -> Dict[str, Any]:
        """
        优化关系查询响应，去除重复节点并整理关系结构
        """
        nodes_map = {}
        relationships = []
        processed_relationships = set()  # 用于跟踪已处理的关系，避免重复
        
        for i, record in enumerate(records):
            try:
                # 序列化节点数据
                source_data = serialize_neo4j_value(record.get(source_key, {}))
                target_data = serialize_neo4j_value(record.get(target_key, {}))
                
                # 生成复合节点ID
                source_composite_id = RelationshipResponseOptimizer._extract_composite_node_id(
                    source_data, record.get(source_key), query_hint, source_key
                )
                target_composite_id = RelationshipResponseOptimizer._extract_composite_node_id(
                    target_data, record.get(target_key), query_hint, target_key
                )
                
                # 添加源节点
                if source_composite_id and source_composite_id not in nodes_map:
                    nodes_map[source_composite_id] = {
                        "id": source_composite_id,
                        "business_id": RelationshipResponseOptimizer._extract_business_id(source_data),
                        "labels": RelationshipResponseOptimizer._extract_node_labels(record.get(source_key)),
                        "properties": source_data
                    }
                
                # 添加目标节点
                if target_composite_id and target_composite_id not in nodes_map:
                    nodes_map[target_composite_id] = {
                        "id": target_composite_id,
                        "business_id": RelationshipResponseOptimizer._extract_business_id(target_data),
                        "labels": RelationshipResponseOptimizer._extract_node_labels(record.get(target_key)),
                        "properties": target_data
                    }
                
                # 提取关系信息
                rel_data = record.get(rel_key)
                if rel_data and source_composite_id and target_composite_id:
                    rel_info = RelationshipResponseOptimizer._extract_relationship_info_fixed(rel_data)
                    if rel_info:
                        # 创建关系的唯一标识符（无向关系）
                        # 使用有序的节点对作为关系标识，避免重复
                        node_pair = tuple(sorted([source_composite_id, target_composite_id]))
                        rel_type = rel_info.get("type", "UNKNOWN")
                        rel_identifier = (node_pair, rel_type)
                        
                        # 只有当这个关系还没有被处理过时才添加
                        if rel_identifier not in processed_relationships:
                            processed_relationships.add(rel_identifier)
                            
                            # 对于去重后的关系，保持原始的方向性
                            # （使用第一次遇到的方向）
                            relationships.append({
                                "id": f"rel_{len(relationships)}",
                                "source": source_composite_id,
                                "target": target_composite_id,
                                "type": rel_type,
                                "properties": rel_info.get("properties", {})
                            })
                
            except Exception as e:
                logger.warning(f"处理第 {i+1} 条关系记录失败: {str(e)}")
                continue
        
        return {
            "nodes": nodes_map,
            "relationships": relationships,
            "summary": {
                "total_nodes": len(records) * 2,  # 原始记录中的节点数（可能重复）
                "total_relationships": len(relationships),
                "unique_nodes": len(nodes_map)
            }
        }
    
    @staticmethod
    def _extract_composite_node_id(node_data: Dict[str, Any], node_obj: Any, query_hint: str = None, variable_name: str = None) -> str:
        """
        提取复合节点ID，避免不同类型节点的ID冲突
        格式: "节点类型:business_id"（基于节点实际内容，而不是变量名）
        """
        # 获取业务ID
        business_id = RelationshipResponseOptimizer._extract_business_id(node_data)
        
        # 提取节点标签
        labels = RelationshipResponseOptimizer._extract_node_labels(node_obj)
        
        # 确定节点类型前缀
        if labels:
            prefix = labels[0]  # 使用第一个标签
        else:
            # 如果没有标签，尝试从查询推断
            if query_hint:
                import re
                # 查找类似 (variable:Label) 的模式
                label_patterns = re.findall(r'\([^:]+:(\w+)\)', query_hint, re.IGNORECASE)
                if label_patterns:
                    prefix = label_patterns[0]
                else:
                    prefix = "Node"
            else:
                prefix = "Node"
        
        if business_id:
            return f"{prefix}:{business_id}"
        else:
            # 如果没有业务ID，使用节点的内部ID
            if hasattr(node_obj, 'element_id'):
                return f"{prefix}:{node_obj.element_id}"
            elif hasattr(node_obj, 'id'):
                return f"{prefix}:{node_obj.id}"
            else:
                # 最后兜底，使用节点数据的哈希值确保一致性
                import hashlib
                node_str = str(sorted(node_data.items())) if node_data else str(id(node_obj))
                hash_id = hashlib.md5(node_str.encode()).hexdigest()[:8]
                return f"{prefix}:{hash_id}"
    
    @staticmethod
    def _extract_business_id(node_data: Dict[str, Any]) -> str:
        """从节点数据中提取业务ID"""
        for id_field in ['business_id', 'code', 'id', 'name']:
            if id_field in node_data and node_data[id_field]:
                return str(node_data[id_field])
        return None
    
    @staticmethod
    def _extract_node_labels(node_obj: Any) -> List[str]:
        """从节点对象中提取标签"""
        if not node_obj:
            return []
        
        try:
            # 尝试从Neo4j节点对象中提取标签
            if hasattr(node_obj, 'labels'):
                return list(node_obj.labels)
            elif hasattr(node_obj, '_labels'):
                return list(node_obj._labels)
            # 如果是字典类型，尝试从序列化数据中提取
            elif isinstance(node_obj, dict):
                # 检查是否有标签信息存储在字典中
                if 'labels' in node_obj:
                    return node_obj['labels']
                elif '_labels' in node_obj:
                    return node_obj['_labels']
                else:
                    # 如果无法提取标签，返回空列表，让调用者处理
                    return []
            else:
                return []
        except Exception:
            return []
    
    @staticmethod
    def _extract_relationship_info_fixed(rel_data: Any) -> Dict[str, Any]:
        """
        修复版本：正确提取关系信息
        """
        if isinstance(rel_data, (list, tuple)) and len(rel_data) >= 2:
            # 处理 [source_node, "REL_TYPE", target_node] 或 (source_node, "REL_TYPE", target_node) 格式
            rel_type = "UNKNOWN"
            properties = {}
            
            # 遍历列表/元组寻找字符串类型的关系类型
            for item in rel_data:
                if isinstance(item, str) and item.strip():
                    # 这很可能是关系类型
                    rel_type = item.strip()
                    break
            
            return {
                "type": rel_type,
                "properties": properties
            }
            
        elif isinstance(rel_data, dict):
            # 处理直接的关系对象
            serialized = Neo4jSerializer.serialize(rel_data)
            rel_type = serialized.get("type", "UNKNOWN")
            
            return {
                "type": rel_type,
                "properties": {k: v for k, v in serialized.items() 
                             if k not in ["type", "relationship_type", "rel_type", "edge_type"]}
            }
            
        elif hasattr(rel_data, 'type'):
            # Neo4j关系对象
            try:
                return {
                    "type": rel_data.type,
                    "properties": Neo4jSerializer.serialize(dict(rel_data._properties) if hasattr(rel_data, '_properties') else {})
                }
            except Exception:
                pass
        
        # 兜底情况
        return {
            "type": "UNKNOWN", 
            "properties": {}
        }


# 提供简洁的函数接口
def serialize_neo4j_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """序列化Neo4j记录"""
    return Neo4jSerializer.serialize(record)


def serialize_neo4j_records(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """批量序列化Neo4j记录"""
    return [serialize_neo4j_record(record) for record in records]


def serialize_neo4j_value(value: Any) -> Any:
    """序列化单个Neo4j值"""
    return Neo4jSerializer.serialize(value)


def optimize_relationship_query_response(records: List[Dict[str, Any]], 
                                        source_key: str = "parent", 
                                        target_key: str = "child",
                                        rel_key: str = "r",
                                        query_hint: str = None) -> Dict[str, Any]:
    """
    优化关系查询响应的便捷函数
    """
    return RelationshipResponseOptimizer.optimize_relationship_response(
        records, source_key, target_key, rel_key, query_hint
    )


class Neo4jJSONEncoder(json.JSONEncoder):
    """
    自定义JSON编码器 - 支持Neo4j数据类型
    """
    def default(self, obj):
        return Neo4jSerializer.serialize(obj)


def safe_json_dumps(data: Any, **kwargs) -> str:
    """
    安全的JSON序列化
    """
    kwargs.setdefault('cls', Neo4jJSONEncoder)
    kwargs.setdefault('ensure_ascii', False)
    kwargs.setdefault('indent', 2)
    return json.dumps(data, **kwargs)


# 保持向后兼容的函数别名
serialize_neo4j_datetime = Neo4jSerializer.serialize_datetime
serialize_neo4j_date = Neo4jSerializer.serialize_date
serialize_neo4j_time = Neo4jSerializer.serialize_time 