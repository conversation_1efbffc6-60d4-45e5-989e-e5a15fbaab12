"""
Neo4j数据提取工具类

统一处理Neo4j查询结果的数据提取和序列化
"""
from typing import Any, Dict, List, Optional, Union
import logging

# 导入优雅的序列化工具
from app.utils.serializers import Neo4jSerializer

logger = logging.getLogger(__name__)


class Neo4jDataExtractor:
    """Neo4j数据提取工具类 - 简洁优雅的数据处理"""
    
    @staticmethod
    def extract_properties(obj: Any) -> Dict[str, Any]:
        """
        通用属性提取方法 - 适用于节点和关系
        
        Args:
            obj: Neo4j对象（节点或关系）
            
        Returns:
            Dict[str, Any]: 序列化后的属性字典
        """
        if not obj:
            return {}
            
        try:
            # 尝试多种方式提取属性
            if isinstance(obj, dict):
                properties = obj
            elif hasattr(obj, '_properties'):
                properties = dict(obj._properties)
            elif hasattr(obj, '__dict__'):
                properties = dict(obj)
            else:
                properties = dict(obj)
            
            # 使用优雅的序列化器处理特殊类型
            return Neo4jSerializer.serialize(properties)
                
        except Exception as e:
            logger.warning(f"提取对象属性失败: {str(e)}")
            return {}
    
    @staticmethod
    def extract_node_properties(node_obj: Any) -> Dict[str, Any]:
        """提取节点属性（向后兼容）"""
        return Neo4jDataExtractor.extract_properties(node_obj)
    
    @staticmethod
    def extract_relationship_properties(rel_obj: Any) -> Dict[str, Any]:
        """提取关系属性（向后兼容）"""
        return Neo4jDataExtractor.extract_properties(rel_obj)
    
    @staticmethod
    def extract_record_value(record: Dict[str, Any], key: str) -> Dict[str, Any]:
        """
        从记录中提取指定键的值并序列化
        """
        obj = record.get(key)
        return Neo4jDataExtractor.extract_properties(obj)
    
    @staticmethod
    def build_node_data(node_obj: Any, node_id: str = None, labels: List[str] = None) -> Dict[str, Any]:
        """
        构建标准的节点数据结构
        """
        properties = Neo4jDataExtractor.extract_properties(node_obj)
        
        # 智能提取ID
        if not node_id:
            node_id = (properties.get('business_id') or 
                      properties.get('code') or 
                      properties.get('id') or 
                      properties.get('name'))
        
        # 智能提取标签
        if not labels and hasattr(node_obj, 'labels'):
            try:
                labels = list(node_obj.labels)
            except:
                labels = []
        
        return {
            "id": node_id,
            "labels": labels or [],
            "properties": properties
        }
    
    @staticmethod
    def build_relationship_data(rel_obj: Any, rel_id: str = None, rel_type: str = None, 
                              start_node: str = None, end_node: str = None) -> Dict[str, Any]:
        """
        构建标准的关系数据结构
        """
        properties = Neo4jDataExtractor.extract_properties(rel_obj)
        
        # 智能提取关系类型
        if not rel_type and hasattr(rel_obj, 'type'):
            try:
                rel_type = rel_obj.type
            except:
                rel_type = "RELATED_TO"
        
        # 智能提取节点ID
        if not start_node and hasattr(rel_obj, 'start_node'):
            try:
                start_node = getattr(rel_obj.start_node, 'id', str(rel_obj.start_node))
            except:
                pass
                
        if not end_node and hasattr(rel_obj, 'end_node'):
            try:
                end_node = getattr(rel_obj.end_node, 'id', str(rel_obj.end_node))
            except:
                pass
        
        return {
            "id": rel_id,
            "type": rel_type or "RELATED_TO",
            "start_node": start_node,
            "end_node": end_node,
            "properties": properties
        }
    
    @staticmethod
    def safe_extract_relationships_from_record(record: Dict[str, Any], 
                                             rel_key: str = 'r',
                                             relationship_type: str = None) -> Dict[str, Any]:
        """
        从记录中安全地提取关系数据
        """
        rel_data = {'relationship_type': relationship_type or 'UNKNOWN'}
        
        try:
            rel_obj = record.get(rel_key)
            if rel_obj:
                # 使用通用提取方法
                properties = Neo4jDataExtractor.extract_properties(rel_obj)
                rel_data.update(properties)
        except Exception as e:
            logger.warning(f"提取关系数据失败: {str(e)}")
        
        return rel_data
    
    @staticmethod
    def batch_extract_nodes(records: List[Dict[str, Any]], 
                          node_key: str,
                          id_key: str = None,
                          labels_key: str = None) -> List[Dict[str, Any]]:
        """
        批量提取节点数据
        """
        nodes = []
        
        for i, record in enumerate(records):
            try:
                node_obj = record.get(node_key)
                if not node_obj:
                    continue
                
                # 提取ID和标签
                node_id = record.get(id_key) if id_key else None
                labels = record.get(labels_key, []) if labels_key else None
                
                # 构建节点数据
                node_data = Neo4jDataExtractor.build_node_data(node_obj, node_id, labels)
                nodes.append(node_data)
                
            except Exception as e:
                logger.warning(f"提取第 {i+1} 个节点失败: {str(e)}")
                continue
        
        return nodes
    
    @staticmethod
    def batch_extract_relationships(records: List[Dict[str, Any]], 
                                  rel_key: str,
                                  rel_type_key: str = None,
                                  start_key: str = None,
                                  end_key: str = None) -> List[Dict[str, Any]]:
        """
        批量提取关系数据
        """
        relationships = []
        
        for i, record in enumerate(records):
            try:
                rel_obj = record.get(rel_key)
                if not rel_obj:
                    continue
                
                # 提取关系信息
                rel_type = record.get(rel_type_key) if rel_type_key else None
                start_node = record.get(start_key) if start_key else None
                end_node = record.get(end_key) if end_key else None
                rel_id = f"rel_{i}"
                
                # 构建关系数据
                rel_data = Neo4jDataExtractor.build_relationship_data(
                    rel_obj, rel_id, rel_type, start_node, end_node
                )
                relationships.append(rel_data)
                
            except Exception as e:
                logger.warning(f"提取第 {i+1} 个关系失败: {str(e)}")
                continue
        
        return relationships
    
    @staticmethod
    def serialize_query_result(result: Any) -> Any:
        """
        序列化查询结果 - 新增的便捷方法
        
        Args:
            result: Neo4j查询结果
            
        Returns:
            Any: 序列化后的结果
        """
        return Neo4jSerializer.serialize(result) 