[project]
name = "bzn-relational-graph-server"
version = "0.1.0"
description = "基于 Neo4j 的关系图谱系统服务端"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "flask>=2.3.0",
    "flask-cors>=4.0.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "neo4j>=5.0.0",
    "pandas>=2.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "cryptography>=41.0.0",
    "impyla>=0.18.0",
    "pydantic-settings>=2.0.0",
    "marshmallow>=3.20.0",
    "click>=8.1.0",
    "email-validator>=2.2.0",
    "requests>=2.31.0",
    "psutil>=5.9.0",
    "neo4j-driver[json]>=5.28.1",
    "py2neo>=2021.2.4",
    "neomodel>=5.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
]
