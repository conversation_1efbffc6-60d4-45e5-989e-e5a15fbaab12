# 批量关系服务修改策略

## 目标文件列表

### 保险销售渠道相关服务 (5个)
1. `insurance_sales_channel_agent_relationship_service.py` - 销售渠道与代理人关系
2. `insurance_sales_channel_area_relationship_service.py` - 销售渠道与区域关系
3. `insurance_sales_channel_bd_relationship_service.py` - 销售渠道与BD关系
4. `insurance_sales_channel_hierarchy_relationship_service.py` - 销售渠道层级关系
5. `insurance_sales_channel_mo_relationship_service.py` - 销售渠道与MO关系

### 保单相关服务 (2个)
6. `policy_company_area_relationship_service.py` - 保单公司与区域关系
7. `policy_covered_company_relationship_service.py` - 保单与承保公司关系

## 统一修改模式

### 1. 文件头部修改
```python
# 原来
import pandas as pd
from datetime import date, timedelta

# 修改为
from datetime import datetime
```

### 2. 方法签名简化
```python
# 原来
def create_relationships(self, 
                        limit: Optional[int] = None,
                        where_conditions: Optional[str] = None,
                        custom_day: Optional[str] = None) -> bool:

# 修改为
def create_relationships(self, limit: Optional[int] = None) -> bool:
```

### 3. 核心方法替换模式

#### A. 查询方法
- `_query_*_data_from_hive()` → `_query_*_data_from_neo4j()`
- 返回类型：`pd.DataFrame` → `List[Dict[str, Any]]`

#### B. 构建关系数据方法
- `_build_relationship_data(hive_data: pd.DataFrame)` → `_build_relationship_data_from_neo4j(data: List[Dict[str, Any]])`

#### C. 创建关系方法
- `_create_relationships_directly()` → `_create_*_relationships()`

### 4. 节点标签标准化
- 使用完整的节点名称，避免缩写
- 统一命名规范

### 5. 必需新增方法
- `cleanup_duplicate_relationships()` - 清理重复关系
- `_check_*_exists()` - 检查目标节点存在性（如需要）

## 各服务特定配置

### insurance_sales_channel_agent_relationship_service.py
- **关系类型**: `HAS_AGENT`
- **源节点**: `InsuranceSalesChannel`
- **目标节点**: `InsuranceAgent`
- **匹配字段**: `agent_code` 或 `agent_name`

### insurance_sales_channel_area_relationship_service.py
- **关系类型**: `LOCATED_IN`
- **源节点**: `InsuranceSalesChannel`
- **目标节点**: `Area`
- **匹配字段**: 地址信息（province/city/district）

### insurance_sales_channel_bd_relationship_service.py
- **关系类型**: `HAS_BD`
- **源节点**: `InsuranceSalesChannel`
- **目标节点**: `InsuranceBD`
- **匹配字段**: `bd_code` 或 `bd_name`

### insurance_sales_channel_hierarchy_relationship_service.py
- **关系类型**: `PARENT_OF` / `CHILD_OF`
- **源节点**: `InsuranceSalesChannel`
- **目标节点**: `InsuranceSalesChannel`
- **匹配字段**: `parent_channel_code`

### insurance_sales_channel_mo_relationship_service.py
- **关系类型**: `HAS_MO`
- **源节点**: `InsuranceSalesChannel`
- **目标节点**: `InsuranceMO`
- **匹配字段**: `mo_code` 或 `mo_name`

### policy_company_area_relationship_service.py
- **关系类型**: `LOCATED_IN`
- **源节点**: `PolicyCompany`
- **目标节点**: `Area`
- **匹配字段**: 地址信息（province/city/district）

### policy_covered_company_relationship_service.py
- **关系类型**: `COVERED_BY`
- **源节点**: `Policy`
- **目标节点**: `InsuranceCompany`
- **匹配字段**: `insurance_company_code`

## 标准方法模板

### Neo4j查询方法模板
```python
def _query_*_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """从Neo4j查询数据"""
    try:
        conditions = [
            "source.field IS NOT NULL",
            "source.field <> ''"
        ]
        where_clause = " AND ".join(conditions)
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        MATCH (source:SourceNode)
        WHERE {where_clause}
        RETURN source.code as source_code,
               source.name as source_name,
               source.target_field as target_field,
               source.business_id as source_business_id
        ORDER BY source.code
        {limit_clause}
        """
        
        results = self._safe_execute_query(query)
        return self._process_query_results(results)
        
    except Exception as e:
        logger.error(f"查询Neo4j数据失败: {str(e)}")
        return []
```

### 构建关系数据方法模板
```python
def _build_relationship_data_from_neo4j(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """构建关系数据"""
    relationship_data = []
    skipped_count = 0
    
    for item in data:
        source_code = item.get('source_code', '')
        target_field = item.get('target_field', '')
        
        if not source_code or not target_field:
            skipped_count += 1
            continue
        
        # 检查目标节点是否存在（如需要）
        if not self._check_target_exists(target_field):
            skipped_count += 1
            continue
        
        relationship_data.append({
            'source_code': source_code,
            'target_field': target_field,
            'remarks': f"关系: {source_code} 关联 {target_field}",
            'data_source_node': 'SourceNode',
            'relationship_strength': 8,
            'relationship_type': '业务关系'
        })
    
    logger.info(f"构建了 {len(relationship_data)} 个有效关系，跳过了 {skipped_count} 个无效记录")
    return relationship_data
```

### 创建关系方法模板
```python
def _create_*_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
    """创建关系"""
    try:
        batch_size = 100
        success_count = 0
        
        for i in range(0, len(relationship_data), batch_size):
            batch_data = relationship_data[i:i + batch_size]
            
            query = """
            UNWIND $relationships as rel
            MATCH (source:SourceNode {code: rel.source_code})
            MATCH (target:TargetNode {field: rel.target_field})
            MERGE (source)-[r:RELATIONSHIP_TYPE]->(target)
            SET r.relationship_type = 'RELATIONSHIP_TYPE',
                r.relationship_status = 'active',
                r.relationship_strength = rel.relationship_strength,
                r.data_source_node = rel.data_source_node,
                r.remarks = rel.remarks,
                r.created_at = datetime(),
                r.updated_at = datetime()
            RETURN count(r) as created_count
            """
            
            result = self._safe_execute_query(query, {'relationships': batch_data})
            if result:
                batch_count = result[0].get('created_count', 0)
                success_count += batch_count
                logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
        
        logger.info(f"总共成功创建 {success_count} 个关系")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"创建关系失败: {str(e)}")
        return False
```

## 修改优先级

1. **高优先级**: 保险销售渠道相关服务（业务核心）
2. **中优先级**: 保单相关服务（数据完整性）

## 验证步骤

1. 语法检查
2. 导入验证
3. 方法签名检查
4. 节点标签验证
5. 功能测试
