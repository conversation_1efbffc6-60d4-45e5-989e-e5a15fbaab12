#!/bin/bash

# BZN关系图谱系统启动脚本
# 使用方法:
#   ./run.sh           - 开发模式启动
#   ./run.sh dev       - 开发模式启动
#   ./run.sh prod      - 生产模式启动
#   ./run.sh stop      - 停止服务
#   ./run.sh status    - 查看服务状态
#   ./run.sh restart   - 重启服务
#   ./run.sh monitor   - 监控服务状态

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="BZN关系图谱系统"
DEFAULT_HOST="127.0.0.1"
DEFAULT_PORT="8000"
PID_FILE="app.pid"
LOG_FILE="app.log"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}${PROJECT_NAME} 启动脚本${NC}"
    echo ""
    echo "使用方法:"
    echo "  ./run.sh           开发模式启动"
    echo "  ./run.sh dev       开发模式启动"
    echo "  ./run.sh prod      生产模式启动"
    echo "  ./run.sh stop      停止服务"
    echo "  ./run.sh status    查看服务状态"
    echo "  ./run.sh restart   重启服务"
    echo "  ./run.sh monitor   监控服务状态"
    echo "  ./run.sh daemon    启动监控守护进程"
    echo "  ./run.sh help      显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  API_HOST          服务监听地址 (默认: ${DEFAULT_HOST})"
    echo "  API_PORT          服务端口 (默认: ${DEFAULT_PORT})"
}

# 检查依赖
check_dependencies() {
    if ! command -v uv &> /dev/null; then
        echo -e "${RED}❌ uv未安装${NC}"
        echo -e "${YELLOW}💡 安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh${NC}"
        exit 1
    fi
    
    if [[ ! -f "main.py" ]]; then
        echo -e "${RED}❌ 未找到main.py，请确保在项目根目录执行${NC}"
        exit 1
    fi
}

# 获取配置
get_config() {
    HOST=${API_HOST:-$DEFAULT_HOST}
    PORT=${API_PORT:-$DEFAULT_PORT}
}

# 强制清理所有相关进程
force_cleanup() {
    echo -e "${YELLOW}🧹 强制清理所有相关进程...${NC}"
    
    # 清理所有相关进程
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "uv run uvicorn" 2>/dev/null || true
    pkill -f "python.*main\.py" 2>/dev/null || true
    
    # 清理端口占用
    lsof -ti:${PORT} 2>/dev/null | xargs kill -9 2>/dev/null || true
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    sleep 2
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 检查服务状态
check_status() {
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 服务正在运行 (PID: $PID)${NC}"
            echo -e "${BLUE}📍 服务地址: http://${HOST}:${PORT}${NC}"
            echo -e "${BLUE}📖 API文档: http://${HOST}:${PORT}/api/v1/docs${NC}"
            
            # 检查服务响应
            if curl -s --max-time 5 "http://${HOST}:${PORT}/health" > /dev/null 2>&1; then
                echo -e "${GREEN}🟢 服务响应正常${NC}"
            else
                echo -e "${YELLOW}⚠️  服务可能假死，建议重启${NC}"
            fi
            return 0
        else
            rm -f "$PID_FILE"
            echo -e "${YELLOW}⚠️  PID文件存在但进程未运行，已清理${NC}"
        fi
    fi
    echo -e "${RED}❌ 服务未运行${NC}"
    return 1
}

# 停止服务
stop_service() {
    echo -e "${YELLOW}🛑 停止服务...${NC}"
    
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            # 优雅停止
            kill -TERM "$PID" 2>/dev/null || true
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    break
                fi
                sleep 1
            done
            
            # 如果仍然存在，强制杀死
            if ps -p "$PID" > /dev/null 2>&1; then
                kill -KILL "$PID" 2>/dev/null || true
            fi
            
            echo -e "${GREEN}✅ 服务已停止${NC}"
        fi
        rm -f "$PID_FILE"
    else
        force_cleanup
    fi
}

# 监控服务状态
monitor_service() {
    echo -e "${BLUE}📊 开始监控服务状态...${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
    echo ""
    
    while true; do
        clear
        echo -e "${BLUE}=== $(date) ===${NC}"
        
        if check_status > /dev/null 2>&1; then
            check_status
            
            # 检查内存使用
            if [[ -f "$PID_FILE" ]]; then
                PID=$(cat "$PID_FILE")
                if ps -p "$PID" > /dev/null 2>&1; then
                    MEM_USAGE=$(ps -o pid,ppid,pgid,pcpu,pmem,vsz,rss,comm -p "$PID" | tail -1)
                    echo -e "${BLUE}📈 进程信息: ${MEM_USAGE}${NC}"
                fi
            fi
        else
            echo -e "${RED}❌ 服务未运行${NC}"
        fi
        
        sleep 5
    done
}

# 开发模式启动
start_dev() {
    echo -e "${BLUE}🔧 启动开发环境...${NC}"
    echo -e "${BLUE}📍 服务地址: http://${HOST}:${PORT}${NC}"
    echo -e "${BLUE}📖 API文档: http://${HOST}:${PORT}/api/v1/docs${NC}"
    echo -e "${YELLOW}🔄 自动重载: 开启${NC}"
    echo -e "${YELLOW}🛑 按 Ctrl+C 停止服务${NC}"
    echo "$(printf '%.0s-' {1..50})"
    
    # 设置环境变量优化Python性能
    export PYTHONUNBUFFERED=1
    export PYTHONDONTWRITEBYTECODE=1
    
    # 直接启动，不使用后台模式
    uv run uvicorn main:app \
        --host "${HOST}" \
        --port "${PORT}" \
        --reload \
        --reload-delay 2 \
        --access-log \
        --log-level info \
        --timeout-keep-alive 30 \
        --limit-concurrency 50
}

# 生产模式启动
start_prod() {
    echo -e "${BLUE}🏭 启动生产环境...${NC}"
    
    # 检查是否已经在运行
    if check_status > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  服务已在运行，请先停止${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}📍 服务地址: http://${HOST}:${PORT}${NC}"
    echo -e "${BLUE}📖 API文档: http://${HOST}:${PORT}/api/v1/docs${NC}"
    echo -e "${GREEN}🚀 启动中...${NC}"
    
    # 设置环境变量优化Python性能
    export PYTHONUNBUFFERED=1
    export PYTHONDONTWRITEBYTECODE=1
    export PYTHONHASHSEED=random
    
    # 后台启动，添加资源限制
    nohup uv run uvicorn main:app \
        --host "${HOST}" \
        --port "${PORT}" \
        --workers 1 \
        --access-log \
        --log-level warning \
        --timeout-keep-alive 15 \
        --limit-concurrency 50 \
        --backlog 64 > "$LOG_FILE" 2>&1 &
    
    # 保存PID
    echo $! > "$PID_FILE"
    
    # 等待启动
    sleep 3
    
    if check_status > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 生产服务启动成功${NC}"
        echo -e "${BLUE}📋 日志文件: $LOG_FILE${NC}"
        echo -e "${YELLOW}💡 使用 './run.sh stop' 停止服务${NC}"
        echo -e "${YELLOW}💡 使用 './run.sh monitor' 监控服务${NC}"
    else
        echo -e "${RED}❌ 服务启动失败，请检查日志文件 $LOG_FILE${NC}"
        tail -20 "$LOG_FILE" 2>/dev/null || true
        exit 1
    fi
}

# 重启服务
restart_service() {
    echo -e "${YELLOW}🔄 重启服务...${NC}"
    stop_service
    sleep 2
    
    # 获取之前的运行模式（简单判断）
    if [[ "$1" == "prod" ]] || [[ -f "$PID_FILE.prod" ]]; then
        start_prod
    else
        echo -e "${BLUE}重启为开发模式...${NC}"
        start_dev
    fi
}

# 主逻辑
main() {
    check_dependencies
    get_config
    
    case "${1:-dev}" in
        "dev"|"development")
            start_dev
            ;;
        "prod"|"production")
            start_prod
            ;;
        "stop")
            stop_service
            ;;
        "status")
            check_status
            ;;
        "restart")
            restart_service "$2"
            ;;
        "monitor")
            monitor_service
            ;;
        "daemon")
            echo -e "${BLUE}🤖 启动监控守护进程...${NC}"
            uv run python monitor_daemon.py start 60
            ;;
        "cleanup")
            force_cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@" 