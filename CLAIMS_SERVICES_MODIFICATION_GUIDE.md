# 保险理赔关系服务修改指南

## 需要修改的文件

1. ✅ `insurance_claims_policy_relationship_service.py` - 已完成
2. 🔄 `insurance_claims_company_relationship_service.py` - 进行中
3. ⏳ `insurance_claims_company_insure_relationship_service.py` - 待修改
4. ⏳ `insurance_claims_company_insured_relationship_service.py` - 待修改

## 统一修改模式

### 1. 文件头部修改
```python
# 原来
import pandas as pd
from datetime import date, timedelta

# 修改为
from datetime import datetime
```

### 2. create_relationships 方法签名简化
```python
# 原来
def create_relationships(self, 
                        limit: Optional[int] = None,
                        where_conditions: Optional[str] = None,
                        custom_day: Optional[str] = None) -> bool:

# 修改为
def create_relationships(self, limit: Optional[int] = None) -> bool:
```

### 3. 查询方法替换
每个文件都需要将 `_query_*_data_from_hive` 方法替换为 `_query_*_data_from_neo4j` 方法。

### 4. 构建关系数据方法替换
将 `_build_relationship_data(hive_data: pd.DataFrame)` 替换为 `_build_relationship_data_from_neo4j(claims_data: List[Dict[str, Any]])`。

### 5. 创建关系方法重命名
将 `_create_relationships_directly` 重命名为更具体的名称，如 `_create_claims_*_relationships`。

### 6. 节点标签更新
- `ic:InsuranceClaims` → `claims:InsuranceClaims`
- `comp:InsuranceCompany` → `company:InsuranceCompany`
- `pc:PolicyCompany` → `company:PolicyCompany`

### 7. 添加重复关系清理方法
每个服务都需要添加 `cleanup_duplicate_relationships()` 方法。

### 8. 统计方法更新
更新 `get_statistics()` 方法中的节点标签，并简化错误处理。

### 9. 搜索方法更新
更新 `search_*_relationships()` 方法，使用统一的关系数据结构。

## 具体文件修改要点

### insurance_claims_company_relationship_service.py
- 关系类型: `ISSUED_BY`
- 匹配字段: `insurance_code` (统一社会信用代码)
- 目标节点: `InsuranceCompany`

### insurance_claims_company_insure_relationship_service.py
- 关系类型: `INSURED_BY`
- 匹配字段: `insure_name` (投保人姓名)
- 目标节点: `PolicyCompany`

### insurance_claims_company_insured_relationship_service.py
- 关系类型: `COVERS`
- 匹配字段: `insured_name` (被保险人姓名)
- 目标节点: `PolicyCompany`

## 修改后的标准方法模板

### Neo4j查询方法模板
```python
def _query_claims_*_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """从Neo4j查询保险理赔数据"""
    try:
        conditions = [
            "claims.* IS NOT NULL",
            "claims.* <> ''"
        ]
        where_clause = " AND " + " AND ".join(conditions)
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        MATCH (claims:InsuranceClaims)
        WHERE claims.case_no IS NOT NULL AND claims.case_no <> ''
        {where_clause}
        RETURN claims.case_no as case_no,
               claims.* as *,
               claims.business_id as claims_business_id
        ORDER BY claims.case_no
        {limit_clause}
        """
        
        results = self._safe_execute_query(query)
        # 处理结果...
        return valid_data
        
    except Exception as e:
        logger.error(f"查询Neo4j保险理赔数据失败: {str(e)}")
        return []
```

### 构建关系数据方法模板
```python
def _build_relationship_data_from_neo4j(self, claims_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """从Neo4j保险理赔数据构建关系数据"""
    relationship_data = []
    skipped_count = 0
    
    for claims in claims_data:
        case_no = claims.get('case_no', '')
        target_field = claims.get('target_field', '')
        
        if not case_no or not target_field:
            skipped_count += 1
            continue
        
        # 检查目标节点是否存在
        if not self._check_target_exists(target_field):
            skipped_count += 1
            continue
        
        relationship_data.append({
            'case_no': case_no,
            'target_field': target_field,
            'remarks': f"保险理赔关系: {case_no} 关联 {target_field}",
            'data_source_node': 'InsuranceClaims',
            'relationship_strength': 8,
            'relationship_type': '保险理赔关系'
        })
    
    logger.info(f"构建了 {len(relationship_data)} 个有效关系，跳过了 {skipped_count} 个无效记录")
    return relationship_data
```

### 创建关系方法模板
```python
def _create_claims_*_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
    """创建保险理赔关系"""
    try:
        batch_size = 100
        success_count = 0
        
        for i in range(0, len(relationship_data), batch_size):
            batch_data = relationship_data[i:i + batch_size]
            
            query = """
            UNWIND $relationships as rel
            MATCH (claims:InsuranceClaims {case_no: rel.case_no})
            MATCH (target:TargetNode {field: rel.target_field})
            MERGE (claims)-[r:RELATIONSHIP_TYPE]->(target)
            SET r.relationship_type = 'RELATIONSHIP_TYPE',
                r.relationship_status = 'active',
                r.relationship_strength = rel.relationship_strength,
                r.data_source_node = rel.data_source_node,
                r.remarks = rel.remarks,
                r.created_at = datetime(),
                r.updated_at = datetime()
            RETURN count(r) as created_count
            """
            
            result = self._safe_execute_query(query, {'relationships': batch_data})
            if result:
                batch_count = result[0].get('created_count', 0)
                success_count += batch_count
                logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
        
        logger.info(f"总共成功创建 {success_count} 个关系")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"创建关系失败: {str(e)}")
        return False
```

## 验证步骤

1. 语法检查：确保所有文件没有语法错误
2. 导入检查：确保所有必要的模块都已正确导入
3. 方法签名检查：确保所有方法签名都已更新
4. 节点标签检查：确保所有Cypher查询中的节点标签都已更新
5. 功能测试：验证修改后的服务能够正常工作
