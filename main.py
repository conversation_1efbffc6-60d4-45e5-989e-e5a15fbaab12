"""
BZN 关系图谱系统服务端主程序 (FastAPI版本)
"""

import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.config import get_settings
from app.utils.logger import setup_logger
from app.controllers import main_router
from app.controllers.graph.neo4j_browser import router as neo4j_browser_router
from app.services.data_storage.neo4j_simple_client import neo4j_simple_client


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    yield
    # 关闭时的清理
    try:
        neo4j_simple_client.close()
    except Exception as e:
        print(f"关闭Neo4j连接时出错: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    # 设置日志
    setup_logger()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="BZN 关系图谱系统API",
        description="重构后的关系图谱REST API服务",
        version="2.0.0",
        docs_url="/api/v1/docs",
        redoc_url="/api/v1/redoc",
        openapi_url="/api/v1/openapi.json",
        lifespan=lifespan
    )
    
    # 加载配置
    settings = get_settings()
    
    # 启用CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该限制具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 根路由
    @app.get("/")
    async def root():
        return {
            "message": "BZN 关系图谱系统服务端",
            "version": "2.0.0",
            "status": "running",
            "framework": "FastAPI",
            "docs": "/api/v1/docs"
        }
    
    @app.get("/health")
    async def basic_health():
        return {
            "status": "healthy",
            "version": "2.0.0",
            "framework": "FastAPI"
        }
    
    # 注册主路由器 (包含所有节点、关系和系统路由)
    app.include_router(main_router)
    
    # 注册图查询控制器 (独立的图查询API) - 临时注释掉，使用新的neo4j_browser代替
    # app.include_router(graph_query_router)
    
    # 注册Neo4j Browser风格的查询API
    app.include_router(neo4j_browser_router)

    return app


# 创建全局app实例供uvicorn使用
app = create_app()


def main():
    """主函数"""
    settings = get_settings()

    # 使用uvicorn运行FastAPI应用
    uvicorn.run(
        app,
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.flask_debug,  # 开发模式下启用自动重载
        workers=1 if settings.flask_debug else 4,  # 生产环境使用多进程
        # 移除请求限制，避免200请求后自动终止
        limit_max_requests=None,
        limit_concurrency=None
    )


if __name__ == "__main__":
    main() 