#!/usr/bin/env python3
"""
区域数据导入脚本

从Hive提取区域数据并导入到Neo4j图数据库
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.node_service.area_service import AreaService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='区域数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("区域")
    
    # 打印头部信息
    helper.print_header("区域数据导入")
    
    try:
        # 创建服务实例
        service = AreaService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "区域数据"):
            helper.execute_with_progress(
                "清理现有区域数据",
                service.delete_all_areas
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并存储数据
        result = helper.execute_with_progress(
            "从Hive提取并存储区域数据",
            service.extract_and_store_areas
        )
        
        # 步骤3: 显示导入结果
        if isinstance(result, dict):
            helper.print_import_result(result, "区域数据导入")
        else:
            # 处理布尔返回值
            if result:
                print("\n=== 区域数据导入结果 ===")
                print("✅ 状态: 成功")
                
                # 尝试获取统计信息
                try:
                    stats = service.get_statistics()
                    total_count = stats.get('total_count', 0)
                    print(f"区域总数: {total_count}")
                except Exception as e:
                    print(f"获取统计信息失败: {str(e)}")
            else:
                print("\n=== 区域数据导入结果 ===")
                print("❌ 状态: 失败")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 区域数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 