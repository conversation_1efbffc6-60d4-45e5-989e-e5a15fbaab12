#!/usr/bin/env python3
"""
机构行业关系导入脚本

从Neo4j中的Organization节点的industry_medium属性关联到NationalStandardIndustry节点
注意：专门匹配中类（Level 3）的国家标准行业，避免重名问题
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.organization_industry_relationship_service import OrganizationIndustryRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='机构行业关系导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("机构行业关系")
    
    # 打印头部信息
    helper.print_header("机构行业关系导入")
    
    try:
        # 创建关系服务实例
        relationship_service = OrganizationIndustryRelationshipService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "机构行业关系"):
            helper.execute_with_progress(
                "清理现有机构行业关系",
                relationship_service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 创建关系数据
        result = helper.execute_with_progress(
            "创建机构行业关系",
            lambda: relationship_service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if result:
            print("\n=== 机构行业关系导入结果 ===")
            print("✅ 状态: 成功")
            
            # 尝试获取统计信息
            try:
                stats = relationship_service.get_statistics()
                total_count = stats.get('total_count', 0)
                unmatched_count = stats.get('unmatched_organizations', 0)
                print(f"机构行业关系总数: {total_count}")
                print(f"未匹配机构数量: {unmatched_count}")
            except Exception as e:
                print(f"获取统计信息失败: {str(e)}")
        else:
            print("\n=== 机构行业关系导入结果 ===")
            print("❌ 状态: 失败")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ 机构行业关系导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 