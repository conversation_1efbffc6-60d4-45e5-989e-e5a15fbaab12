#!/usr/bin/env python3
"""
被保险人与保单关系导入脚本

根据被保险人节点的data_source_id属性与保单节点的policy_code属性建立关系
用法: python scripts/data_import/import_insured_person_policy_relationships.py
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insured_person_policy_relationship_service import InsuredPersonPolicyRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='被保险人与保单关系导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("被保险人与保单关系")
    
    # 打印头部信息
    helper.print_header("被保险人与保单关系导入")
    
    try:
        # 创建关系服务实例
        service = InsuredPersonPolicyRelationshipService()
        
        # 步骤1: 确认清理现有关系
        if helper.confirm_overwrite(args.force, "被保险人与保单关系"):
            helper.execute_with_progress(
                "清理现有被保险人与保单关系",
                service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 创建被保险人与保单关系
        result = helper.execute_with_progress(
            "从Neo4j提取被保险人数据并创建与保单的关系",
            lambda: service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if isinstance(result, dict):
            helper.print_import_result(result, "被保险人与保单关系导入")
        else:
            # 处理布尔返回值
            if result:
                print("\n=== 被保险人与保单关系导入结果 ===")
                print("✅ 状态: 成功")
                
                # 尝试获取统计信息
                try:
                    stats = service.get_statistics()
                    total_count = stats.get('total_count', 0)
                    print(f"关系总数: {total_count}")
                    
                    additional_stats = stats.get('additional_stats', {})
                    if additional_stats:
                        insured_count = additional_stats.get('insured_persons_with_policy', 0)
                        policy_count = additional_stats.get('policies_with_insured', 0)
                        print(f"有保单关系的被保险人数: {insured_count}")
                        print(f"有被保险人关系的保单数: {policy_count}")
                        
                        # 显示数据源分布
                        by_data_source = additional_stats.get('by_data_source_node', {})
                        if by_data_source:
                            print("按数据源分布:")
                            for source, count in by_data_source.items():
                                print(f"  {source}: {count}个关系")
                except Exception as e:
                    print(f"获取统计信息失败: {str(e)}")
            else:
                print("\n=== 被保险人与保单关系导入结果 ===")
                print("❌ 状态: 失败")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 被保险人与保单关系导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 