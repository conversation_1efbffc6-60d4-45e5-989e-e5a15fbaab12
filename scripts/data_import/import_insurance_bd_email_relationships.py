#!/usr/bin/env python3
"""
保险商务拓展人员邮箱关系数据导入脚本

从Neo4j提取保险商务拓展人员的email属性，创建Email节点并建立关系
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_bd_email_relationship_service import InsuranceBDEmailRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='保险商务拓展人员邮箱关系数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("保险商务拓展人员邮箱关系")
    
    # 打印头部信息
    helper.print_header("保险商务拓展人员邮箱关系数据导入")
    
    try:
        # 创建服务实例
        service = InsuranceBDEmailRelationshipService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "保险商务拓展人员邮箱关系数据"):
            helper.execute_with_progress(
                "清理现有保险商务拓展人员邮箱关系数据",
                service.delete_all_relationships
            )
            
            # 清理孤立的Email节点
            helper.execute_with_progress(
                "清理孤立的Email节点",
                service.delete_orphaned_emails
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并创建关系
        result = helper.execute_with_progress(
            "从Neo4j提取商务拓展人员数据并创建邮箱关系",
            lambda: service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if isinstance(result, dict):
            helper.print_import_result(result, "保险商务拓展人员邮箱关系数据导入")
        else:
            # 处理布尔返回值
            if result:
                print("\n=== 保险商务拓展人员邮箱关系数据导入结果 ===")
                print("✅ 状态: 成功")
                
                # 尝试获取统计信息
                try:
                    stats = service.get_statistics()
                    total_count = stats.get('total_count', 0)
                    print(f"关系总数: {total_count}")
                    
                    additional_stats = stats.get('additional_stats', {})
                    if additional_stats:
                        bds_with_email = additional_stats.get('bds_with_email', 0)
                        print(f"有邮箱的商务拓展人员数量: {bds_with_email}")
                        
                        total_emails = additional_stats.get('total_emails', 0)
                        print(f"Email节点总数: {total_emails}")
                        
                        duplicate_codes = additional_stats.get('duplicate_email_codes', 0)
                        duplicate_nodes = additional_stats.get('duplicate_email_nodes', 0)
                        if duplicate_codes > 0:
                            print(f"⚠️  发现 {duplicate_codes} 个重复邮箱地址，共 {duplicate_nodes} 个重复节点")
                            print("建议运行清理脚本: python scripts/cleanup_duplicate_emails.py")
                except Exception as e:
                    print(f"获取统计信息失败: {str(e)}")
            else:
                print("\n=== 保险商务拓展人员邮箱关系数据导入结果 ===")
                print("❌ 状态: 失败")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 保险商务拓展人员邮箱关系数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 