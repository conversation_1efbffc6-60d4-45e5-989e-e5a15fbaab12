#!/usr/bin/env python3
"""
保险理赔与被保险人关系数据导入脚本

从Neo4j提取保险理赔与被保险人关系数据并建立关联关系
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_claims_insured_person_relationship_service import InsuranceClaimsInsuredPersonRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='保险理赔与被保险人关系数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    parser.add_argument('--show-unmatched', action='store_true', help='显示未匹配的理赔记录')
    parser.add_argument('--show-stats', action='store_true', help='显示当前统计信息')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("保险理赔与被保险人关系")
    
    # 创建服务实例
    service = InsuranceClaimsInsuredPersonRelationshipService()
    
    # 如果只显示统计信息
    if args.show_stats:
        helper.print_header("保险理赔与被保险人关系统计")
        try:
            stats = service.get_statistics()
            if stats:
                print("=== 保险理赔与被保险人关系统计 ===")
                
                # 基本统计信息
                total_count = stats.get('total_count', 0)
                additional_stats = stats.get('additional_stats', {})
                
                print(f"关系总数: {total_count}")
                print(f"有关联被保险人的理赔案件: {additional_stats.get('claims_with_insured_person', 0)}")
                print(f"涉及理赔的被保险人数量: {additional_stats.get('insured_persons_with_claims', 0)}")
                print(f"未匹配的理赔记录: {additional_stats.get('unmatched_claims', 0)}")
                
                # 多次理赔统计
                multiple_claims = additional_stats.get('persons_with_multiple_claims', 0)
                if multiple_claims > 0:
                    print(f"\n📈 多次理赔统计:")
                    print(f"  有多次理赔的被保险人: {multiple_claims}")
                    print(f"  单人最多理赔次数: {additional_stats.get('max_claims_per_person', 0)}")
                    print(f"  平均理赔次数: {additional_stats.get('avg_claims_per_person', 0.0):.1f}")
                    
                # 数据源统计
                source_stats = additional_stats.get('by_data_source_node', {})
                if source_stats:
                    print(f"\n📋 数据源分布:")
                    for source, count in source_stats.items():
                        print(f"  {source}: {count} 个关系")
            else:
                print("无法获取统计信息")
        except Exception as e:
            print(f"获取统计信息失败: {str(e)}")
        return
    
    # 如果显示未匹配记录
    if args.show_unmatched:
        helper.print_header("未匹配的理赔记录检查")
        try:
            unmatched_claims = service.check_unmatched_claims(limit=50)
            
            if unmatched_claims:
                print(f"找到 {len(unmatched_claims)} 个未匹配的理赔记录（前50个）：")
                for i, claim in enumerate(unmatched_claims, 1):
                    print(f"{i:3d}. 案件号: {claim['case_no']}")
                    print(f"      证件号: {claim['insured_user_cert_no']}")
                    print(f"      姓名: {claim['insured_user_name']}")
                    print(f"      保单号: {claim['policy_code']}")
                    print()
            else:
                print("未找到未匹配的理赔记录")
        except Exception as e:
            print(f"检查未匹配记录失败: {str(e)}")
        return
    
    # 打印头部信息
    helper.print_header("保险理赔与被保险人关系数据导入")
    
    try:
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "保险理赔与被保险人关系数据"):
            helper.execute_with_progress(
                "清理现有保险理赔与被保险人关系数据",
                service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并存储数据
        success = helper.execute_with_progress(
            "从Neo4j提取并存储保险理赔与被保险人关系数据",
            lambda: service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if success:
            print("\n=== 保险理赔与被保险人关系数据导入结果 ===")
            print("✅ 状态: 成功")
            
            # 获取统计信息
            try:
                stats = service.get_statistics()
                total_count = stats.get('total_count', 0)
                additional_stats = stats.get('additional_stats', {})
                
                print(f"关系总数: {total_count}")
                
                # 显示基本统计信息
                claims_count = additional_stats.get('claims_with_insured_person', 0)
                persons_count = additional_stats.get('insured_persons_with_claims', 0)
                unmatched_count = additional_stats.get('unmatched_claims', 0)
                
                print(f"有关联被保险人的理赔案件: {claims_count}")
                print(f"涉及理赔的被保险人数量: {persons_count}")
                print(f"未匹配的理赔记录: {unmatched_count}")
                
                # 显示多次理赔统计
                multiple_claims = additional_stats.get('persons_with_multiple_claims', 0)
                max_claims = additional_stats.get('max_claims_per_person', 0)
                avg_claims = additional_stats.get('avg_claims_per_person', 0)
                
                if multiple_claims > 0:
                    print("\n📈 多次理赔被保险人统计:")
                    print(f"  有多次理赔的被保险人: {multiple_claims} 人")
                    print(f"  单人最多理赔次数: {max_claims} 次")
                    print(f"  平均理赔次数: {avg_claims:.2f} 次")
                
                # 显示关系强度统计
                strength_stats = additional_stats.get('by_relationship_strength', {})
                if strength_stats:
                    print("\n💪 关系强度分布:")
                    for strength, count in strength_stats.items():
                        print(f"  强度 {strength}: {count} 个关系")
                
                # 显示数据源统计
                source_stats = additional_stats.get('by_data_source_node', {})
                if source_stats:
                    print("\n📋 数据源分布:")
                    for source, count in source_stats.items():
                        print(f"  {source}: {count} 个关系")
                
                # 提示未匹配信息
                if unmatched_count > 0:
                    print(f"\n⚠️  提示: 发现 {unmatched_count} 个未匹配的理赔记录")
                    print("   可使用 --show-unmatched 参数查看详情")
                        
            except Exception as e:
                print(f"获取统计信息失败: {str(e)}")
        else:
            print("\n=== 保险理赔与被保险人关系数据导入结果 ===")
            print("❌ 状态: 失败")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ 保险理赔与被保险人关系数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 