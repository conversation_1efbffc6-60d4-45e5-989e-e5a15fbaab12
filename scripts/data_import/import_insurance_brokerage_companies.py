#!/usr/bin/env python3
"""
保险经纪公司数据导入脚本

从Hive数据库提取保险经纪公司数据并导入到Neo4j关系图谱中
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.node_service.insurance_brokerage_company_service import InsuranceBrokerageCompanyService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='保险经纪公司数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("保险经纪公司")
    
    # 打印头部信息
    helper.print_header("保险经纪公司数据导入")
    
    try:
        # 创建服务实例
        service = InsuranceBrokerageCompanyService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "保险经纪公司数据"):
            helper.execute_with_progress(
                "清理现有保险经纪公司数据",
                service.delete_all_companies
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并存储数据
        result = helper.execute_with_progress(
            "提取并存储保险经纪公司数据",
            service.extract_and_store_companies
        )
        
        # 步骤3: 显示导入结果
        if isinstance(result, dict):
            helper.print_import_result(result, "保险经纪公司数据导入")
        else:
            # 处理布尔返回值
            if result:
                print("\n=== 保险经纪公司数据导入结果 ===")
                print("✅ 状态: 成功")
                
                # 尝试获取统计信息
                try:
                    stats = service.get_statistics()
                    total_count = stats.get('total_count', 0)
                    print(f"经纪公司总数: {total_count}")
                except Exception as e:
                    print(f"获取统计信息失败: {str(e)}")
            else:
                print("\n=== 保险经纪公司数据导入结果 ===")
                print("❌ 状态: 失败")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 保险经纪公司数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main()
