#!/usr/bin/env python3
"""
国家标准行业层级关系导入脚本

从Neo4j中的NationalStandardIndustry节点的parent_code属性创建层级关系
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.national_standard_industry_hierarchy_relationship_service import NationalStandardIndustryHierarchyRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='国家标准行业层级关系导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("国家标准行业层级关系")
    
    # 打印头部信息
    helper.print_header("国家标准行业层级关系导入")
    
    try:
        # 创建关系服务实例
        relationship_service = NationalStandardIndustryHierarchyRelationshipService()
        
        # 步骤1: 确认清理现有关系
        if helper.confirm_overwrite(args.force, "国家标准行业层级关系"):
            helper.execute_with_progress(
                "清理现有国家标准行业层级关系",
                relationship_service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 创建层级关系
        result = helper.execute_with_progress(
            "创建国家标准行业层级关系",
            lambda: relationship_service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if result:
            print("\n=== 国家标准行业层级关系导入结果 ===")
            print("✅ 状态: 成功")
            
            # 尝试获取统计信息
            try:
                stats = relationship_service.get_statistics()
                total_count = stats.get('total_count', 0)
                by_child_level = stats.get('by_child_level', {})
                orphan_nodes = stats.get('orphan_nodes', 0)
                root_nodes = stats.get('root_nodes', 0)
                
                print(f"层级关系总数: {total_count}")
                print(f"根节点数量: {root_nodes}")
                print(f"孤立节点数量: {orphan_nodes}")
                
                if by_child_level:
                    print("按子节点层级分布:")
                    level_names = {1: "门类", 2: "大类", 3: "中类", 4: "小类"}
                    for level, count in sorted(by_child_level.items()):
                        level_name = level_names.get(level, f"第{level}级")
                        print(f"  {level_name}子节点: {count}个关系")
                        
            except Exception as e:
                print(f"获取统计信息失败: {str(e)}")
        else:
            print("\n=== 国家标准行业层级关系导入结果 ===")
            print("❌ 状态: 失败")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ 国家标准行业层级关系导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 