#!/usr/bin/env python3
"""
特殊名单与组织机构关系数据导入脚本

从Neo4j提取特殊名单的code属性，关联Organization节点并建立黑名单关系
处理条件：list_category = 2（企业）&& list_type = 1（黑名单）
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.special_list_organization_relationship_service import SpecialListOrganizationRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='特殊名单与组织机构关系数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("特殊名单与组织机构关系")
    
    # 打印头部信息
    helper.print_header("特殊名单与组织机构关系数据导入")
    
    try:
        # 创建服务实例
        service = SpecialListOrganizationRelationshipService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "特殊名单与组织机构关系数据"):
            helper.execute_with_progress(
                "清理现有特殊名单与组织机构关系数据",
                service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并创建关系
        result = helper.execute_with_progress(
            "从Neo4j提取特殊名单数据并创建与组织机构的关系",
            lambda: service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if isinstance(result, dict):
            helper.print_import_result(result, "特殊名单与组织机构关系数据导入")
        else:
            # 处理布尔返回值
            if result:
                print("\n=== 特殊名单与组织机构关系数据导入结果 ===")
                print("✅ 状态: 成功")
                
                # 尝试获取统计信息
                try:
                    stats = service.get_statistics()
                    total_count = stats.get('total_count', 0)
                    print(f"关系总数: {total_count}")
                    
                    additional_stats = stats.get('additional_stats', {})
                    if additional_stats:
                        blacklisted_orgs = additional_stats.get('blacklisted_organizations', 0)
                        special_list_entries = additional_stats.get('special_list_entries', 0)
                        print(f"黑名单企业数量: {blacklisted_orgs}")
                        print(f"特殊名单条目数量: {special_list_entries}")
                        
                        # 风险等级分布
                        risk_level_stats = additional_stats.get('by_risk_level', {})
                        if risk_level_stats:
                            print("风险等级分布:")
                            for level, count in risk_level_stats.items():
                                print(f"  {level}: {count}")
                        
                        # 状态分布
                        status_stats = additional_stats.get('by_status', {})
                        if status_stats:
                            print("状态分布:")
                            for status, count in status_stats.items():
                                print(f"  {status}: {count}")
                                
                except Exception as e:
                    print(f"获取统计信息失败: {str(e)}")
            else:
                print("\n=== 特殊名单与组织机构关系数据导入结果 ===")
                print("❌ 状态: 失败")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 特殊名单与组织机构关系数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 