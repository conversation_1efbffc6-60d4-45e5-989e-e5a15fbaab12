#!/usr/bin/env python3
"""
保险理赔公司关系数据导入脚本

从Hive提取保险理赔与保险公司关系数据并导入到Neo4j图数据库
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_claims_company_relationship_service import InsuranceClaimsCompanyRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='保险理赔公司关系数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    parser.add_argument('--limit', type=int, help='限制导入记录数，仅用于测试')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("保险理赔公司关系")
    
    # 打印头部信息
    helper.print_header("保险理赔公司关系数据导入")
    
    try:
        # 创建服务实例
        service = InsuranceClaimsCompanyRelationshipService()
        
        # 步骤1: 确认清理现有数据
        if helper.confirm_overwrite(args.force, "保险理赔公司关系数据"):
            helper.execute_with_progress(
                "清理现有保险理赔公司关系数据",
                service.delete_all_relationships
            )
        else:
            print("用户取消操作")
            return
        
        # 步骤2: 提取并存储数据
        success = helper.execute_with_progress(
            "从Hive提取并存储保险理赔公司关系数据",
            lambda: service.create_relationships(limit=args.limit)
        )
        
        # 步骤3: 显示导入结果
        if success:
            print("\n=== 保险理赔公司关系数据导入结果 ===")
            print("✅ 状态: 成功")
            
            # 获取统计信息
            try:
                stats = service.get_statistics()
                total_count = stats.get('total_count', 0)
                additional_stats = stats.get('additional_stats', {})
                
                print(f"关系总数: {total_count}")
                
                # 显示按保险公司统计（Top 5）
                company_stats = additional_stats.get('by_company_name', {})
                if company_stats:
                    print("\n📊 主要保险公司理赔统计:")
                    for i, (company, count) in enumerate(list(company_stats.items())[:5], 1):
                        print(f"  {i}. {company}: {count} 个理赔案件")
                
                # 显示按保险公司代码统计（Top 3）
                code_stats = additional_stats.get('by_insurance_code', {})
                if code_stats:
                    print("\n🏢 主要保险公司代码统计:")
                    for i, (code, count) in enumerate(list(code_stats.items())[:3], 1):
                        print(f"  {i}. {code}: {count} 个理赔案件")
                
                # 显示关系强度统计
                strength_stats = additional_stats.get('by_relationship_strength', {})
                if strength_stats:
                    print("\n💪 关系强度分布:")
                    for strength, count in strength_stats.items():
                        print(f"  强度 {strength}: {count} 个关系")
                        
            except Exception as e:
                print(f"获取统计信息失败: {str(e)}")
        else:
            print("\n=== 保险理赔公司关系数据导入结果 ===")
            print("❌ 状态: 失败")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ 保险理赔公司关系数据导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 