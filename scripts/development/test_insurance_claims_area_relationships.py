#!/usr/bin/env python3
"""
测试保险理赔区域关系功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_claims_area_relationship_service import InsuranceClaimsAreaRelationshipService


def test_claims_area_relationships():
    """测试保险理赔区域关系功能"""
    print("🔄 开始测试保险理赔区域关系功能...\n")
    
    try:
        # 创建服务实例
        service = InsuranceClaimsAreaRelationshipService()
        
        # 测试1: 从Hive查询保险理赔区域数据（限制10条）
        print("📊 测试1: 从Hive查询保险理赔区域数据（限制10条）")
        hive_data = service._query_claims_area_data_from_hive(limit=10)
        if not hive_data.empty:
            print(f"✅ 成功查询到 {len(hive_data)} 条记录")
            print(f"📋 数据列: {list(hive_data.columns)}")
            print(f"🔍 示例数据:")
            for i, row in hive_data.head(3).iterrows():
                case_no = row.get('case_no', 'N/A')
                risk_province = row.get('risk_province', 'N/A')
                risk_city = row.get('risk_city', 'N/A')
                risk_district = row.get('risk_district', 'N/A')
                print(f"  案件{i+1}: {case_no} - {risk_province}/{risk_city}/{risk_district}")
        else:
            print("⚠️  未查询到数据")
        
        print("\n" + "="*60 + "\n")
        
        # 测试2: 构建关系数据
        print("📊 测试2: 构建关系数据")
        if not hive_data.empty:
            relationship_data = service._build_relationship_data(hive_data)
            print(f"✅ 成功构建 {len(relationship_data)} 个关系")
            if relationship_data:
                print("🔍 示例关系数据:")
                for i, rel in enumerate(relationship_data[:3], 1):
                    print(f"  关系{i}: {rel['case_no']} -> {rel['area_name']}({rel['area_code']}) [{rel['location_type']}]")
        else:
            print("⚠️  无数据可构建关系")
        
        print("\n" + "="*60 + "\n")
        
        # 测试3: 获取现有关系统计
        print("📊 测试3: 获取现有关系统计")
        stats = service.get_relationship_statistics()
        print(f"✅ 统计信息获取成功:")
        print(f"  总关系数: {stats.get('total_count', 0)}")
        print(f"  关系类型: {stats.get('relationship_type', 'N/A')}")
        
        # 显示详细统计
        additional_stats = stats.get('additional_stats', {})
        if additional_stats:
            location_stats = additional_stats.get('by_location_type', {})
            if location_stats:
                print(f"  按位置类型分布:")
                for loc_type, count in location_stats.items():
                    print(f"    {loc_type}: {count}")
        
        print("\n" + "="*60 + "\n")
        
        # 测试4: 搜索关系（如果有数据的话）
        print("📊 测试4: 搜索保险理赔区域关系")
        search_results = service.search_claims_area_relationships(limit=5)
        print(f"✅ 搜索到 {len(search_results)} 个关系")
        if search_results:
            print("🔍 示例搜索结果:")
            for i, rel in enumerate(search_results[:3], 1):
                case_no = rel.get('case_no', 'N/A')
                area_name = rel.get('area_name', 'N/A')
                location_type = rel.get('location_type', 'N/A')
                print(f"  关系{i}: {case_no} -> {area_name} [{location_type}]")
        
        print("\n" + "="*60 + "\n")
        
        # 测试5: 小批量创建关系（如果有新数据）
        print("📊 测试5: 小批量创建关系测试")
        if not hive_data.empty and len(relationship_data) > 0:
            # 只测试前3个关系的创建
            test_data = relationship_data[:3]
            print(f"🔧 尝试创建 {len(test_data)} 个测试关系...")
            success = service._create_relationships_directly(test_data)
            if success:
                print("✅ 关系创建测试成功")
                # 重新获取统计信息
                new_stats = service.get_relationship_statistics()
                print(f"📊 更新后总关系数: {new_stats.get('total_count', 0)}")
            else:
                print("❌ 关系创建测试失败")
        else:
            print("⚠️  无测试数据可创建关系")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 保险理赔区域关系功能测试完成！")
    return True


if __name__ == "__main__":
    test_claims_area_relationships() 