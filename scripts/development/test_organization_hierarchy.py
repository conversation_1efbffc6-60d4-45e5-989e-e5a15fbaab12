#!/usr/bin/env python3
"""
测试组织层次架构脚本

验证保险公司和保险经纪公司的组织继承关系，以及多标签查询功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.node_service.organization_service import OrganizationService
from app.services.node_service.insurance_company_service import InsuranceCompanyService  
from app.services.node_service.insurance_brokerage_company_service import InsuranceBrokerageCompanyService
from app.utils.logger import get_logger

logger = get_logger(__name__)


def test_organization_hierarchy():
    """测试组织层次架构"""
    
    logger.info("开始测试组织层次架构")
    
    try:
        # 创建服务实例
        org_service = OrganizationService()
        insurance_company_service = InsuranceCompanyService()
        brokerage_service = InsuranceBrokerageCompanyService()
        
        print("=" * 60)
        print("BZN 关系图谱系统 - 组织层次架构测试")
        print("=" * 60)
        
        # 测试1: 获取组织统计信息
        print("\n=== 测试1: 组织统计信息 ===")
        stats = org_service.get_organization_statistics()
        print(f"组织统计结果: {stats}")
        
        # 测试2: 查询所有组织
        print("\n=== 测试2: 查询所有组织 ===")
        all_orgs = org_service.search_all_organizations(limit=5)
        print(f"找到 {len(all_orgs)} 个组织")
        
        for i, org in enumerate(all_orgs[:3], 1):
            print(f"\n组织{i}:")
            print(f"  名称: {org.get('name', 'N/A')}")
            print(f"  简称: {org.get('short_name', 'N/A')}")
            print(f"  统一社会信用代码: {org.get('unified_social_credit_code', 'N/A')}")
            print(f"  节点标签: {org.get('node_labels', [])}")
            print(f"  业务ID: {org.get('business_id', 'N/A')}")
        
        # 测试3: 按类型查询组织
        print("\n=== 测试3: 按类型查询组织 ===")
        
        # 查询保险公司
        insurance_companies = org_service.get_organizations_by_type("InsuranceCompany", limit=3)
        print(f"\n保险公司 (共{len(insurance_companies)}个):")
        for i, company in enumerate(insurance_companies[:2], 1):
            print(f"  {i}. {company.get('name', 'N/A')} - 标签: {company.get('node_labels', [])}")
        
        # 查询保险经纪公司
        brokerage_companies = org_service.get_organizations_by_type("InsuranceBrokerageCompany", limit=3)
        print(f"\n保险经纪公司 (共{len(brokerage_companies)}个):")
        for i, company in enumerate(brokerage_companies[:2], 1):
            print(f"  {i}. {company.get('name', 'N/A')} - 标签: {company.get('node_labels', [])}")
        
        # 测试4: 验证多标签查询
        print("\n=== 测试4: 验证多标签查询 ===")
        
        # 使用原有服务查询
        old_insurance_companies = insurance_company_service.search_companies(limit=3)
        print(f"原保险公司服务查询结果: {len(old_insurance_companies)} 个")
        
        old_brokerage_companies = brokerage_service.search_companies(limit=3)
        print(f"原保险经纪公司服务查询结果: {len(old_brokerage_companies)} 个")
        
        # 测试5: 按名称搜索组织
        print("\n=== 测试5: 按名称搜索组织 ===")
        
        # 搜索包含"保险"的组织
        search_results = org_service.search_all_organizations(name_filter="保险", limit=5)
        print(f"搜索'保险'相关组织: 找到 {len(search_results)} 个")
        
        for i, org in enumerate(search_results[:3], 1):
            print(f"  {i}. {org.get('name', 'N/A')} - 类型: {org.get('node_labels', [])}")
        
        # 测试6: 验证标签继承关系
        print("\n=== 测试6: 验证标签继承关系 ===")
        
        if all_orgs:
            # 取第一个组织验证其标签
            first_org = all_orgs[0]
            labels = first_org.get('node_labels', [])
            
            print(f"组织示例: {first_org.get('name', 'N/A')}")
            print(f"标签列表: {labels}")
            
            has_organization_label = "Organization" in labels
            has_specific_label = any(label in ["InsuranceCompany", "InsuranceBrokerageCompany"] for label in labels)
            
            print(f"✅ 包含基础标签 'Organization': {has_organization_label}")
            print(f"✅ 包含具体类型标签: {has_specific_label}")
            
            if has_organization_label and has_specific_label:
                print("🎉 标签继承关系验证成功！")
            else:
                print("❌ 标签继承关系验证失败！")
        
        print("\n" + "=" * 60)
        print("组织层次架构测试完成")
        
        logger.info("组织层次架构测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise


def test_cypher_queries():
    """测试Cypher查询语句"""
    
    print("\n=== 补充测试: 直接Cypher查询 ===")
    
    try:
        from app.services.data_storage.neo4j_storage import Neo4jDataStorage
        
        storage = Neo4jDataStorage()
        
        # 测试1: 查询所有组织标签
        print("\n1. 查询所有组织的标签分布:")
        query1 = """
        MATCH (n:Organization)
        WITH labels(n) as node_labels, count(*) as count
        RETURN node_labels, count
        ORDER BY count DESC
        """
        
        results1 = storage.execute_query(query1)
        for record in results1:
            print(f"   标签: {record['node_labels']} - 数量: {record['count']}")
        
        # 测试2: 验证多标签查询
        print("\n2. 验证多标签查询:")
        query2 = """
        MATCH (n:Organization:InsuranceCompany)
        RETURN count(n) as insurance_company_count
        """
        
        results2 = storage.execute_query(query2)
        if results2:
            print(f"   Organization:InsuranceCompany 查询结果: {results2[0]['insurance_company_count']} 个")
        
        query3 = """
        MATCH (n:Organization:InsuranceBrokerageCompany)
        RETURN count(n) as brokerage_company_count
        """
        
        results3 = storage.execute_query(query3)
        if results3:
            print(f"   Organization:InsuranceBrokerageCompany 查询结果: {results3[0]['brokerage_company_count']} 个")
        
        # 测试3: 统一查询所有组织
        print("\n3. 统一查询所有组织:")
        query4 = """
        MATCH (n:Organization)
        RETURN count(n) as total_organizations
        """
        
        results4 = storage.execute_query(query4)
        if results4:
            print(f"   所有组织总数: {results4[0]['total_organizations']} 个")
        
    except Exception as e:
        print(f"   Cypher查询测试失败: {str(e)}")


if __name__ == "__main__":
    print("开始组织层次架构测试...")
    
    test_organization_hierarchy()
    test_cypher_queries()
    
    print("\n测试完成！") 