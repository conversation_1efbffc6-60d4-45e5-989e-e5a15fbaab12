"""
测试保险销售渠道商务拓展人员关系功能

验证从Hive数据源提取和创建关系的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_sales_channel_bd_relationship_service import InsuranceSalesChannelBDRelationshipService


def main():
    """测试保险销售渠道商务拓展人员关系功能"""
    print("🔄 开始测试保险销售渠道商务拓展人员关系功能...\n")
    
    try:
        # 初始化服务
        service = InsuranceSalesChannelBDRelationshipService()
        
        # 测试1: 查询Hive数据（限制10条）
        print("📊 测试1: 从Hive查询保险销售渠道商务拓展人员数据（限制10条）")
        hive_data = service._query_channel_bd_data_from_hive(limit=10)
        
        if hive_data is not None and not hive_data.empty:
            print(f"✅ 成功查询到 {len(hive_data)} 条数据")
            print("🔍 数据样例:")
            for i, row in hive_data.head(3).iterrows():
                print(f"  - 渠道: {row.get('org_name')} (ID: {row.get('id')}) "
                      f"BD: {row.get('bd_user_id')} 类型: {row.get('org_type')}")
        else:
            print("⚠️ 未查询到有效数据")
            return
        
        # 测试2: 提取编码
        print(f"\n📋 测试2: 提取渠道和商务拓展人员编码")
        channel_codes, bd_codes = service._extract_codes_from_data(hive_data)
        print(f"✅ 提取到 {len(channel_codes)} 个渠道编码，{len(bd_codes)} 个商务拓展人员编码")
        
        # 测试3: 检查节点存在性
        print(f"\n🔍 测试3: 检查节点存在性")
        existing_channels = service.check_nodes_exist("InsuranceSalesChannel", channel_codes[:5])
        existing_bds = service.check_nodes_exist("InsuranceBDPerson", bd_codes[:5])
        print(f"✅ 找到 {len(existing_channels)} 个现有渠道节点，{len(existing_bds)} 个现有BD节点")
        
        # 测试4: 获取关系统计
        print(f"\n📈 测试4: 获取现有关系统计")
        stats = service.get_relationship_statistics()
        if stats:
            print(f"✅ 当前关系总数: {stats.get('total_relationships', 0)}")
            print(f"📊 按组织类型统计: {stats.get('by_org_type', {})}")
        else:
            print("ℹ️ 当前无关系数据")
        
        # 测试5: 搜索现有关系
        print(f"\n🔎 测试5: 搜索现有关系（限制5条）")
        relationships = service.search_bd_relationships(limit=5)
        if relationships:
            print(f"✅ 找到 {len(relationships)} 个关系")
            for i, rel in enumerate(relationships[:3], 1):
                channel_info = rel.get('channel', {})
                bd_info = rel.get('bd', {})
                print(f"  {i}. {channel_info.get('name', 'N/A')} -> {bd_info.get('name', 'N/A')}")
        else:
            print("ℹ️ 当前无关系数据")
        
        # 测试6: 小批量创建关系（测试模式）
        print(f"\n🚀 测试6: 小批量创建关系（测试模式，限制5条）")
        result = service.create_relationships(limit=5)
        if result:
            print("✅ 小批量关系创建测试成功")
            
            # 获取更新后的统计
            updated_stats = service.get_relationship_statistics()
            if updated_stats:
                print(f"📊 更新后关系总数: {updated_stats.get('total_relationships', 0)}")
        else:
            print("⚠️ 小批量关系创建测试失败")
        
        print(f"\n🎉 保险销售渠道商务拓展人员关系功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 