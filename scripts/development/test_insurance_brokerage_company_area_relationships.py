#!/usr/bin/env python3
"""
保险经纪公司区域关系功能综合测试脚本

测试优化后的所有功能，包括：
1. 验证逻辑统一性
2. 搜索功能稳定性
3. 错误处理机制
4. 统计信息格式一致性
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insurance_brokerage_company_area_relationship_service import InsuranceBrokerageCompanyAreaRelationshipService
from app.utils.import_helper import ImportHelper


def test_statistics_format():
    """测试统计信息格式的一致性"""
    print("🔍 测试统计信息格式...")
    
    service = InsuranceBrokerageCompanyAreaRelationshipService()
    
    try:
        stats = service.get_relationship_statistics()
        
        # 检查必要的字段
        required_fields = ['total_count', 'total_relationships', 'relationship_type']
        missing_fields = []
        
        for field in required_fields:
            if field not in stats:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 统计信息缺少必要字段: {missing_fields}")
            return False
        
        # 检查字段值的一致性
        if stats['total_count'] != stats['total_relationships']:
            print(f"❌ 统计字段值不一致: total_count={stats['total_count']}, total_relationships={stats['total_relationships']}")
            return False
        
        print(f"✅ 统计信息格式正确，总关系数: {stats['total_count']}")
        return True
        
    except Exception as e:
        print(f"❌ 统计信息测试失败: {str(e)}")
        return False


def test_import_helper_validation():
    """测试ImportHelper的验证逻辑"""
    print("🔍 测试ImportHelper验证逻辑...")
    
    service = InsuranceBrokerageCompanyAreaRelationshipService()
    helper = ImportHelper("保险经纪公司区域关系测试")
    
    try:
        # 测试验证逻辑
        result = helper.validate_import(service.get_relationship_statistics, min_count=0)
        
        if result:
            print("✅ ImportHelper验证逻辑正常")
            return True
        else:
            print("❌ ImportHelper验证失败")
            return False
        
    except Exception as e:
        print(f"❌ ImportHelper验证测试失败: {str(e)}")
        return False


def test_search_functionality():
    """测试搜索功能的稳定性"""
    print("🔍 测试搜索功能...")
    
    service = InsuranceBrokerageCompanyAreaRelationshipService()
    
    try:
        # 测试基本搜索
        relationships = service.search_location_relationships(limit=5)
        print(f"✅ 基本搜索成功，返回 {len(relationships)} 条记录")
        
        # 测试带条件搜索
        filtered_relationships = service.search_location_relationships(
            location_type='district',
            limit=3
        )
        print(f"✅ 条件搜索成功，返回 {len(filtered_relationships)} 条记录")
        
        # 测试搜索结果格式
        if relationships:
            first_record = relationships[0]
            expected_fields = ['location_type', 'area_name', 'area_code']
            
            for field in expected_fields:
                if field not in first_record:
                    print(f"❌ 搜索结果缺少字段: {field}")
                    return False
            
            print("✅ 搜索结果格式正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {str(e)}")
        return False


def test_error_handling():
    """测试错误处理机制"""
    print("🔍 测试错误处理机制...")
    
    service = InsuranceBrokerageCompanyAreaRelationshipService()
    
    try:
        # 测试无效查询参数
        invalid_relationships = service.search_location_relationships(
            location_type='invalid_type',
            limit=1
        )
        
        # 应该返回空列表而不是抛出异常
        if isinstance(invalid_relationships, list):
            print("✅ 无效参数处理正确")
        else:
            print("❌ 无效参数处理异常")
            return False
        
        # 测试统计信息错误恢复
        stats = service.get_relationship_statistics()
        if isinstance(stats, dict):
            print("✅ 统计信息错误恢复正常")
        else:
            print("❌ 统计信息错误恢复异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False


def test_hive_data_query():
    """测试从Hive查询数据"""
    print("🔍 测试从Hive查询数据...")
    
    service = InsuranceBrokerageCompanyAreaRelationshipService()
    
    try:
        # 查询少量数据进行测试
        data = service._query_company_area_data_from_hive(limit=3)
        
        if data.empty:
            print("⚠️  没有查询到数据（这可能是正常的）")
            return True
        
        print(f"✅ 成功查询到 {len(data)} 条记录")
        
        # 检查数据格式
        expected_columns = ['company_name', 'province', 'city', 'district']
        missing_columns = []
        
        for col in expected_columns:
            if col not in data.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"❌ 数据缺少必要列: {missing_columns}")
            return False
        
        print("✅ 数据格式正确")
        return True
        
    except Exception as e:
        print(f"❌ Hive数据查询测试失败: {str(e)}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("保险经纪公司区域关系功能综合测试")
    print("=" * 60)
    
    tests = [
        ("统计信息格式", test_statistics_format),
        ("ImportHelper验证", test_import_helper_validation),
        ("搜索功能", test_search_functionality),
        ("错误处理", test_error_handling),
        ("Hive数据查询", test_hive_data_query),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   测试失败: {test_name}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 