#!/usr/bin/env python3
"""
被保险人与保单关系验证脚本

提供统计、搜索、验证和清理功能
用法: python scripts/data_verification/verify_insured_person_policy_relationships.py
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.relationship_service.insured_person_policy_relationship_service import InsuredPersonPolicyRelationshipService
from app.utils.import_helper import ImportHelper


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='被保险人与保单关系验证脚本')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    parser.add_argument('--search', action='store_true', help='搜索关系')
    parser.add_argument('--delete', action='store_true', help='删除所有关系')
    parser.add_argument('--verify', action='store_true', help='验证数据完整性')
    parser.add_argument('--force', action='store_true', help='强制执行删除操作')
    parser.add_argument('--limit', type=int, default=10, help='搜索结果限制数量')
    parser.add_argument('--insured-name', type=str, help='按被保险人姓名搜索')
    parser.add_argument('--policy-code', type=str, help='按保单号搜索')
    args = parser.parse_args()
    
    # 创建导入助手
    helper = ImportHelper("被保险人与保单关系验证")
    
    # 打印头部信息
    helper.print_header("被保险人与保单关系验证工具")
    
    try:
        # 创建关系服务实例
        service = InsuredPersonPolicyRelationshipService()
        
        # 如果没有指定任何参数，显示帮助信息
        if not any([args.stats, args.search, args.delete, args.verify]):
            parser.print_help()
            return
        
        if args.stats:
            # 显示统计信息
            print("\n📊 被保险人与保单关系统计信息:")
            stats = service.get_statistics()
            
            if stats:
                print(f"\n=== 统计概览 ===")
                print(f"总关系数: {stats.get('total_count', 0)}")
                
                additional_stats = stats.get('additional_stats', {})
                if additional_stats:
                    print(f"有保单关系的被保险人数: {additional_stats.get('insured_persons_with_policy', 0)}")
                    print(f"有被保险人关系的保单数: {additional_stats.get('policies_with_insured', 0)}")
                    
                    # 显示数据源分布
                    by_data_source = additional_stats.get('by_data_source_node', {})
                    if by_data_source:
                        print("\n=== 数据源分布 ===")
                        for source, count in by_data_source.items():
                            print(f"  {source}: {count}个关系")
                            
                print(f"\n最后更新时间: {stats.get('last_updated', 'N/A')}")
            else:
                print("❌ 无统计数据")
        
        if args.search:
            # 搜索关系
            print(f"\n🔍 搜索被保险人与保单关系 (限制: {args.limit}):")
            
            # 构建搜索参数
            search_kwargs = {
                'limit': args.limit,
                'include_node_details': True
            }
            
            if args.insured_name:
                search_kwargs['insured_name'] = args.insured_name
                print(f"   - 被保险人姓名包含: {args.insured_name}")
                
            if args.policy_code:
                search_kwargs['policy_code'] = args.policy_code
                print(f"   - 保单号包含: {args.policy_code}")
            
            relationships = service.search_insured_policy_relationships(**search_kwargs)
            
            if relationships:
                print(f"\n找到 {len(relationships)} 个关系:")
                for i, rel in enumerate(relationships):
                    policy_info = rel.get('policy', {})
                    insured_info = rel.get('insured_person', {})
                    policy_code = policy_info.get('policy_code', 'N/A')
                    insured_name = insured_info.get('name', 'N/A')
                    insured_code = insured_info.get('code', 'N/A')
                    
                    print(f"  {i+1:2d}. 保单: {policy_code} -> 被保险人: {insured_name} ({insured_code})")
                    
                    # 显示关系属性
                    if 'relationship_strength' in rel:
                        print(f"      关系强度: {rel['relationship_strength']}, 状态: {rel.get('relationship_status', 'N/A')}")
            else:
                print("❌ 未找到匹配的关系")
        
        if args.verify:
            # 验证数据完整性
            print("\n🔍 验证数据完整性:")
            
            # 查找没有被保险人的保单
            print("   - 检查没有被保险人关系的保单...")
            # 这里可以添加具体的验证逻辑
            
            # 查找没有保单的被保险人
            print("   - 检查没有保单关系的被保险人...")
            # 这里可以添加具体的验证逻辑
            
            print("✅ 数据完整性验证完成")
        
        if args.delete:
            # 删除所有关系
            if not args.force:
                confirm = input("\n⚠️  确定要删除所有被保险人与保单关系吗？(y/N): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return
            
            print("\n🗑️ 删除所有被保险人与保单关系...")
            result = service.delete_all_relationships()
            
            if result:
                print("✅ 删除成功")
            else:
                print("❌ 删除失败")
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        helper.logger.error(f"验证操作失败: {str(e)}")
        sys.exit(1)
    
    # 打印尾部信息
    helper.print_footer()


if __name__ == "__main__":
    main() 