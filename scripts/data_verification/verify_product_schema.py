#!/usr/bin/env python3
"""
保险产品数据结构验证脚本

检查Neo4j中的保险产品数据是否严格符合图谱属性规范
用法: python scripts/data_verification/verify_product_schema.py [--limit N]
"""

import sys
import argparse
from pathlib import Path

# 项目路径设置
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.data_storage.neo4j_storage import Neo4jDataStorage
from app.utils.logger import get_logger

logger = get_logger(__name__)


def verify_product_schema(limit: int = 10):
    """
    验证产品数据结构
    
    Args:
        limit: 检查的产品数量限制
    
    Returns:
        bool: 验证是否通过
    """
    
    logger.info("开始验证保险产品数据结构")
    
    try:
        # 创建Neo4j存储服务
        storage = Neo4jDataStorage()
        
        # 查询产品数据
        query = f'''
        MATCH (n:InsuranceProduct)
        RETURN n
        LIMIT {limit}
        '''
        
        results = storage.execute_query(query)
        print(f"=== Neo4j中的保险产品数据结构验证 ===")
        print(f"总共找到 {len(results)} 个产品节点（限制: {limit}）")
        
        # 根据图谱属性规范定义的期望字段
        expected_fields = {
            'code',              # 产品代码
            'name',              # 产品名称  
            'short_name',        # 产品简称
            'business_source',   # 业务来源
            'business_id',       # 业务ID（技术字段）
            'created_at',        # 创建时间
            'updated_at'         # 更新时间
        }
        
        all_compliant = True
        
        for i, record in enumerate(results, 1):
            product = record['n']
            print(f"\n产品{i}:")
            print(f"  所有字段: {sorted(list(product.keys()))}")
            print(f"  产品代码: {product.get('code', 'N/A')}")
            print(f"  产品名称: {product.get('name', 'N/A')}")
            print(f"  产品简称: {product.get('short_name', 'N/A')}")
            print(f"  业务来源: {product.get('business_source', 'N/A')}")
            
            # 检查规范符合性
            actual_fields = set(product.keys())
            
            if actual_fields == expected_fields:
                print(f"  ✅ 完全符合图谱属性规范")
            else:
                all_compliant = False
                missing = expected_fields - actual_fields
                extra = actual_fields - expected_fields
                if missing:
                    print(f"  ❌ 缺少字段: {missing}")
                if extra:
                    print(f"  ❌ 多余字段: {extra}")
        
        print(f"\n=== 验证总结 ===")
        if all_compliant:
            print("✅ 所有产品数据完全符合图谱属性规范")
        else:
            print("❌ 部分产品数据不符合图谱属性规范")
        
        # 统计信息
        stats = storage.get_node_statistics("InsuranceProduct")
        print(f"\n=== 统计信息 ===")
        print(f"产品总数: {stats.get('total_count', 0)}")
        print(f"最后更新: {stats.get('last_updated', 'N/A')}")
        
        logger.info("保险产品数据结构验证完成")
        return all_compliant
        
    except Exception as e:
        logger.error(f"验证失败: {str(e)}")
        print(f"❌ 验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="保险产品数据结构验证脚本")
    parser.add_argument(
        "--limit", 
        type=int, 
        default=10, 
        help="检查的产品数量限制（默认: 10）"
    )
    
    args = parser.parse_args()
    
    print("BZN 关系图谱系统 - 保险产品数据结构验证")
    print("=" * 50)
    
    success = verify_product_schema(limit=args.limit)
    
    print("\n验证完成！")
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 