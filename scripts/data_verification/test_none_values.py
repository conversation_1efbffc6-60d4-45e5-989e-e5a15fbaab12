#!/usr/bin/env python3
"""
测试None值在Neo4j中的存储行为
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.data_storage.neo4j_storage import Neo4jDataStorage
from app.models.nodes.insurance_product_node import InsuranceProductNode
from datetime import datetime


def test_none_values():
    """测试None值存储"""
    
    print("=== 测试None值在Neo4j中的存储行为 ===")
    
    try:
        # 创建一个包含None值的产品节点
        product = InsuranceProductNode(
            product_code="TEST001",
            product_name="测试产品",
            product_short_name=None,  # 明确设置为None
            business_source="online",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        print(f"创建的产品节点:")
        print(f"  product_code: {product.product_code}")
        print(f"  product_name: {product.product_name}")
        print(f"  product_short_name: {product.product_short_name}")
        print(f"  business_source: {product.business_source}")
        
        # 查看to_dict()的输出
        properties = product.to_dict()
        print(f"\nto_dict()输出:")
        for key, value in properties.items():
            print(f"  {key}: {value} (type: {type(value).__name__})")
        
        # 存储到Neo4j
        storage = Neo4jDataStorage()
        
        # 先删除测试数据
        storage.execute_query("MATCH (n:InsuranceProduct {product_code: 'TEST001'}) DELETE n")
        
        # 存储节点
        success = storage.store_nodes([product])
        print(f"\n存储结果: {'成功' if success else '失败'}")
        
        # 查询验证
        query = "MATCH (n:InsuranceProduct {product_code: 'TEST001'}) RETURN n"
        results = storage.execute_query(query)
        
        if results:
            stored_product = results[0]['n']
            print(f"\n从Neo4j查询到的数据:")
            for key, value in stored_product.items():
                print(f"  {key}: {value} (type: {type(value).__name__})")
            
            # 检查是否包含product_short_name字段
            if 'product_short_name' in stored_product:
                print(f"\n✅ product_short_name字段存在: {stored_product['product_short_name']}")
            else:
                print(f"\n❌ product_short_name字段缺失")
        else:
            print("\n❌ 未找到存储的数据")
        
        # 清理测试数据
        storage.execute_query("MATCH (n:InsuranceProduct {product_code: 'TEST001'}) DELETE n")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    test_none_values() 