# 启动脚本使用说明

## 🚀 FastAPI服务启动方式

系统已升级到FastAPI架构，提供多种启动方式：

### 1. 快速启动（推荐）
```bash
# 最简单的启动方式
python scripts/quick_start.py

# 或使用uv环境
uv run python scripts/quick_start.py
```

### 2. 开发环境启动
```bash
# 使用开发环境配置
./scripts/start_dev.sh

# 或直接运行
python scripts/start_server.py
```

### 3. 生产环境启动
```bash
# 使用生产环境配置
./scripts/start_prod.sh
```

### 4. 直接使用main.py
```bash
# 直接运行主文件
python main.py

# 或使用uv环境
uv run python main.py
```

### 5. 使用uvicorn命令
```bash
# 开发模式
uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 📊 启动方式对比

| 启动方式 | 适用场景 | 特点 | 命令 |
|----------|----------|------|------|
| **quick_start.py** | 快速测试 | 零配置，开箱即用 | `python scripts/quick_start.py` |
| **start_dev.sh** | 开发环境 | 自动重载，详细日志 | `./scripts/start_dev.sh` |
| **start_prod.sh** | 生产环境 | 多进程，性能优化 | `./scripts/start_prod.sh` |
| **main.py** | 通用启动 | 读取环境配置 | `python main.py` |
| **uvicorn直接** | 自定义配置 | 完全控制参数 | `uvicorn main:app --reload` |

## 🌐 访问地址

启动成功后，可以访问以下地址：

- **根路径**: http://127.0.0.1:8000/
- **健康检查**: http://127.0.0.1:8000/health
- **API文档**: http://127.0.0.1:8000/api/v1/docs
- **ReDoc文档**: http://127.0.0.1:8000/api/v1/redoc

## ⚙️ 环境变量配置

可以通过环境变量自定义配置：

```bash
export API_HOST="0.0.0.0"        # 服务监听地址
export API_PORT="8000"           # 服务端口
export FLASK_DEBUG="True"        # 调试模式（兼容旧变量名）
```

## 🔧 常见问题

### Q: 为什么不是Flask了？
A: 系统已完全升级到FastAPI架构，性能更好，功能更强大。

### Q: 如何停止服务？
A: 在终端中按 `Ctrl+C` 即可停止服务。

### Q: 端口被占用怎么办？
A: 修改环境变量 `API_PORT` 或在命令中指定其他端口。

### Q: 如何查看所有API？
A: 启动服务后访问 http://127.0.0.1:8000/api/v1/docs

## 📝 日志查看

- **开发模式**: 日志直接输出到终端
- **生产模式**: 检查 `logs/app.log` 文件

## 🚨 注意事项

1. **依赖安装**: 确保已安装所有依赖 `uv sync`
2. **数据库连接**: 确保Neo4j和Hive服务正常运行
3. **环境配置**: 检查 `.env` 文件配置是否正确 