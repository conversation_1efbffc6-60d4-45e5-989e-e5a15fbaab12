# BZN 关系图谱系统服务端

基于 Flask + Neo4j 的保险业务关系图谱系统，从 Hive 数据仓库提取数据构建图数据库，支持复杂的业务关系查询和分析。

## 核心特性

- **🔄 数据管道**: 从 Hive 到 Neo4j 的完整数据流水线
- **📊 图数据建模**: 多标签节点和复杂关系建模
- **🔍 智能查询**: 基于图算法的关系查询和路径分析  
- **🛡️ 数据安全**: 敏感信息 AES 加密存储
- **⚡ 批量处理**: 大数据集分页处理和性能优化
- **🌐 RESTful API**: 标准化的 API 接口设计
- **🇨🇳 中文优先**: 文档、注释、日志全中文

## 技术栈

- **Python 3.12+**: 主要开发语言
- **uv**: Python 包管理器（替代 pip/poetry）
- **Flask**: Web 框架和 API 服务
- **Neo4j**: 图数据库存储
- **Impala/Hive**: 大数据查询引擎
- **Pandas**: 数据处理和转换
- **Pydantic**: 数据验证和序列化

## 项目架构

```
bzn-relational-graph-server/
├── app/                          # 应用核心
│   ├── models/                   # 数据模型层
│   │   ├── base/                # 基础模型
│   │   ├── nodes/               # 节点模型（统一目录）
│   │   └── relationships/       # 关系模型
│   ├── services/                # 服务层
│   │   ├── node_service/        # 节点服务
│   │   ├── relationship_service/ # 关系服务
│   │   ├── data_extraction/     # 数据提取
│   │   ├── data_transform/      # 数据转换
│   │   └── data_storage/        # 数据存储
│   ├── controllers/             # API 控制器（统一目录）
│   ├── config/                  # 配置管理
│   └── utils/                   # 工具模块
├── scripts/                     # 脚本目录
│   ├── data_import/            # 数据导入脚本
│   ├── data_verification/      # 数据验证脚本
│   ├── development/            # 开发辅助脚本
│   └── maintenance/            # 维护脚本
├── tests/                      # 测试目录
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── fixtures/               # 测试数据
├── docs/                       # 用户文档
│   ├── guides/                 # 使用指南
│   └── specifications/         # 技术规范
├── .cursor/                    # 开发规则
│   └── rules/                  # Cursor 开发规范
├── logs/                       # 日志目录
├── pyproject.toml             # uv 项目配置
├── env.example                # 环境变量模板
└── main.py                    # 应用入口
```

## 快速开始

### 1. 环境要求

- **Python 3.12+**
- **uv** 包管理器（推荐安装方式：`curl -LsSf https://astral.sh/uv/install.sh | sh`）
- **Neo4j 5.0+** 图数据库
- **Hive/Impala** 数据仓库访问权限

### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd bzn-relational-graph-server

# 使用 uv 安装依赖
uv sync

# 复制并配置环境变量
cp env.example .env
# 编辑 .env 配置数据库连接
```

### 3. 环境配置

编辑 `.env` 文件：

```bash
# Neo4j 图数据库配置
NEO4J_URI=bolt://*************:7687
NEO4J_USERNAME=your_username
NEO4J_PASSWORD=your_password
NEO4J_DATABASE=neo4j

# Hive/Impala 数据仓库配置  
IMPALA_HOST=*************
IMPALA_PORT=21050
IMPALA_DATABASE=default
IMPALA_USERNAME=your_username
IMPALA_PASSWORD=your_password

# 数据加密配置
ENCRYPTION_KEY=your-32-char-encryption-key-here
```

### 4. 启动服务

使用统一的 `run.sh` 脚本启动服务：

```bash
# 开发模式启动（默认）
./run.sh

# 开发模式启动（显式指定）
./run.sh dev

# 生产模式启动
./run.sh prod

# 查看服务状态
./run.sh status

# 停止服务
./run.sh stop

# 查看帮助
./run.sh help
```

**环境变量配置**：
```bash
# 自定义监听地址和端口
export API_HOST=0.0.0.0
export API_PORT=8080
./run.sh dev
```

**服务信息**：
- 📍 服务地址: http://127.0.0.1:8000
- 📖 API文档: http://127.0.0.1:8000/api/v1/docs
- 📋 生产日志: app.log

## 核心功能

### 数据管道

- **📥 数据提取**: 从 Hive 分页提取业务数据
- **🔄 数据转换**: 结构化数据转图模型节点
- **💾 数据存储**: 批量存储到 Neo4j 图数据库
- **🔗 关系建模**: 构建复杂的业务关系网络

### 业务模型

#### 主要节点类型
- **👤 PersonNode**: 人员信息（投保人、被保险人）
- **🏢 OrganizationNode**: 组织机构
- **📋 PolicyNode**: 保单信息
- **🏷️ InsuranceProductNode**: 保险产品
- **🌍 AreaNode**: 地理区域
- **💼 OccupationNode**: 职业信息

#### 关系类型
- **POLICY_HOLDER**: 投保关系
- **INSURED**: 被保险关系  
- **BELONGS_TO**: 归属关系
- **LOCATED_IN**: 地理位置关系

### API 接口

#### 标准端点模式
每个业务模块提供统一的 API 接口：

```bash
# 数据提取接口
POST /api/v1/{resource}/extract     # 从Hive提取数据
GET  /api/v1/{resource}/search      # 搜索查询数据
GET  /api/v1/{resource}/statistics  # 获取统计信息
GET  /api/v1/{resource}/health      # 服务健康检查
DELETE /api/v1/{resource}/delete-all # 删除所有数据（管理员）
```

#### 图查询接口
```bash
GET  /api/v1/graph/stats           # 图数据库统计
POST /api/v1/graph/query           # 自定义 Cypher 查询
GET  /api/v1/graph/nodes/{id}      # 节点详情查询
GET  /api/v1/graph/relationships/{id} # 关系详情查询
```

## 开发规范

### 包管理
```bash
# 添加依赖
uv add package-name

# 添加开发依赖  
uv add --dev package-name

# 安装项目依赖
uv sync

# 运行脚本
uv run python script.py
```

### 代码质量
```bash
# 代码格式化
uv run black app/

# 代码检查
uv run flake8 app/  

# 类型检查
uv run mypy app/

# 运行测试
uv run pytest
```

### 开发指南

项目遵循严格的开发规范，详见 `.cursor/rules/` 目录：

- **architecture.mdc**: 系统架构和设计模式
- **service-patterns.mdc**: 服务层模式和数据处理
- **model-structure.mdc**: 数据模型设计规范
- **development-standards.mdc**: 开发标准和编程规范  
- **data-import-standards.mdc**: 数据导入流程标准

## 数据导入示例

### 使用 ImportHelper 标准流程

```python
from app.utils.import_helper import ImportHelper

def main():
    parser = argparse.ArgumentParser(description='数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行')
    args = parser.parse_args()
    
    helper = ImportHelper("保险产品")
    helper.print_header("保险产品数据导入")
    
    try:
        # 确认清理现有数据
        if helper.confirm_overwrite(args.force, "保险产品数据"):
            helper.execute_with_progress("清理现有数据", service.delete_all)
        
        # 导入数据
        result = helper.execute_with_progress("导入数据", service.extract_and_store)
        helper.print_import_result(result, "保险产品导入")
        
        # 验证结果
        helper.validate_import(service.get_statistics, min_count=1)
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        sys.exit(1)
    
    helper.print_footer()
```

### 批量数据导入

```bash
# 导入保险产品数据
uv run scripts/data_import/import_insurance_products.py

# 导入组织架构数据  
uv run scripts/data_import/import_organizations.py

# 导入人员信息数据
uv run scripts/data_import/import_persons.py

# 强制模式（跳过确认）
uv run scripts/data_import/import_policies.py --force
```

## 性能优化

### 大数据处理策略
- **分页查询**: 默认 1000 条记录避免内存溢出
- **批量存储**: 50 个节点一批使用 Neo4j UNWIND 语法
- **动态分区**: 使用 `(date.today() - timedelta(days=1))` 处理 Hive 分区
- **数据去重**: 基于 `business_id` 在转换阶段去重

### 数据转换优化
- **类型转换**: pandas.Timestamp → Python date 自动转换
- **百分比处理**: '10%' → 0.1 自动解析
- **数值兼容**: Decimal → float 适配 Neo4j
- **字段映射**: 支持多种字段名兼容（如 policy_code/policy_no）

## 部署运维

### Docker 部署
```bash
# 构建镜像
docker build -t bzn-graph-server .

# 运行容器
docker run -d -p 5000:5000 --env-file .env bzn-graph-server
```

### 监控日志
- **日志位置**: `logs/app.log`
- **日志轮转**: 按大小和时间自动轮转
- **中文日志**: 错误信息和操作记录使用中文

## 文档资源

- **📖 使用指南**: `docs/guides/` - 业务用户使用文档
- **📋 技术规范**: `docs/specifications/` - 图数据模型规范  
- **⚙️ 开发规则**: `.cursor/rules/` - 开发者技术规范
- **📝 API 文档**: 内置 Swagger 文档（/api/docs）

## 重构亮点

本项目经过大规模重构，消除了技术债务：

- **📁 文档优化**: 从 35+ 个文档整合为 11 个核心文档
- **🏗️ 架构清理**: 统一模型结构，清晰的服务层分离
- **📏 规范统一**: 建立了完整的开发和导入标准
- **🇨🇳 中文优先**: 全面采用中文文档和注释
- **⚡ 性能提升**: 优化了数据处理和存储策略

## 技术支持

如有技术问题或功能建议，请联系开发团队或提交 Issue。

项目遵循中文优先原则，所有文档、注释和交流均使用中文。
