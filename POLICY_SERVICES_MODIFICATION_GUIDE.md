# 保单关系服务修改指南

## 目标文件列表

1. `policy_insurance_company_relationship_service.py` - 保单与保险公司关系
2. `policy_insurer_company_relationship_service.py` - 保单与投保公司关系
3. `policy_product_relationship_service.py` - 保单与产品关系
4. `policy_sale_person_relationship_service.py` - 保单与销售人员关系
5. `policy_sales_channel_relationship_service.py` - 保单与销售渠道关系

## 统一修改模式

### 1. 文件头部修改
```python
# 原来
from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import date, timedelta

# 修改为
from typing import List, Dict, Any, Optional
from datetime import datetime
```

### 2. 方法签名简化
```python
# 原来
def create_relationships(self, 
                        limit: Optional[int] = None,
                        where_conditions: Optional[str] = None,
                        custom_day: Optional[str] = None) -> bool:

# 修改为
def create_relationships(self, limit: Optional[int] = None) -> bool:
```

### 3. 各服务特定配置

#### policy_insurance_company_relationship_service.py
- **关系类型**: `ISSUED_BY`
- **源节点**: `Policy`
- **目标节点**: `InsuranceCompany`
- **匹配字段**: `insurance_company_code` 或 `insurance_company_name`

#### policy_insurer_company_relationship_service.py
- **关系类型**: `INSURED_BY`
- **源节点**: `Policy`
- **目标节点**: `PolicyCompany`
- **匹配字段**: `insurer_company_code` 或 `insurer_company_name`

#### policy_product_relationship_service.py
- **关系类型**: `COVERS_PRODUCT`
- **源节点**: `Policy`
- **目标节点**: `InsuranceProduct`
- **匹配字段**: `product_code` 或 `product_name`

#### policy_sale_person_relationship_service.py
- **关系类型**: `SOLD_BY`
- **源节点**: `Policy`
- **目标节点**: `SalePerson`
- **匹配字段**: `sale_person_code` 或 `sale_person_name`

#### policy_sales_channel_relationship_service.py
- **关系类型**: `SOLD_THROUGH`
- **源节点**: `Policy`
- **目标节点**: `InsuranceSalesChannel`
- **匹配字段**: `sales_channel_code` 或 `sales_channel_name`

## 标准方法模板

### Neo4j查询方法模板
```python
def _query_policy_*_data_from_neo4j(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """从Neo4j查询保单数据"""
    try:
        conditions = [
            "policy.target_field IS NOT NULL",
            "policy.target_field <> ''"
        ]
        where_clause = " AND ".join(conditions)
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        MATCH (policy:Policy)
        WHERE {where_clause}
        RETURN policy.code as policy_code,
               policy.name as policy_name,
               policy.target_field as target_field,
               policy.business_id as policy_business_id
        ORDER BY policy.code
        {limit_clause}
        """
        
        results = self._safe_execute_query(query)
        
        if not results:
            return []
        
        # 过滤掉无效的数据
        valid_data = []
        for record in results:
            policy_code = str(record.get('policy_code', '')).strip()
            target_field = str(record.get('target_field', '')).strip()
            
            if policy_code and target_field and target_field != 'nan':
                valid_data.append({
                    'policy_code': policy_code,
                    'policy_name': record.get('policy_name', ''),
                    'target_field': target_field,
                    'policy_business_id': record.get('policy_business_id', policy_code)
                })
        
        logger.info(f"找到 {len(valid_data)} 个有效的保单记录")
        return valid_data
        
    except Exception as e:
        logger.error(f"查询Neo4j保单数据失败: {str(e)}")
        return []
```

### 构建关系数据方法模板
```python
def _build_relationship_data_from_neo4j(self, policy_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """从Neo4j保单数据构建关系数据"""
    relationship_data = []
    skipped_count = 0
    
    for policy in policy_data:
        policy_code = policy.get('policy_code', '')
        target_field = policy.get('target_field', '')
        
        if not policy_code or not target_field:
            skipped_count += 1
            continue
        
        # 检查目标节点是否存在（如需要）
        if not self._check_target_exists(target_field):
            skipped_count += 1
            continue
        
        relationship_data.append({
            'policy_code': policy_code,
            'target_field': target_field,
            'remarks': f"保单关系: {policy_code} 关联 {target_field}",
            'data_source_node': 'Policy',
            'relationship_strength': 9,
            'relationship_type': '保单业务关系'
        })
    
    logger.info(f"构建了 {len(relationship_data)} 个有效关系，跳过了 {skipped_count} 个无效记录")
    return relationship_data
```

### 创建关系方法模板
```python
def _create_policy_*_relationships(self, relationship_data: List[Dict[str, Any]]) -> bool:
    """创建保单关系"""
    try:
        batch_size = 100
        success_count = 0
        
        for i in range(0, len(relationship_data), batch_size):
            batch_data = relationship_data[i:i + batch_size]
            
            query = """
            UNWIND $relationships as rel
            MATCH (policy:Policy {code: rel.policy_code})
            MATCH (target:TargetNode {field: rel.target_field})
            MERGE (policy)-[r:RELATIONSHIP_TYPE]->(target)
            SET r.relationship_type = 'RELATIONSHIP_TYPE',
                r.relationship_status = 'active',
                r.relationship_strength = rel.relationship_strength,
                r.data_source_node = rel.data_source_node,
                r.remarks = rel.remarks,
                r.created_at = datetime(),
                r.updated_at = datetime()
            RETURN count(r) as created_count
            """
            
            result = self._safe_execute_query(query, {'relationships': batch_data})
            if result:
                batch_count = result[0].get('created_count', 0)
                success_count += batch_count
                logger.info(f"批次 {i//batch_size + 1} 成功创建 {batch_count} 个关系")
        
        logger.info(f"总共成功创建 {success_count} 个关系")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"创建关系失败: {str(e)}")
        return False
```

### 重复关系清理方法模板
```python
def cleanup_duplicate_relationships(self) -> int:
    """清理重复的保单关系"""
    try:
        # 查找重复的关系
        find_duplicates_query = """
        MATCH (policy:Policy)-[r:RELATIONSHIP_TYPE]->(target)
        WITH policy, target, collect(r) as rels
        WHERE size(rels) > 1
        RETURN policy.code as policy_code, target, rels
        """
        
        duplicates_result = self._safe_execute_query(find_duplicates_query)
        
        if not duplicates_result:
            logger.info("没有发现重复的保单关系")
            return 0
        
        total_deleted = 0
        
        for record in duplicates_result:
            policy_code = record['policy_code']
            target = record['target']
            rels = record['rels']
            
            if len(rels) <= 1:
                continue
            
            logger.info(f"发现重复的关系，保单: {policy_code}，共 {len(rels)} 个")
            
            # 保留最早创建的关系，删除其他
            cleanup_query = """
            MATCH (policy:Policy {code: $policy_code})-[r:RELATIONSHIP_TYPE]->(target)
            WHERE id(target) = $target_id
            WITH r 
            ORDER BY COALESCE(r.created_at, datetime()) ASC
            WITH collect(r) as rels
            WHERE size(rels) > 1
            UNWIND rels[1..] as duplicate_rel
            DELETE duplicate_rel
            RETURN count(duplicate_rel) as deleted_count
            """
            
            cleanup_result = self._safe_execute_query(cleanup_query, {
                'policy_code': policy_code,
                'target_id': target.id if hasattr(target, 'id') else 0
            })
            deleted_count = cleanup_result[0]['deleted_count'] if cleanup_result else 0
            total_deleted += deleted_count
            
            logger.info(f"清理了 {deleted_count} 个重复关系，保单: {policy_code}")
        
        logger.info(f"总共清理了 {total_deleted} 个重复的保单关系")
        return total_deleted
        
    except Exception as e:
        logger.error(f"清理重复关系失败: {str(e)}")
        return 0
```

## 修改步骤

1. **更新文件头部**: 导入和文档字符串
2. **简化方法签名**: create_relationships方法
3. **替换查询方法**: Hive → Neo4j
4. **重构构建方法**: 适配Neo4j数据结构
5. **更新创建方法**: 使用新的关系类型和属性
6. **添加清理方法**: 重复关系清理功能
7. **更新统计方法**: 节点标签和查询语句
8. **更新搜索方法**: 统一的关系数据结构

## 验证要点

1. 语法正确性
2. 导入模块正确
3. 方法签名一致
4. 节点标签正确
5. 关系类型合理
6. 错误处理完善
