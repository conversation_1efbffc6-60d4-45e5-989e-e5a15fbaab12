---
description: 
globs: 
alwaysApply: false
---
# Flask API 开发规范

## 核心文件位置

- **应用入口**: [main.py](mdc:main.py)
- **应用配置**: [app/__init__.py](mdc:app/__init__.py)
- **控制器**: [app/controllers/](mdc:app/controllers)
- **配置管理**: [app/config/](mdc:app/config)
- **工具函数**: [app/utils/](mdc:app/utils)

## API 设计规范

### 1. RESTful 端点设计
```python
# ✅ 标准的资源端点模式
# 每个业务模块提供统一的 API 接口

# 数据提取接口
POST /api/v1/{resource}/extract     # 从Hive提取数据
GET  /api/v1/{resource}/search      # 搜索查询数据
GET  /api/v1/{resource}/statistics  # 获取统计信息
GET  /api/v1/{resource}/health      # 服务健康检查
DELETE /api/v1/{resource}/delete-all # 删除所有数据（管理员）

# 示例：人员接口
POST /api/v1/persons/extract
GET  /api/v1/persons/search?name=张三&limit=10
GET  /api/v1/persons/statistics
```

### 2. 控制器结构设计
```python
from flask import Blueprint, request, jsonify
from app.services.person_service import PersonService
from app.utils.response import success_response, error_response
from app.utils.validators import validate_query_params

person_bp = Blueprint('person', __name__, url_prefix='/api/v1/persons')

class PersonController:
    """人员管理控制器"""
    
    def __init__(self):
        self.service = PersonService()
    
    @person_bp.route('/extract', methods=['POST'])
    def extract_persons(self):
        """从 Hive 提取人员数据"""
        try:
            # 参数验证
            data = request.get_json() or {}
            batch_size = data.get('batch_size', 10000)
            
            # 调用服务层
            result = self.service.extract_from_hive(batch_size=batch_size)
            
            return success_response(
                data=result,
                message="人员数据提取任务已启动"
            )
            
        except Exception as e:
            logger.error(f"人员数据提取失败：{e}")
            return error_response(message=f"提取失败：{str(e)}")
```

### 3. 统一响应格式
```python
# app/utils/response.py

def success_response(data=None, message="操作成功", code=200):
    """成功响应格式"""
    response = {
        "success": True,
        "code": code,
        "message": message,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response), code

def error_response(message="操作失败", code=400, error_code=None):
    """错误响应格式"""
    response = {
        "success": False,
        "code": code,
        "message": message,
        "error_code": error_code,
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response), code

# ✅ 标准响应示例
{
    "success": true,
    "code": 200,
    "message": "查询成功",
    "data": {
        "persons": [...],
        "total": 100,
        "page": 1,
        "limit": 10
    },
    "timestamp": "2024-12-19T10:30:00"
}
```

## 参数验证规范

### 1. 请求参数验证
```python
from marshmallow import Schema, fields, ValidationError

class PersonSearchSchema(Schema):
    """人员搜索参数验证"""
    name = fields.Str(required=False, validate=lambda x: len(x) >= 2)
    id_card = fields.Str(required=False, validate=lambda x: len(x) in [15, 18])
    phone = fields.Str(required=False, validate=lambda x: len(x) == 11)
    limit = fields.Int(required=False, validate=lambda x: 1 <= x <= 1000, missing=10)
    page = fields.Int(required=False, validate=lambda x: x >= 1, missing=1)

def validate_request_data(schema_class):
    """请求数据验证装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                schema = schema_class()
                data = request.get_json() or {}
                validated_data = schema.load(data)
                request.validated_data = validated_data
                return func(*args, **kwargs)
            except ValidationError as e:
                return error_response(
                    message="参数验证失败",
                    error_code="VALIDATION_ERROR",
                    code=400
                )
        return wrapper
    return decorator
```

### 2. 查询参数验证
```python
def validate_query_params(request, required_params=None, optional_params=None):
    """验证查询参数"""
    errors = []
    
    # 检查必需参数
    if required_params:
        for param in required_params:
            if param not in request.args:
                errors.append(f"缺少必需参数：{param}")
    
    # 验证可选参数
    if optional_params:
        for param, validator in optional_params.items():
            value = request.args.get(param)
            if value and not validator(value):
                errors.append(f"参数 {param} 格式错误")
    
    if errors:
        raise ValidationError(errors)
```

## 错误处理规范

### 1. 全局异常处理
```python
# app/__init__.py

from flask import Flask
from app.utils.exceptions import APIException, ValidationError

def create_app():
    app = Flask(__name__)
    
    # 注册全局异常处理器
    @app.errorhandler(APIException)
    def handle_api_exception(e):
        return error_response(
            message=e.message,
            code=e.code,
            error_code=e.error_code
        )
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(e):
        return error_response(
            message="参数验证失败",
            code=400,
            error_code="VALIDATION_ERROR"
        )
    
    @app.errorhandler(404)
    def handle_not_found(e):
        return error_response(
            message="接口不存在",
            code=404,
            error_code="NOT_FOUND"
        )
    
    @app.errorhandler(500)
    def handle_internal_error(e):
        logger.error(f"服务器内部错误：{e}")
        return error_response(
            message="服务器内部错误",
            code=500,
            error_code="INTERNAL_ERROR"
        )
    
    return app
```

### 2. 自定义异常类
```python
# app/utils/exceptions.py

class APIException(Exception):
    """API 异常基类"""
    
    def __init__(self, message="API 错误", code=400, error_code=None):
        self.message = message
        self.code = code
        self.error_code = error_code
        super().__init__(self.message)

class DatabaseException(APIException):
    """数据库异常"""
    
    def __init__(self, message="数据库操作失败"):
        super().__init__(message, code=500, error_code="DATABASE_ERROR")

class Neo4jException(APIException):
    """Neo4j 异常"""
    
    def __init__(self, message="图数据库操作失败"):
        super().__init__(message, code=500, error_code="NEO4J_ERROR")

class HiveException(APIException):
    """Hive 异常"""
    
    def __init__(self, message="Hive 查询失败"):
        super().__init__(message, code=500, error_code="HIVE_ERROR")
```

## 中间件开发规范

### 1. 请求日志中间件
```python
# app/middleware/logging_middleware.py

import time
from flask import request, g
from loguru import logger

class RequestLoggingMiddleware:
    """请求日志中间件"""
    
    def __init__(self, app=None):
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """请求开始前"""
        g.start_time = time.time()
        g.request_id = self.generate_request_id()
        
        logger.info(
            f"请求开始 - ID: {g.request_id} | "
            f"方法: {request.method} | "
            f"路径: {request.path} | "
            f"IP: {request.remote_addr}"
        )
    
    def after_request(self, response):
        """请求结束后"""
        execution_time = time.time() - g.start_time
        
        logger.info(
            f"请求完成 - ID: {g.request_id} | "
            f"状态码: {response.status_code} | "
            f"耗时: {execution_time:.3f}秒"
        )
        
        # 添加响应头
        response.headers['X-Request-ID'] = g.request_id
        response.headers['X-Execution-Time'] = f"{execution_time:.3f}s"
        
        return response
```

### 2. CORS 配置
```python
# app/middleware/cors_middleware.py

from flask_cors import CORS

def setup_cors(app):
    """配置 CORS"""
    CORS(app, 
         origins=['http://localhost:3000', 'http://127.0.0.1:3000'],  # 前端地址
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
         allow_headers=['Content-Type', 'Authorization', 'X-Requested-With'],
         supports_credentials=True
    )
```

### 3. 认证中间件
```python
# app/middleware/auth_middleware.py

from functools import wraps
from flask import request, g
import jwt

def require_auth(f):
    """需要认证的接口装饰器"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return error_response(
                message="缺少认证令牌",
                code=401,
                error_code="AUTH_TOKEN_MISSING"
            )
        
        try:
            # 验证 JWT 令牌
            payload = jwt.decode(token.replace('Bearer ', ''), 
                               app.config['SECRET_KEY'], 
                               algorithms=['HS256'])
            g.current_user = payload
        except jwt.ExpiredSignatureError:
            return error_response(
                message="认证令牌已过期",
                code=401,
                error_code="AUTH_TOKEN_EXPIRED"
            )
        except jwt.InvalidTokenError:
            return error_response(
                message="无效的认证令牌",
                code=401,
                error_code="AUTH_TOKEN_INVALID"
            )
        
        return f(*args, **kwargs)
    return wrapper
```

## 配置管理规范

### 1. 配置类设计
```python
# app/config/config.py

import os
from pydantic_settings import BaseSettings

class Config(BaseSettings):
    """基础配置"""
    
    # Flask 基础配置
    SECRET_KEY: str = "your-secret-key-here"
    DEBUG: bool = False
    TESTING: bool = False
    
    # 数据库配置
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USERNAME: str = "neo4j"
    NEO4J_PASSWORD: str = "password"
    NEO4J_DATABASE: str = "neo4j"
    
    # Hive 配置
    IMPALA_HOST: str = "localhost"
    IMPALA_PORT: int = 21050
    IMPALA_DATABASE: str = "default"
    IMPALA_USERNAME: str = ""
    IMPALA_PASSWORD: str = ""
    
    # 加密配置
    ENCRYPTION_KEY: str = ""
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"
    
    class Config:
        env_file = ".env"

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"

# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
```

### 2. 应用工厂模式
```python
# app/__init__.py

from flask import Flask
from app.config.config import config_map
from app.middleware.logging_middleware import RequestLoggingMiddleware
from app.middleware.cors_middleware import setup_cors

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    config_class = config_map.get(config_name, config_map['default'])
    config = config_class()
    app.config.from_object(config)
    
    # 设置中间件
    RequestLoggingMiddleware(app)
    setup_cors(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 初始化扩展
    init_extensions(app)
    
    return app

def register_blueprints(app):
    """注册蓝图"""
    from app.controllers.person_controller import person_bp
    from app.controllers.policy_controller import policy_bp
    from app.controllers.graph_controller import graph_bp
    
    app.register_blueprint(person_bp)
    app.register_blueprint(policy_bp)
    app.register_blueprint(graph_bp)
```

## 性能优化规范

### 1. 分页查询
```python
def paginate_query(query_func, page=1, limit=10, max_limit=1000):
    """分页查询辅助函数"""
    # 限制每页最大数量
    limit = min(limit, max_limit)
    offset = (page - 1) * limit
    
    # 执行查询
    results = query_func(limit=limit, offset=offset)
    
    # 获取总数（可选）
    total = get_total_count() if hasattr(query_func, 'count') else None
    
    return {
        'data': results,
        'pagination': {
            'page': page,
            'limit': limit,
            'total': total,
            'has_next': len(results) == limit
        }
    }
```

### 2. 缓存策略
```python
from functools import wraps
import json
import redis

# Redis 连接
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time=300):
    """结果缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(json.dumps(kwargs, sort_keys=True))}"
            
            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expire_time, json.dumps(result))
            
            return result
        return wrapper
    return decorator
```

## 测试规范

### 1. API 测试
```python
# tests/test_person_api.py

import pytest
from app import create_app

@pytest.fixture
def client():
    app = create_app('testing')
    with app.test_client() as client:
        yield client

def test_person_search_success(client):
    """测试人员搜索成功"""
    response = client.get('/api/v1/persons/search?name=张三')
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] is True
    assert 'data' in data

def test_person_search_validation_error(client):
    """测试人员搜索参数验证错误"""
    response = client.get('/api/v1/persons/search?limit=2000')  # 超出限制
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert data['error_code'] == 'VALIDATION_ERROR'
```

### 2. 集成测试
```python
@pytest.mark.integration
def test_person_extract_integration(client):
    """测试人员数据提取集成"""
    # 模拟提取请求
    response = client.post('/api/v1/persons/extract', 
                          json={'batch_size': 100})
    
    assert response.status_code == 200
    
    # 验证数据是否正确插入到 Neo4j
    # ... 数据库验证逻辑
```

## 最佳实践总结

1. **统一接口规范**：所有 API 遵循 RESTful 设计原则
2. **标准响应格式**：成功和错误响应格式统一
3. **完善错误处理**：全局异常处理和自定义异常类
4. **参数验证**：使用 Marshmallow 进行严格的参数验证
5. **中间件使用**：请求日志、CORS、认证等中间件
6. **配置管理**：环境变量和配置类分离
7. **性能优化**：分页查询、缓存策略、数据库连接池
8. **测试覆盖**：单元测试、集成测试、API 测试

遵循这些规范可以确保 Flask API 的稳定性、可维护性和高性能。
