---
description: 
globs: 
alwaysApply: false
---
# UV 包管理规范

## 📦 强制使用 UV
所有Python包管理操作必须使用 `uv`，严禁使用 `pip` 或 `poetry`。

## 🔧 核心命令

### 项目初始化
```bash
# 创建新项目
uv init project-name

# 同步现有项目依赖
uv sync
```

### 依赖管理
```bash
# 添加生产依赖
uv add flask>=2.3.0
uv add neo4j>=5.0.0
uv add pandas>=2.0.0

# 添加开发依赖
uv add --dev pytest>=7.0.0
uv add --dev black>=23.0.0
uv add --dev mypy>=1.5.0

# 添加可选依赖组
uv add --optional dev pytest>=7.0.0
```

### 运行脚本
```bash
# 运行Python脚本
uv run python main.py
uv run python scripts/data_import/import_policies.py

# 运行应用
uv run flask run
uv run gunicorn -w 4 -b 0.0.0.0:5000 main:app
```

### 开发工具
```bash
# 代码格式化
uv run black app/

# 代码检查
uv run flake8 app/

# 类型检查
uv run mypy app/

# 运行测试
uv run pytest
uv run pytest tests/unit/
uv run pytest tests/integration/
```

## 📋 配置文件

### pyproject.toml 配置
项目配置在 [pyproject.toml](mdc:pyproject.toml) 中管理：

```toml
[project]
name = "bzn-relational-graph-server"
version = "0.1.0"
description = "基于 Neo4j 的关系图谱系统服务端"
requires-python = ">=3.12"
dependencies = [
    "flask>=2.3.0",
    "neo4j>=5.0.0",
    "pandas>=2.0.0",
    # ... 其他依赖
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "mypy>=1.5.0",
]
```

### 锁定文件
- `uv.lock`: 精确的依赖版本锁定文件
- 自动生成，不要手动编辑
- 确保环境一致性

## 🔒 版本管理

### 依赖版本策略
```bash
# 精确版本（生产环境关键依赖）
uv add flask==2.3.2

# 最小版本（通用库）
uv add pandas>=2.0.0

# 兼容版本（开发工具）
uv add black~=23.0
```

### 更新依赖
```bash
# 更新所有依赖
uv sync --upgrade

# 更新特定依赖
uv add flask --upgrade

# 检查过期依赖
uv tree --outdated
```

## 🚫 严格禁止的操作

### 禁用的包管理器
```bash
# ❌ 禁止使用 pip
pip install package-name
pip freeze > requirements.txt

# ❌ 禁止使用 poetry
poetry add package-name
poetry install

# ❌ 禁止使用 conda
conda install package-name
```

### 禁用的配置文件
- ❌ `requirements.txt` - 使用 pyproject.toml
- ❌ `poetry.lock` - 使用 uv.lock
- ❌ `Pipfile` - 使用 pyproject.toml

## 🏗️ 开发工作流

### 新功能开发
```bash
# 1. 同步依赖
uv sync

# 2. 添加新依赖（如需要）
uv add new-package>=1.0.0

# 3. 开发和测试
uv run python -m pytest

# 4. 代码质量检查
uv run black app/
uv run flake8 app/
uv run mypy app/
```

### 脚本执行
```bash
# 数据导入脚本
uv run python scripts/data_import/import_policies.py --force

# 数据验证脚本
uv run python scripts/data_verification/verify_data.py

# 开发工具脚本
uv run python scripts/development/test_connection.py
```

## 🔄 环境管理

### 虚拟环境
```bash
# uv 自动管理虚拟环境
# 无需手动创建或激活

# 查看环境信息
uv python list
uv python install 3.12

# 指定Python版本
uv python pin 3.12
```

### 跨平台兼容
```bash
# 生成跨平台锁定文件
uv sync --universal

# 导出requirements.txt（如需要）
uv export --format requirements-txt > requirements.txt
```

## 📈 性能优化

### 缓存管理
```bash
# 清理缓存
uv cache clean

# 查看缓存信息
uv cache dir
```

### 并行安装
```bash
# uv 默认并行安装，速度比 pip 快 10-100 倍
# 无需特殊配置
```

## 🔍 故障排除

### 常见问题
```bash
# 依赖冲突解决
uv sync --resolution=lowest-direct

# 强制重新解析
uv sync --upgrade --refresh

# 详细输出
uv sync --verbose
```

### 依赖分析
```bash
# 查看依赖树
uv tree

# 查看特定包的依赖
uv tree package-name

# 检查依赖冲突
uv sync --dry-run
```
