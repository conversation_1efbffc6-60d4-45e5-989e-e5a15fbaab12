---
description: 
globs: 
alwaysApply: false
---
# 开发规范与最佳实践

## 🇨🇳 语言规范
- **中文优先**: 所有代码注释必须使用中文
- **文档中文**: README、API文档、错误信息都用中文编写
- **日志中文**: 系统日志信息使用中文，便于运维团队理解
- **变量命名**: 使用英文，但注释解释必须用中文

```python
# ✅ 正确示例
def extract_policy_data():
    """从Hive提取保单数据"""
    policy_count = 0  # 保单数量
    return policy_count

# ❌ 错误示例  
def extract_policy_data():
    """Extract policy data from Hive"""
    policy_count = 0  # number of policies
    return policy_count
```

## 📦 包管理规范
- **强制使用uv**: 所有Python包管理操作必须使用uv，禁用pip
- **依赖管理**: 通过 [pyproject.toml](mdc:pyproject.toml) 管理所有依赖

```bash
# ✅ 正确的包管理命令
uv add flask>=2.3.0        # 添加依赖
uv add --dev pytest        # 添加开发依赖
uv sync                     # 同步依赖
uv run python main.py       # 运行脚本

# ❌ 禁止使用
pip install flask           # 禁止使用pip
poetry add flask           # 禁止使用poetry
```

## 🏗️ 项目架构规范
- **分层架构**: 严格遵循 Controller → Service → Model 分层
- **单一职责**: 每个模块只负责一个特定功能
- **统一目录**: 相同类型的文件放在统一目录下

### 目录结构标准
```
app/
├── models/nodes/          # 节点模型（统一目录）
├── services/
│   ├── node_service/      # 节点服务
│   └── relationship_service/  # 关系服务
├── controllers/           # 控制器（统一目录）
└── utils/                # 工具模块

scripts/
├── data_import/          # 数据导入脚本
├── data_verification/    # 数据验证脚本
├── maintenance/          # 维护脚本
└── development/          # 开发工具脚本
```

## 🔧 代码质量
- **格式化**: 使用 black 进行代码格式化
- **类型检查**: 使用 mypy 进行类型检查  
- **代码检查**: 使用 flake8 进行代码质量检查

```bash
# 代码质量检查流程
uv run black app/           # 格式化代码
uv run flake8 app/          # 检查代码质量
uv run mypy app/            # 类型检查
uv run pytest              # 运行测试
```

## 💡 服务开发标准

### 节点服务规范
所有节点服务必须继承 `BasePaginationService`：

```python
class MyEntityService(BasePaginationService):
    """实体服务类"""
    
    def __init__(self):
        super().__init__()
        self.transformer = MyEntityTransformer()
    
    def _build_query(self, limit: int, offset: int = 0) -> str:
        """构建分页查询，必须包含动态日期分区"""
        yesterday = (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        return f"SELECT * FROM table WHERE day = '{yesterday}' LIMIT {limit} OFFSET {offset}"
    
    def _transform_data(self, df: pd.DataFrame) -> List[BaseNode]:
        """使用转换器处理数据"""
        return self.transformer.transform_data(df)
    
    def _store_data(self, data: List[BaseNode]) -> bool:
        """存储到Neo4j"""
        return self.storage.store_nodes(data)
```

### 关系服务规范
所有关系服务必须继承 `BaseRelationshipService`：

```python
class MyEntityRelationshipService(BaseRelationshipService):
    """实体关系服务类"""
    
    def __init__(self):
        super().__init__()
        self.source_node_type = "SourceNode"
        self.target_node_type = "TargetNode" 
        self.relationship_type = "MY_RELATIONSHIP"
    
    def _build_relationship_query(self, limit: int = None) -> str:
        """构建关系数据查询"""
        limit_clause = f"LIMIT {limit}" if limit else ""
        return f"SELECT source_code, target_code FROM table {limit_clause}"
    
    def _transform_relationship_data(self, df: pd.DataFrame) -> List[Dict]:
        """转换为关系数据格式"""
        relationships = []
        for _, row in df.iterrows():
            relationships.append({
                'source_code': str(row['source_code']),
                'target_code': str(row['target_code']),
                'relationship_type': self.relationship_type
            })
        return relationships
```

## 📝 命名约定
- **文件名**: 使用snake_case，如 `insurance_product_service.py`
- **类名**: 使用PascalCase，如 `InsuranceProductService`
- **变量名**: 使用snake_case，如 `product_code`
- **常量名**: 使用UPPER_CASE，如 `DEFAULT_PAGE_SIZE`

### 服务类命名规范
- **节点服务**: `{Entity}Service` (如 `PolicyService`)
- **关系服务**: `{Entity}RelationshipService` (如 `PolicyInsuranceCompanyRelationshipService`)

## 🔍 SQL查询最佳实践

### 推荐子查询模式
```sql
-- ✅ 推荐：子查询模式（支持分页）
SELECT tcb.field1, tcb.field2
FROM base_table tcb
WHERE tcb.key_field IN (
    SELECT key_field FROM (
        SELECT t1.key_field FROM source_table1 t1 WHERE t1.condition1
        UNION
        SELECT t2.key_field FROM source_table2 t2 WHERE t2.condition2
    ) tmp
    ORDER BY key_field ASC
)
AND tcb.additional_conditions
ORDER BY tcb.key_field ASC
LIMIT {limit} OFFSET {offset}
```

## 📋 API设计规范

### 标准端点模式
```python
# 每个功能模块提供标准端点：
@app.route('/api/v1/resource/extract', methods=['POST'])  # 数据提取
@app.route('/api/v1/resource/search', methods=['GET'])    # 搜索查询
@app.route('/api/v1/resource/statistics', methods=['GET']) # 统计信息
@app.route('/api/v1/resource/delete-all', methods=['DELETE']) # 删除所有
@app.route('/api/v1/resource/health', methods=['GET'])    # 健康检查
```

### 响应格式标准
```python
# 成功响应
{
    "success": true,
    "message": "操作描述",
    "data": {...},
    "timestamp": "2024-01-01T10:00:00"
}

# 错误响应
{
    "success": false,
    "error": "错误类型",
    "message": "详细错误描述",
    "timestamp": "2024-01-01T10:00:00"
}
```

## 🛠️ 数据导入标准

### ImportHelper使用规范
所有数据导入脚本必须使用 [ImportHelper](mdc:app/utils/import_helper.py)：

```python
from app.utils.import_helper import ImportHelper
import argparse

def main():
    parser = argparse.ArgumentParser(description='数据导入脚本')
    parser.add_argument('--force', action='store_true', help='强制执行，跳过确认提示')
    args = parser.parse_args()
    
    helper = ImportHelper("模块名称")
    helper.print_header("数据导入标题")
    
    try:
        # 确认清理现有数据
        if helper.confirm_overwrite(args.force, "数据类型描述"):
            helper.execute_with_progress("清理现有数据", service.delete_method)
        else:
            print("用户取消操作")
            return
        
        # 导入数据
        result = helper.execute_with_progress("导入数据", service.import_method)
        helper.print_import_result(result, "导入操作")
        helper.validate_import(service.get_statistics, min_count=1)
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        helper.logger.error(f"导入失败: {str(e)}")
        sys.exit(1)
    
    helper.print_footer()

if __name__ == "__main__":
    main()
```

## 🧪 测试规范

### 单元测试
```python
import pytest
from app.services.insurance_product_service import InsuranceProductService

class TestInsuranceProductService:
    """保险产品服务测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.service = InsuranceProductService()
    
    def test_extract_and_store_data(self):
        """测试数据提取和存储"""
        result = self.service.extract_and_store_data()
        assert result['success'] is True
        assert result['extracted_count'] > 0
```

### 集成测试
```python
def test_full_data_pipeline():
    """测试完整数据流水线"""
    # 1. 清理数据
    # 2. 导入数据
    # 3. 验证结果
    # 4. 清理测试数据
```

## 🚫 严格禁止事项
- ❌ 在项目根目录创建任何 `.md` 文件
- ❌ 在根目录创建测试文件
- ❌ 在非 `docs/` 目录创建文档
- ❌ 在 `scripts/` 根目录直接放置脚本
- ❌ 使用 pip 或 poetry 进行包管理
- ❌ 使用英文编写注释、日志、文档

## ✅ 正确的文件位置
- 测试文件 → `tests/unit/` 或 `tests/integration/`
- 文档文件 → `docs/guides/` 或 `docs/specifications/`
- 导入脚本 → `scripts/data_import/`
- 验证脚本 → `scripts/data_verification/`
- 维护脚本 → `scripts/maintenance/`

## 📊 性能监控标准
- **节点服务**: 处理速度应达到1000-5000记录/分钟
- **关系服务**: 关系创建应在秒级完成
- **内存使用**: 分批处理避免内存溢出
- **数据库连接**: 合理使用连接池

## 🔄 错误处理标准
```python
try:
    result = self.some_operation()
    self.logger.info(f"操作成功: {result}")
except Exception as e:
    self.logger.error(f"操作失败: {str(e)}")
    raise
```

## 📋 代码审查清单
- [ ] 继承正确的基类
- [ ] 实现所有抽象方法
- [ ] 包含完整错误处理
- [ ] 添加适当的日志记录
- [ ] 遵循命名约定
- [ ] 包含必需的文档字符串
- [ ] 通过类型检查
- [ ] 包含单元测试
- [ ] 所有注释使用中文
