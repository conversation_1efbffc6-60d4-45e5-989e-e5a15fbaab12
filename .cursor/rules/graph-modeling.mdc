---
description: Neo4j图数据建模和关系设计规范
---

# Neo4j 图数据建模规范

## 节点设计规范

### 节点类型约定
- **Person节点**: 人员相关实体（代理人、BD、MO、被保人等）
- **Organization节点**: 组织机构（保险公司、经纪公司、销售渠道等）
- **Product节点**: 产品信息（保险产品）
- **Policy节点**: 保单相关（保险单据）
- **Area节点**: 地理区域信息

### 节点属性规范
- 每个节点必须有唯一标识符（通常是 `id` 或 `code`）
- 使用清晰的中文属性名，如 `姓名`、`编码`、`创建时间`
- 时间属性统一使用 ISO 格式
- 状态字段使用枚举值，在模型中明确定义

## 关系设计规范

### 关系命名约定
- 使用动词短语表示关系，如 `属于`、`管理`、`承保`
- 关系方向要明确，从主体指向客体
- 复杂关系可添加属性，如关系建立时间、权重等

### 常见关系模式
- **层级关系**: 组织架构、地理区域层次
- **归属关系**: 人员与组织、产品与公司
- **业务关系**: 保单与被保人、代理人与客户

## 服务层实现

### 节点服务规范
参考 [app/services/node_service/](mdc:app/services/node_service/) 下的服务类：
- 继承基础服务类进行扩展
- 实现标准的 CRUD 操作
- 提供分页查询支持
- 包含业务特定的查询方法

### 关系服务规范
参考 [app/services/relationship_service/](mdc:app/services/relationship_service/) 下的服务类：
- 处理节点间的关系建立和查询
- 支持批量关系操作
- 提供关系路径查询功能

## 数据转换规范

使用 [app/services/data_transform/node_transformers/](mdc:app/services/data_transform/node_transformers/) 进行数据转换：
- 源数据清洗和标准化
- 业务规则验证
- 数据格式统一
