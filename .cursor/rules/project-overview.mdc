# 保智鸟关系图谱服务器项目结构指南

这是一个基于 FastAPI 和 Neo4j 的保险业务关系图谱服务器项目。

## 核心组件结构

- **入口点**: [main.py](mdc:main.py) - 应用主入口
- **配置**: [app/config/](mdc:app/config/) - 包含 Neo4j、Impala 等数据库配置
- **模型层**: [app/models/](mdc:app/models/) - 节点和关系的数据模型定义
- **服务层**: [app/services/](mdc:app/services/) - 业务逻辑和数据处理服务
- **控制器**: [app/controllers/](mdc:app/controllers/) - API 接口层
- **工具**: [app/utils/](mdc:app/utils/) - 通用工具和辅助函数

## 领域模型

项目专注于保险业务领域，主要包含以下核心实体：
- 人员节点（Person）：代理人、BD、MO、被保人等
- 组织节点（Organization）：保险公司、经纪公司、渠道等  
- 产品节点（Product）：保险产品
- 保单节点（Policy）：保险单据
- 区域节点（Area）：地理区域信息

## 开发规范

- 使用 uv 作为 Python 包管理工具
- 遵循 MVC 架构模式
- 所有 Neo4j 操作通过服务层封装
- 数据转换通过专门的 transformer 处理

- 🇨🇳 **中文优先**: 所有代码注释、文档、日志都使用中文
- ⚡ **uv 优先**: 所有Python包管理操作使用uv命令
- 🔄 **数据管道**: 完整的 Hive → Neo4j 数据流水线
- 🔍 **图查询**: 基于Neo4j的复杂关系查询
