---
globs: *.py
---

# Python 开发规范

## 包管理
- **必须使用 uv**: 所有 Python 包操作使用 `uv` 命令而非 pip
  - 安装依赖: `uv add package_name`
  - 安装开发依赖: `uv add --dev package_name`
  - 运行脚本: `uv run python script.py`
  - 同步依赖: `uv sync`

## 代码规范
- **中文注释**: 所有注释、文档字符串使用中文
- **类型提示**: 所有函数参数和返回值都应有类型提示
- **日志记录**: 使用项目统一的 logger，参考 [app/utils/logger.py](mdc:app/utils/logger.py)

## 项目架构模式
- **服务层模式**: 业务逻辑放在 `services/` 目录下的服务类中
- **模型层**: 数据模型定义在 `models/` 目录，继承自 [app/models/base/base_model.py](mdc:app/models/base/base_model.py)
- **控制器层**: API 接口在 `controllers/` 目录，使用 FastAPI 装饰器

## Neo4j 操作规范
- 所有 Neo4j 查询通过服务层封装，参考 [app/services/data_storage/neo4j_client.py](mdc:app/services/data_storage/neo4j_client.py)
- 使用参数化查询防止注入攻击
- 大批量操作使用事务处理

## 错误处理
- 使用适当的异常类型
- 记录详细的错误日志
- 对外接口返回标准化的错误响应
