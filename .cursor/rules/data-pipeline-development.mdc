---
description: 
globs: 
alwaysApply: false
---
# 数据管道开发规范

## 核心文件位置

- **数据提取**: [app/services/data_extraction/](mdc:app/services/data_extraction)
- **数据转换**: [app/services/data_transform/](mdc:app/services/data_transform)
- **数据存储**: [app/services/data_storage/](mdc:app/services/data_storage)
- **数据管道脚本**: [scripts/data_import/](mdc:scripts/data_import)
- **环境配置**: [env.example](mdc:env.example)

## 数据管道架构

### 三层架构模式
```
Hive/Impala (数据源) 
    ↓ 
数据提取层 (data_extraction)
    ↓
数据转换层 (data_transform) 
    ↓
数据存储层 (data_storage)
    ↓
Neo4j (目标存储)
```

## 数据提取规范

### 1. Hive 查询设计
```python
# ✅ 推荐的查询结构
class PersonDataExtractor:
    """人员数据提取器"""
    
    def get_extract_query(self, limit: int = 10000, offset: int = 0) -> str:
        """构建提取查询"""
        return f"""
        SELECT 
            person_id,
            person_name,
            id_card,
            phone,
            address,
            create_time
        FROM insurance.person_info 
        WHERE person_id IS NOT NULL
            AND person_name IS NOT NULL
        ORDER BY create_time DESC
        LIMIT {limit} OFFSET {offset}
        """
```

### 2. 分页提取策略
```python
async def extract_data_in_batches(
    extractor: BaseExtractor, 
    batch_size: int = 10000
) -> AsyncGenerator[List[Dict], None]:
    """分批提取数据"""
    offset = 0
    
    while True:
        # 执行分页查询
        query = extractor.get_extract_query(batch_size, offset)
        batch_data = await execute_hive_query(query)
        
        if not batch_data:
            break
            
        logger.info(f"提取到 {len(batch_data)} 条记录，偏移量：{offset}")
        yield batch_data
        
        offset += batch_size
        
        # 避免过快查询导致数据库压力
        await asyncio.sleep(0.1)
```

### 3. 错误处理和重试
```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def execute_hive_query(query: str) -> List[Dict]:
    """执行 Hive 查询，支持重试"""
    try:
        result = await hive_client.execute(query)
        return result.fetchall()
    except Exception as e:
        logger.error(f"Hive 查询失败：{query[:100]}... - 错误：{e}")
        raise
```

## 数据转换规范

### 1. 数据清洗规则
```python
class PersonDataTransformer:
    """人员数据转换器"""
    
    def clean_phone_number(self, phone: str) -> Optional[str]:
        """清洗电话号码"""
        if not phone:
            return None
            
        # 移除非数字字符
        clean_phone = re.sub(r'\D', '', phone)
        
        # 验证手机号格式
        if len(clean_phone) == 11 and clean_phone.startswith('1'):
            return clean_phone
            
        logger.warning(f"无效电话号码：{phone}")
        return None
    
    def clean_id_card(self, id_card: str) -> Optional[str]:
        """清洗身份证号"""
        if not id_card:
            return None
            
        # 移除空格和特殊字符
        clean_id = re.sub(r'[^\dxX]', '', id_card.upper())
        
        # 验证身份证格式
        if len(clean_id) in [15, 18]:
            return clean_id
            
        logger.warning(f"无效身份证号：{id_card}")
        return None
```

### 2. 数据加密处理
```python
from app.utils.encryption import encrypt_sensitive_data

def transform_person_data(raw_data: Dict) -> PersonNode:
    """转换人员数据"""
    
    # 清洗数据
    clean_phone = clean_phone_number(raw_data.get('phone'))
    clean_id_card = clean_id_card(raw_data.get('id_card'))
    
    # 加密敏感信息
    encrypted_id_card = encrypt_sensitive_data(clean_id_card) if clean_id_card else None
    encrypted_phone = encrypt_sensitive_data(clean_phone) if clean_phone else None
    
    return PersonNode(
        id=raw_data['person_id'],
        name=raw_data['person_name'],
        id_card=encrypted_id_card,
        phone=encrypted_phone,
        address=raw_data.get('address'),
        create_time=raw_data.get('create_time')
    )
```

### 3. 数据验证
```python
from pydantic import ValidationError

def validate_transformed_data(nodes: List[BaseNode]) -> Tuple[List[BaseNode], List[Dict]]:
    """验证转换后的数据"""
    valid_nodes = []
    invalid_records = []
    
    for node in nodes:
        try:
            # Pydantic 自动验证
            node.model_validate(node.model_dump())
            valid_nodes.append(node)
        except ValidationError as e:
            invalid_records.append({
                'node_data': node.model_dump(),
                'errors': e.errors()
            })
            logger.warning(f"数据验证失败：{e}")
    
    return valid_nodes, invalid_records
```

## 数据存储规范

### 1. 批量插入策略
```python
async def batch_insert_nodes(
    nodes: List[BaseNode], 
    batch_size: int = 1000
) -> Dict[str, int]:
    """批量插入节点"""
    stats = {'success': 0, 'failed': 0}
    
    for i in range(0, len(nodes), batch_size):
        batch = nodes[i:i + batch_size]
        
        try:
            await insert_node_batch(batch)
            stats['success'] += len(batch)
            logger.info(f"成功插入 {len(batch)} 个节点")
        except Exception as e:
            stats['failed'] += len(batch)
            logger.error(f"批量插入失败：{e}")
            
            # 尝试单个插入以识别具体问题
            for node in batch:
                try:
                    await insert_single_node(node)
                    stats['success'] += 1
                except Exception as single_error:
                    stats['failed'] += 1
                    logger.error(f"单个节点插入失败：{node.id} - {single_error}")
    
    return stats
```

### 2. 重复数据处理
```python
def create_upsert_query(node: BaseNode) -> Tuple[str, Dict]:
    """创建 MERGE 查询，处理重复数据"""
    labels = ':'.join(node.__neo4j_labels__)
    
    # 使用主键进行 MERGE
    query = f"""
    MERGE (n:{labels} {{id: $id}})
    SET n += $properties
    RETURN n
    """
    
    params = {
        'id': node.id,
        'properties': node.model_dump(exclude={'id'})
    }
    
    return query, params
```

### 3. 关系建立
```python
async def create_relationships_after_nodes(
    source_data: List[Dict]
) -> Dict[str, int]:
    """在节点创建后建立关系"""
    stats = {'success': 0, 'failed': 0}
    
    for record in source_data:
        try:
            # 根据业务逻辑创建关系
            if record.get('policy_id') and record.get('person_id'):
                await create_policy_holder_relationship(
                    person_id=record['person_id'],
                    policy_id=record['policy_id'],
                    properties={
                        'start_date': record.get('policy_start_date'),
                        'premium': record.get('premium')
                    }
                )
                stats['success'] += 1
                
        except Exception as e:
            stats['failed'] += 1
            logger.error(f"关系创建失败：{record} - {e}")
    
    return stats
```

## 数据管道监控

### 1. 进度跟踪
```python
from dataclasses import dataclass
from datetime import datetime

@dataclass
class PipelineProgress:
    """管道进度跟踪"""
    start_time: datetime
    total_records: int
    processed_records: int
    success_records: int
    failed_records: int
    current_stage: str
    
    @property
    def progress_percentage(self) -> float:
        if self.total_records == 0:
            return 0.0
        return (self.processed_records / self.total_records) * 100
    
    def log_progress(self):
        """记录进度日志"""
        logger.info(
            f"管道进度 - 阶段：{self.current_stage} | "
            f"进度：{self.progress_percentage:.1f}% | "
            f"成功：{self.success_records} | "
            f"失败：{self.failed_records}"
        )
```

### 2. 性能监控
```python
import time
from contextlib import contextmanager

@contextmanager
def monitor_stage_performance(stage_name: str):
    """监控阶段性能"""
    start_time = time.time()
    start_memory = get_memory_usage()
    
    logger.info(f"开始执行阶段：{stage_name}")
    
    try:
        yield
    finally:
        end_time = time.time()
        end_memory = get_memory_usage()
        
        execution_time = end_time - start_time
        memory_usage = end_memory - start_memory
        
        logger.info(
            f"阶段完成：{stage_name} | "
            f"耗时：{execution_time:.2f}秒 | "
            f"内存使用：{memory_usage:.2f}MB"
        )
```

## 错误处理和恢复

### 1. 断点续传
```python
class PipelineCheckpoint:
    """管道检查点"""
    
    def __init__(self, checkpoint_file: str):
        self.checkpoint_file = checkpoint_file
    
    def save_checkpoint(self, stage: str, offset: int, metadata: Dict):
        """保存检查点"""
        checkpoint_data = {
            'stage': stage,
            'offset': offset,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata
        }
        
        with open(self.checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f)
    
    def load_checkpoint(self) -> Optional[Dict]:
        """加载检查点"""
        try:
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
```

### 2. 数据回滚机制
```python
async def rollback_pipeline_data(pipeline_id: str):
    """回滚管道数据"""
    logger.info(f"开始回滚管道数据：{pipeline_id}")
    
    # 删除本次导入的节点和关系
    rollback_query = """
    MATCH (n)
    WHERE n.pipeline_id = $pipeline_id
    DETACH DELETE n
    """
    
    await neo4j_session.run(rollback_query, {'pipeline_id': pipeline_id})
    logger.info(f"管道数据回滚完成：{pipeline_id}")
```

## 配置管理

### 1. 管道配置
```python
from pydantic_settings import BaseSettings

class PipelineConfig(BaseSettings):
    """管道配置"""
    
    # 提取配置
    hive_batch_size: int = 10000
    max_retries: int = 3
    retry_delay: int = 5
    
    # 转换配置
    enable_data_encryption: bool = True
    skip_invalid_records: bool = True
    
    # 存储配置
    neo4j_batch_size: int = 1000
    enable_upsert: bool = True
    
    # 监控配置
    log_progress_interval: int = 1000
    enable_performance_monitoring: bool = True
    
    class Config:
        env_prefix = "PIPELINE_"
```

### 2. 环境配置验证
```python
def validate_pipeline_environment():
    """验证管道运行环境"""
    required_configs = [
        'IMPALA_HOST',
        'IMPALA_PORT', 
        'NEO4J_URI',
        'NEO4J_USERNAME',
        'ENCRYPTION_KEY'
    ]
    
    missing_configs = []
    for config in required_configs:
        if not os.getenv(config):
            missing_configs.append(config)
    
    if missing_configs:
        raise ValueError(f"缺少必要的环境配置：{missing_configs}")
    
    logger.info("管道环境配置验证通过")
```

## 最佳实践总结

1. **数据一致性**：使用事务确保数据的原子性操作
2. **性能优化**：合理设置批处理大小，避免内存溢出
3. **容错机制**：实现重试、回滚和断点续传功能
4. **监控日志**：详细记录处理进度和错误信息
5. **配置管理**：使用环境变量管理不同环境的配置
6. **数据验证**：在每个阶段进行数据完整性检查
7. **资源管理**：及时释放数据库连接和内存资源

遵循这些规范可以确保数据管道的稳定性、可维护性和高性能运行。
