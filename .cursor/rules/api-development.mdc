---
description: FastAPI接口开发规范和最佳实践
---

# FastAPI 接口开发规范

## 控制器结构规范

遵循 [app/controllers/](mdc:app/controllers/) 的模块化结构：
- **节点控制器**: [app/controllers/nodes/](mdc:app/controllers/nodes/) - 处理节点相关的API
- **关系控制器**: [app/controllers/relationships/](mdc:app/controllers/relationships/) - 处理关系相关的API  
- **图查询控制器**: [app/controllers/graph/](mdc:app/controllers/graph/) - 处理复杂图查询
- **系统控制器**: [app/controllers/system/](mdc:app/controllers/system/) - 系统健康检查等

## 接口设计规范

### 路由命名
- 使用中文路径参数描述，如 `/nodes/person/代理人`
- RESTful 风格：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 版本控制：使用路径前缀 `/api/v1/`

### 请求/响应格式
- **统一响应格式**：包含 `code`、`message`、`data` 字段
- **分页响应**：使用统一的分页结构，参考 [app/services/base_pagination_service.py](mdc:app/services/base_pagination_service.py)
- **错误处理**：返回标准HTTP状态码和错误信息

### 输入验证
- 使用 Pydantic 模型进行请求参数验证
- 自定义验证器处理业务规则，参考 [app/utils/validators.py](mdc:app/utils/validators.py)
- 敏感数据使用加密处理，参考 [app/utils/encryption.py](mdc:app/utils/encryption.py)

## 性能优化规范

### 查询优化
- 复杂图查询使用异步处理
- 大数据量查询实现分页和流式返回
- 合理使用缓存机制

### 日志记录
- 所有API调用记录访问日志
- 错误信息详细记录，使用统一logger
- 性能指标监控，记录响应时间

## 安全规范

### 数据安全
- 敏感字段加密存储
- 查询参数验证防止注入攻击
- 接口访问频率限制

### 权限控制
- 实现基于角色的访问控制
- API密钥验证
- 操作审计日志记录
