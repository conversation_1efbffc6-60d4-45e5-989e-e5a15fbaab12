---
description: 
globs: 
alwaysApply: false
---
# 项目架构指南

## 🏗️ 分层架构
项目严格遵循三层架构模式：

```
Controller Layer (控制器层)
    ↓
Service Layer (服务层)  
    ↓
Model Layer (模型层)
```

## 📂 目录结构规范

### app/ - 应用核心
```
app/
├── models/                   # 数据模型层
│   ├── base/                # 基础模型和抽象类
│   ├── nodes/               # 节点模型（统一目录）
│   └── relationships/       # 关系模型
├── services/                # 服务层
│   ├── node_service/        # 节点相关服务
│   ├── relationship_service/ # 关系相关服务
│   ├── data_extraction/     # 数据提取服务
│   ├── data_transform/      # 数据转换服务
│   └── data_storage/        # 数据存储服务
├── controllers/             # 控制器层（统一目录）
├── config/                  # 配置管理
└── utils/                   # 工具模块
```

### scripts/ - 脚本目录（严格分类）
```
scripts/
├── data_import/            # 数据导入脚本
│   ├── import_policies.py      # 节点导入示例
│   └── import_policy_relationships.py # 关系导入示例
├── data_verification/      # 数据验证脚本
│   └── verify_data_integrity.py
├── maintenance/            # 维护脚本
│   └── cleanup_expired_data.py
└── development/            # 开发工具脚本
    └── test_connection.py
```

### tests/ - 测试目录
```
tests/
├── unit/                   # 单元测试
│   ├── test_services/      # 服务层测试
│   ├── test_models/        # 模型层测试
│   └── test_utils/         # 工具类测试
├── integration/            # 集成测试
│   └── test_api_endpoints/ # API端点测试
└── fixtures/               # 测试数据
    └── sample_data.json
```

## 🔧 核心组件

### 服务层设计
- **节点服务**: 继承 `BasePaginationService`
- **关系服务**: 继承 `BaseRelationshipService` 
- **数据转换**: 继承 `BaseNodeTransformer`

### 关键文件参考
- [BasePaginationService](mdc:app/services/base_pagination_service.py): 分页服务基类
- [BaseNode](mdc:app/models/base/base_model.py): 节点模型基类
- [ImportHelper](mdc:app/utils/import_helper.py): 导入助手工具

## 🚫 严格禁止事项
- ❌ 在项目根目录创建任何 `.md` 文件
- ❌ 在根目录创建测试文件
- ❌ 在非 `docs/` 目录创建文档
- ❌ 在 `scripts/` 根目录直接放置脚本
- ❌ 跨目录功能混合

## ✅ 正确的文件位置
- 测试文件 → `tests/unit/` 或 `tests/integration/`
- 文档文件 → `docs/guides/` 或 `docs/specifications/`
- 导入脚本 → `scripts/data_import/`
- 验证脚本 → `scripts/data_verification/`
- 维护脚本 → `scripts/maintenance/`
