---
description: 
globs: 
alwaysApply: false
---
# 中文优先原则

## 🇨🇳 核心理念
所有开发活动必须遵循"中文优先"原则，确保团队协作和系统维护的高效性。

## 📝 代码注释规范

### 必须使用中文
```python
# ✅ 正确示例
def extract_policy_data():
    """从Hive提取保单数据并转换为Neo4j节点"""
    policy_count = 0  # 保单数量
    processed_count = 0  # 已处理数量
    
    # 分页查询数据
    for batch in paginated_query():
        # 转换数据格式
        nodes = transform_to_nodes(batch)
        # 存储到Neo4j
        store_to_neo4j(nodes)
        processed_count += len(nodes)
    
    return processed_count

# ❌ 错误示例  
def extract_policy_data():
    """Extract policy data from Hive and convert to Neo4j nodes"""
    policy_count = 0  # number of policies
    processed_count = 0  # processed count
    return processed_count
```

### 函数和类文档字符串
```python
class PolicyService:
    """保单服务类
    
    提供保单数据的提取、转换、存储和查询功能。
    继承BasePaginationService以支持大数据集的分页处理。
    """
    
    def extract_and_store_data(self, force: bool = False) -> Dict[str, Any]:
        """从Hive提取保单数据并存储到Neo4j
        
        Args:
            force (bool): 是否强制覆盖现有数据，默认False
            
        Returns:
            Dict[str, Any]: 包含处理结果的字典
                - success: bool, 操作是否成功
                - extracted_count: int, 提取的记录数
                - stored_count: int, 存储的记录数
                - duration: float, 处理耗时（秒）
                
        Raises:
            ConnectionError: 数据库连接失败时抛出
            ValueError: 数据格式不正确时抛出
        """
        pass
```

## 📋 日志记录规范

### 系统日志中文化
```python
import logging

logger = logging.getLogger(__name__)

# ✅ 正确的日志记录
logger.info("开始提取保单数据，预期处理 1000 条记录")
logger.warning("发现 15 条重复数据，已自动去重")
logger.error("Neo4j连接失败，正在重试连接")
logger.debug("当前批次处理完成，已处理 500 条记录")

# ❌ 错误的日志记录
logger.info("Starting policy data extraction for 1000 records")
logger.warning("Found 15 duplicate records, auto deduplication applied")
logger.error("Neo4j connection failed, retrying connection")
```

### 错误信息中文化
```python
# ✅ 正确的错误处理
try:
    result = process_data(data)
    logger.info(f"数据处理成功，共处理 {len(result)} 条记录")
except ConnectionError as e:
    logger.error(f"数据库连接失败: {str(e)}")
    raise Exception("无法连接到Neo4j数据库，请检查网络和配置")
except ValueError as e:
    logger.error(f"数据格式错误: {str(e)}")
    raise Exception("数据格式不符合要求，请检查源数据质量")

# ❌ 错误的错误处理
try:
    result = process_data(data)
    logger.info(f"Data processing successful, processed {len(result)} records")
except ConnectionError as e:
    logger.error(f"Database connection failed: {str(e)}")
    raise Exception("Unable to connect to Neo4j database")
```

## 🌐 API响应中文化

### 响应消息
```python
# ✅ 正确的API响应
{
    "success": true,
    "message": "保单数据提取成功",
    "data": {
        "extracted_count": 1500,
        "stored_count": 1485,
        "duration": 45.2
    }
}

# 错误响应
{
    "success": false,
    "error": "数据提取失败",
    "message": "Hive数据库连接超时，请稍后重试",
    "error_code": "HIVE_CONNECTION_TIMEOUT"
}

# ❌ 错误的API响应
{
    "success": true,
    "message": "Policy data extraction successful",
    "data": {...}
}
```

### 验证错误消息
```python
# ✅ 正确的参数验证
def validate_limit(limit: int) -> str:
    if limit <= 0:
        return "limit参数必须大于0"
    if limit > 10000:
        return "limit参数不能超过10000"
    return ""

# API端点中的使用
limit = request.args.get('limit', 10, type=int)
error_msg = validate_limit(limit)
if error_msg:
    return jsonify({
        "success": False,
        "error": "参数验证失败",
        "message": error_msg
    }), 400
```

## 📖 文档规范

### README和文档
- 所有README文件使用中文编写
- API文档使用中文描述
- 配置说明使用中文
- 部署指南使用中文

### 配置文件注释
```bash
# ✅ 正确的环境变量配置
# Neo4j 图数据库配置
NEO4J_URI=bolt://*************:7687
NEO4J_USERNAME=your_username    # Neo4j用户名
NEO4J_PASSWORD=your_password    # Neo4j密码
NEO4J_DATABASE=neo4j            # 数据库名称

# Hive/Impala 数据仓库配置  
IMPALA_HOST=*************       # Impala服务器地址
IMPALA_PORT=21050               # Impala端口号
IMPALA_DATABASE=default         # 默认数据库
```

## 🔧 工具配置中文化

### Git提交信息
```bash
# ✅ 正确的提交信息
git commit -m "新增保单数据提取功能"
git commit -m "修复Neo4j连接池内存泄漏问题"
git commit -m "优化数据转换性能，提升30%处理速度"

# ❌ 错误的提交信息
git commit -m "Add policy data extraction feature"
git commit -m "Fix Neo4j connection pool memory leak"
```

### 分支命名
```bash
# ✅ 推荐的分支命名（可以使用英文）
feature/policy-data-extraction
bugfix/neo4j-connection-pool
hotfix/data-validation-issue

# 但提交信息和PR描述必须中文
```

## 📊 代码审查要求

### 审查检查点
- [ ] 所有函数都有中文文档字符串
- [ ] 所有注释使用中文
- [ ] 日志信息使用中文
- [ ] 错误信息使用中文
- [ ] API响应消息使用中文
- [ ] 变量名虽然用英文，但有中文注释说明

### 审查模板
```markdown
## 代码审查清单

### 中文优先原则检查
- [ ] 函数文档字符串使用中文
- [ ] 行内注释使用中文  
- [ ] 日志记录使用中文
- [ ] 错误信息使用中文
- [ ] API响应使用中文

### 其他检查项
- [ ] 代码逻辑正确
- [ ] 性能表现良好
- [ ] 测试覆盖充分
```

## 🚫 严格禁止

### 禁用英文的场景
- ❌ 函数和类的文档字符串
- ❌ 代码行内注释
- ❌ 日志记录信息
- ❌ 错误提示信息
- ❌ API响应消息
- ❌ 配置文件说明
- ❌ README文档内容

### 可以使用英文的场景
- ✅ 变量名、函数名、类名（但需要中文注释解释）
- ✅ Git分支名称
- ✅ 技术术语（如Neo4j、Cypher等）
- ✅ 第三方库和框架名称

## 🎯 执行标准

### 新代码要求
- 100% 中文注释覆盖
- 所有用户面向信息中文化
- 日志信息完全中文化

### 旧代码改造
- 逐步改造现有英文注释
- 优先改造用户面向的信息
- 保持向后兼容性
