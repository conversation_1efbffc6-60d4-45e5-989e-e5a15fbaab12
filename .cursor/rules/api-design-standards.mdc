---
description: 
globs: 
alwaysApply: false
---
# API 设计标准

## 🌐 RESTful API 规范

### 标准端点模式
每个业务模块提供统一的API接口：

```bash
# 核心CRUD操作
POST   /api/v1/{resource}/extract      # 从Hive提取数据
GET    /api/v1/{resource}/search       # 搜索查询数据
GET    /api/v1/{resource}/statistics   # 获取统计信息
DELETE /api/v1/{resource}/delete-all   # 删除所有数据（管理员）
GET    /api/v1/{resource}/health       # 服务健康检查
```

### 关系API端点
```bash
POST   /api/v1/relationships/{type}/extract      # 提取关系数据
GET    /api/v1/relationships/{type}/search       # 搜索关系
GET    /api/v1/relationships/{type}/statistics   # 关系统计
DELETE /api/v1/relationships/{type}/delete-all   # 删除所有关系
GET    /api/v1/relationships/{type}/health       # 关系服务健康检查
```

## 📝 响应格式标准

### 成功响应
```json
{
    "success": true,
    "message": "操作描述",
    "data": {...},
    "total_count": 123,
    "timestamp": "2024-01-01T10:00:00"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误类型",
    "message": "详细错误描述",
    "timestamp": "2024-01-01T10:00:00"
}
```

### 统计响应
```json
{
    "success": true,
    "data": {
        "total_count": 123,
        "last_updated": "2024-01-01T10:00:00",
        "data_source": "Hive",
        "status": "active"
    }
}
```

## 🔍 参数处理规范

### 搜索参数
```python
# 标准搜索参数
code = request.args.get('code', '')          # 精确匹配代码
name = request.args.get('name', '')          # 模糊匹配名称
limit = min(int(request.args.get('limit', 10)), 1000)  # 限制最大1000

# 参数验证
if limit <= 0:
    return {"success": False, "error": "limit必须大于0"}
```

### 提取参数
```python
# 数据提取参数
force = request.form.get('force', 'false').lower() == 'true'
limit = request.form.get('limit', type=int)
day = request.form.get('day')  # 可选日期参数
```

## 🛠️ 控制器实现标准

### 基础控制器结构
```python
from flask import Blueprint, request, jsonify
from app.services.{entity}_service import {Entity}Service

{entity}_blueprint = Blueprint('{entity}', __name__)
service = {Entity}Service()

@{entity}_blueprint.route('/api/v1/{entity}/extract', methods=['POST'])
def extract_data():
    """从Hive提取数据并存储到Neo4j"""
    try:
        force = request.form.get('force', 'false').lower() == 'true'
        result = service.extract_and_store_data(force=force)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
```

### 错误处理标准
```python
@app.errorhandler(Exception)
def handle_error(e):
    logger.error(f"API错误: {str(e)}")
    return jsonify({
        "success": False,
        "error": str(e),
        "message": "服务器内部错误"
    }), 500

@app.errorhandler(404)
def not_found(e):
    return jsonify({
        "success": False,
        "error": "资源未找到",
        "message": "请检查请求的URL路径"
    }), 404
```

## 📋 蓝图注册规范

### 在 [main.py](mdc:main.py) 中注册
```python
# 导入控制器蓝图
from app.controllers.insurance_product_controller import insurance_product_blueprint
from app.controllers.policy_controller import policy_blueprint

# 注册蓝图
app.register_blueprint(insurance_product_blueprint)
app.register_blueprint(policy_blueprint)
```

## 🔒 安全性规范

### 危险操作保护
```python
@{entity}_blueprint.route('/api/v1/{entity}/delete-all', methods=['DELETE'])
def delete_all():
    """危险操作 - 删除所有数据"""
    # 需要额外确认
    confirm = request.form.get('confirm', '').lower()
    if confirm != 'yes':
        return jsonify({
            "success": False,
            "error": "需要确认参数",
            "message": "请设置 confirm=yes 确认删除操作"
        }), 400
    
    try:
        result = service.delete_all_data()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
```

## 📊 日志记录标准

### API访问日志
```python
@{entity}_blueprint.before_request
def log_request():
    logger.info(f"API请求: {request.method} {request.url}")

@{entity}_blueprint.after_request  
def log_response(response):
    logger.info(f"API响应: {response.status_code}")
    return response
```

## 🏥 健康检查端点

### 标准健康检查
```python
@{entity}_blueprint.route('/api/v1/{entity}/health', methods=['GET'])
def health_check():
    """服务状态检查"""
    try:
        # 检查服务可用性
        stats = service.get_statistics()
        return jsonify({
            "success": True,
            "status": "healthy",
            "service": "{entity}_service",
            "data_count": stats.get('total_count', 0),
            "last_updated": stats.get('last_updated')
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "status": "unhealthy", 
            "error": str(e)
        }), 503
```
