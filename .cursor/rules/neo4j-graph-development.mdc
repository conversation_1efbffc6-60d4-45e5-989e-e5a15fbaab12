---
description: 
globs: 
alwaysApply: false
---
# Neo4j 图数据库开发规范

## 核心文件位置

- **数据库配置**: [app/config/database.py](mdc:app/config/database.py)
- **节点模型**: [app/models/nodes/](mdc:app/models/nodes)
- **关系模型**: [app/models/relationships/](mdc:app/models/relationships)
- **数据服务**: [app/services/data_storage/](mdc:app/services/data_storage)

## 节点模型开发规范

### 1. 节点类命名约定
```python
# ✅ 正确的节点类命名
class PersonNode(BaseNode):
    """人员节点：投保人、被保险人等"""
    pass

class PolicyNode(BaseNode):
    """保单节点：保险合同信息"""
    pass

# ❌ 错误的命名
class Person(BaseModel):  # 缺少Node后缀
class policy_node(BaseNode):  # 小写命名
```

### 2. 节点属性定义
```python
from pydantic import Field
from typing import Optional

class PersonNode(BaseNode):
    """人员节点"""
    # 必填属性
    name: str = Field(..., description="姓名")
    id_card: str = Field(..., description="身份证号（加密存储）")
    
    # 可选属性
    phone: Optional[str] = Field(None, description="联系电话")
    address: Optional[str] = Field(None, description="联系地址")
    
    # Neo4j 标签
    __neo4j_labels__ = ["Person", "Individual"]
```

### 3. 节点验证规则
- 所有敏感信息（身份证、电话等）必须加密存储
- 使用 Pydantic 进行数据验证
- 必须定义 `__neo4j_labels__` 属性
- 字段描述必须使用中文

## 关系模型开发规范

### 1. 关系类命名
```python
# ✅ 正确的关系命名
class PolicyHolderRelationship(BaseRelationship):
    """投保关系：人员 -[投保]-> 保单"""
    __relationship_type__ = "POLICY_HOLDER"

class InsuredRelationship(BaseRelationship):
    """被保险关系：人员 -[被保险]-> 保单"""
    __relationship_type__ = "INSURED"
```

### 2. 关系属性定义
```python
class PolicyHolderRelationship(BaseRelationship):
    """投保关系"""
    start_date: str = Field(..., description="投保开始日期")
    premium: float = Field(..., description="保费金额")
    
    __relationship_type__ = "POLICY_HOLDER"
    __start_node_labels__ = ["Person"]
    __end_node_labels__ = ["Policy"]
```

## Cypher 查询规范

### 1. 查询构造原则
```python
# ✅ 推荐的查询方式
def get_person_policies(person_id: str) -> str:
    """获取人员的所有保单"""
    return """
    MATCH (p:Person {id: $person_id})-[r:POLICY_HOLDER]->(pol:Policy)
    RETURN p, r, pol
    ORDER BY r.start_date DESC
    """

# ❌ 避免的查询方式
def bad_query():
    return "MATCH (n) RETURN n"  # 过于宽泛，性能差
```

### 2. 参数化查询
```python
# ✅ 始终使用参数化查询
def find_nodes_by_name(name: str):
    query = "MATCH (n:Person {name: $name}) RETURN n"
    params = {"name": name}
    return query, params

# ❌ 避免字符串拼接
def bad_query(name: str):
    return f"MATCH (n:Person {{name: '{name}'}}) RETURN n"  # SQL注入风险
```

### 3. 性能优化
- 始终在查询中使用索引字段（如 id、name）
- 限制返回结果数量：`LIMIT 1000`
- 对于复杂查询使用 `EXPLAIN` 分析执行计划
- 避免笛卡尔积：确保节点间有明确的关系路径

## 数据导入规范

### 1. 批量导入策略
```python
# ✅ 推荐的批量导入方式
async def batch_import_nodes(nodes: List[BaseNode], batch_size: int = 1000):
    """批量导入节点"""
    for i in range(0, len(nodes), batch_size):
        batch = nodes[i:i + batch_size]
        await import_node_batch(batch)
        logger.info(f"已导入 {i + len(batch)} / {len(nodes)} 个节点")
```

### 2. 事务管理
```python
# ✅ 正确的事务处理
with neo4j_driver.session() as session:
    with session.begin_transaction() as tx:
        try:
            # 执行多个相关操作
            tx.run(create_nodes_query, nodes_data)
            tx.run(create_relationships_query, rel_data)
            tx.commit()
        except Exception as e:
            tx.rollback()
            logger.error(f"事务失败：{e}")
            raise
```

### 3. 数据验证
- 导入前验证数据完整性
- 检查必填字段
- 验证关系的起始和结束节点存在性
- 记录导入过程中的错误和警告

## 索引和约束管理

### 1. 创建索引
```cypher
-- 为常用查询字段创建索引
CREATE INDEX person_id_index FOR (p:Person) ON (p.id);
CREATE INDEX policy_number_index FOR (pol:Policy) ON (pol.policy_number);
CREATE INDEX organization_code_index FOR (org:Organization) ON (org.code);
```

### 2. 唯一性约束
```cypher
-- 确保关键字段的唯一性
CREATE CONSTRAINT person_id_unique FOR (p:Person) REQUIRE p.id IS UNIQUE;
CREATE CONSTRAINT policy_number_unique FOR (pol:Policy) REQUIRE pol.policy_number IS UNIQUE;
```

## 错误处理和日志

### 1. 异常处理
```python
from neo4j.exceptions import Neo4jError

try:
    result = session.run(query, parameters)
    return result.data()
except Neo4jError as e:
    logger.error(f"Neo4j 查询失败：{query} - 错误：{e}")
    raise
except Exception as e:
    logger.error(f"未知错误：{e}")
    raise
```

### 2. 日志记录
```python
# 查询日志
logger.info(f"执行 Cypher 查询：{query}")
logger.debug(f"查询参数：{parameters}")

# 性能日志
start_time = time.time()
result = session.run(query, parameters)
execution_time = time.time() - start_time
logger.info(f"查询执行时间：{execution_time:.2f}秒")
```

## 测试规范

### 1. 单元测试
```python
import pytest
from app.models.nodes.person_node import PersonNode

def test_person_node_creation():
    """测试人员节点创建"""
    person = PersonNode(
        name="张三",
        id_card="encrypted_id_card",
        phone="encrypted_phone"
    )
    assert person.name == "张三"
    assert "Person" in person.__neo4j_labels__
```

### 2. 集成测试
```python
@pytest.mark.integration
def test_person_policy_relationship():
    """测试人员-保单关系创建"""
    # 创建测试数据
    person = create_test_person()
    policy = create_test_policy()
    
    # 创建关系
    relationship = create_policy_holder_relationship(person, policy)
    
    # 验证关系
    assert relationship.start_node == person.id
    assert relationship.end_node == policy.id
```

## 监控和维护

### 1. 性能监控
- 监控慢查询（执行时间超过5秒）
- 定期检查索引使用情况
- 监控内存使用和连接池状态

### 2. 数据维护
```python
# 定期清理孤立节点
def cleanup_orphaned_nodes():
    """清理没有关系的孤立节点"""
    query = """
    MATCH (n)
    WHERE NOT (n)--()
    DELETE n
    """
    session.run(query)
```

这些规范确保了 Neo4j 图数据库的高效开发和维护，保证数据质量和查询性能。
