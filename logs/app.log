2025-07-23 09:37:21 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取区域统计
2025-07-23 09:37:21 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:21 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:22 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:24 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:24 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:25 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:25 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 09:37:28 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:28 | INFO     | app.services.data_storage.neo4j_storage:get_node_statistics:470 - InsuranceProduct 节点统计: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:28 | INFO     | app.services.node_service.insurance_product_service:get_insurance_product_statistics:129 - 获取保险产品统计信息: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_storage:get_node_statistics:470 - InsuranceProduct 节点统计: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.services.node_service.insurance_product_service:get_insurance_product_statistics:129 - 获取保险产品统计信息: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取区域统计
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:30 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:31 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:31 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:32 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:32 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:33 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:33 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取保单统计
2025-07-23 09:37:33 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:37 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 6.67ms
2025-07-23 09:37:39 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 6, 节点数: 12, 关系数: 6, 处理时间: 1.96ms
2025-07-23 09:38:05 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 09:38:49 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 2, 节点数: 4, 关系数: 2, 处理时间: 1.90ms
2025-07-23 09:38:51 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 20, 节点数: 40, 关系数: 20, 处理时间: 6.75ms
