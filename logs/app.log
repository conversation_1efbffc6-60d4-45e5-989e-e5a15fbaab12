2025-07-23 09:37:21 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取区域统计
2025-07-23 09:37:21 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:21 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:22 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:23 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:24 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:24 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:25 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:25 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 09:37:28 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:28 | INFO     | app.services.data_storage.neo4j_storage:get_node_statistics:470 - InsuranceProduct 节点统计: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:28 | INFO     | app.services.node_service.insurance_product_service:get_insurance_product_statistics:129 - 获取保险产品统计信息: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_storage:get_node_statistics:470 - InsuranceProduct 节点统计: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.services.node_service.insurance_product_service:get_insurance_product_statistics:129 - 获取保险产品统计信息: {'total_count': 235, 'last_updated': '2025-05-28 23:47:22'}
2025-07-23 09:37:29 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取区域统计
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:29 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:30 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:31 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:31 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:32 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:32 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:33 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:33 | INFO     | app.utils.controller_base:wrapper:161 - API调用: 获取保单统计
2025-07-23 09:37:33 | INFO     | app.services.data_storage.neo4j_client:connect:64 - 成功连接到 Neo4j 数据库: bolt://172.16.11.192:7687
2025-07-23 09:37:37 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 6.67ms
2025-07-23 09:37:39 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 6, 节点数: 12, 关系数: 6, 处理时间: 1.96ms
2025-07-23 09:38:05 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 09:38:49 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 2, 节点数: 4, 关系数: 2, 处理时间: 1.90ms
2025-07-23 09:38:51 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 20, 节点数: 40, 关系数: 20, 处理时间: 6.75ms
2025-07-23 10:08:06 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.38ms
2025-07-23 10:08:30 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.91ms
2025-07-23 10:10:32 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 10:10:38 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.16ms
2025-07-23 10:10:45 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 10:10:55 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.89ms
2025-07-23 10:11:25 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 10:11:29 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.74ms
2025-07-23 10:11:29 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.67ms
2025-07-23 10:11:29 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.86ms
2025-07-23 10:11:29 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.64ms
2025-07-23 10:11:29 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.55ms
2025-07-23 10:11:31 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.40ms
2025-07-23 10:11:31 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.48ms
2025-07-23 10:11:32 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 6.22ms
2025-07-23 10:11:41 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 6.74ms
2025-07-23 10:11:42 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.71ms
2025-07-23 10:11:43 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.50ms
2025-07-23 10:11:45 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 8.16ms
2025-07-23 10:11:51 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 58.98ms
2025-07-23 10:11:56 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 1.76ms
2025-07-23 10:11:57 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.81ms
2025-07-23 10:11:58 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.37ms
2025-07-23 10:12:04 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 10:12:07 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.07ms
2025-07-23 10:12:07 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.42ms
2025-07-23 10:12:08 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.64ms
2025-07-23 10:12:09 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.38ms
2025-07-23 10:12:10 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.47ms
2025-07-23 10:12:11 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.25ms
2025-07-23 10:12:13 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.23ms
2025-07-23 10:12:13 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.49ms
2025-07-23 10:12:14 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.50ms
2025-07-23 10:12:15 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 1.68ms
2025-07-23 10:12:19 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.75ms
2025-07-23 10:12:19 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.30ms
2025-07-23 10:12:21 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.32ms
2025-07-23 10:12:21 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.93ms
2025-07-23 10:12:21 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.36ms
2025-07-23 10:12:22 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.29ms
2025-07-23 10:12:23 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 40.13ms
2025-07-23 10:12:23 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.53ms
2025-07-23 10:12:24 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
2025-07-23 10:12:26 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.86ms
2025-07-23 10:12:26 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.47ms
2025-07-23 10:12:31 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 6.13ms
2025-07-23 10:12:31 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.35ms
2025-07-23 10:12:31 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.96ms
2025-07-23 10:12:35 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 2.33ms
2025-07-23 10:12:35 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 5.03ms
2025-07-23 10:12:37 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.94ms
2025-07-23 10:12:37 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.10ms
2025-07-23 10:12:38 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.85ms
2025-07-23 10:12:38 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 4.82ms
2025-07-23 10:12:39 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 1.99ms
2025-07-23 10:12:39 | INFO     | app.controllers.graph.neo4j_browser:process_query_result:298 - 查询结果处理完成 - 记录数: 25, 节点数: 50, 关系数: 25, 处理时间: 3.95ms
2025-07-23 10:13:19 | INFO     | app.services.data_storage.neo4j_simple_client:close:63 - Neo4j 驱动已关闭
