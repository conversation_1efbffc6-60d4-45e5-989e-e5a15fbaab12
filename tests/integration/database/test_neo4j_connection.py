#!/usr/bin/env python3
"""
Neo4j数据库连接集成测试

测试与Neo4j数据库的连接和基本操作
"""

import pytest
import sys
from pathlib import Path

# 项目路径设置
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.data_storage.neo4j_storage import Neo4jDataStorage


class TestNeo4jConnection:
    """测试Neo4j数据库连接"""

    def test_connection_creation(self):
        """测试连接对象创建"""
        storage = Neo4jDataStorage()
        assert storage is not None

    @pytest.mark.integration
    def test_database_connection(self):
        """测试数据库连接"""
        storage = Neo4jDataStorage()
        
        try:
            success = storage.test_connection()
            assert isinstance(success, bool)
            
            if success:
                print("✅ Neo4j连接测试通过")
            else:
                pytest.skip("Neo4j数据库不可用")
                
        except Exception as e:
            pytest.skip(f"Neo4j连接失败: {str(e)}")

    @pytest.mark.integration
    def test_simple_query(self):
        """测试简单查询"""
        storage = Neo4jDataStorage()
        
        try:
            if not storage.test_connection():
                pytest.skip("Neo4j数据库不可用")
            
            # 执行简单查询
            result = storage.execute_query("RETURN 1 as test_value")
            
            assert isinstance(result, list)
            if result:
                assert result[0]['test_value'] == 1
                print("✅ 简单查询测试通过")
            
        except Exception as e:
            pytest.fail(f"查询执行失败: {str(e)}")

    @pytest.mark.integration
    def test_node_statistics(self):
        """测试节点统计功能"""
        storage = Neo4jDataStorage()
        
        try:
            if not storage.test_connection():
                pytest.skip("Neo4j数据库不可用")
            
            # 获取统计信息
            stats = storage.get_node_statistics("InsuranceProduct")
            
            assert isinstance(stats, dict)
            assert 'total_count' in stats
            print(f"✅ 统计查询测试通过，产品节点数: {stats.get('total_count', 0)}")
            
        except Exception as e:
            pytest.fail(f"统计查询失败: {str(e)}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 