# 测试目录

## 目录结构

```
tests/
├── unit/              # 单元测试
├── integration/       # 集成测试
├── fixtures/          # 测试数据
└── README.md         # 本文件
```

## 测试约定

### 单元测试 (unit/)
- 测试单个函数或类方法
- 文件命名: `test_{module_name}.py`
- 不依赖外部服务（数据库、网络）

### 集成测试 (integration/)
- 测试完整的业务流程
- 文件命名: `test_integration_{feature_name}.py`
- 可以连接真实的服务和数据库

### 测试数据 (fixtures/)
- 测试用的示例数据文件
- 格式: JSON, CSV等
- 文件命名: `{data_type}_{purpose}.json`

## 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 只运行单元测试
python -m pytest tests/unit/

# 只运行集成测试
python -m pytest tests/integration/

# 运行特定测试文件
python -m pytest tests/unit/test_base_model.py
```

## 注意事项

- 所有测试文件必须放在这个目录下
- 严禁在项目根目录创建测试文件
- 测试文件必须以`test_`开头
- 集成测试需要配置正确的环境变量 