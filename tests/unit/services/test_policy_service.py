#!/usr/bin/env python3
"""
保单服务测试

测试PolicyService的基本功能
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# 项目路径设置
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.node_service.policy_service import PolicyService


class TestPolicyService:
    """测试保单服务"""

    def test_service_initialization(self):
        """测试服务初始化"""
        service = PolicyService()
        
        assert service is not None
        assert hasattr(service, 'page_size')
        assert service.page_size > 0

    @patch('app.services.policy_service.PolicyService._build_query')
    def test_build_query_called(self, mock_build_query):
        """测试查询构建方法被调用"""
        mock_build_query.return_value = "SELECT * FROM test"
        
        service = PolicyService()
        query = service._build_query(0, 100, "2024-01-01", "2024-01-31")
        
        assert mock_build_query.called
        assert query == "SELECT * FROM test"

    def test_page_size_configuration(self):
        """测试分页大小配置"""
        service = PolicyService()
        
        # 检查默认分页大小
        assert service.page_size == 2000

    @pytest.mark.integration 
    def test_search_policies(self):
        """集成测试：搜索保单（需要数据库连接）"""
        service = PolicyService()
        
        try:
            policies = service.search_policies(limit=1)
            assert isinstance(policies, list)
        except Exception:
            # 如果没有数据库连接，跳过此测试
            pytest.skip("需要数据库连接")


if __name__ == "__main__":
    pytest.main([__file__]) 