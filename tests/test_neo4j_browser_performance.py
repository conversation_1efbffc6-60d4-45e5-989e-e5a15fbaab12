"""
测试Neo4j Browser性能优化
验证修复后的代码能正确处理各种Neo4j数据格式，确保性能问题得到解决
"""
import pytest
import time
from unittest.mock import Mock, patch
from app.controllers.graph.neo4j_browser import Neo4jBrowserService, ResponseFormat


class TestNeo4jBrowserPerformance:
    """测试Neo4j Browser性能优化"""
    
    def setup_method(self):
        """设置测试环境"""
        self.service = Neo4jBrowserService()
    
    def test_relationship_detection_performance(self):
        """测试关系检测性能 - 确保不会重复处理"""
        # 模拟Neo4j关系对象
        mock_relationship = Mock()
        mock_relationship.type = "PARENT_OF"
        mock_relationship.start_node = Mock()
        mock_relationship.end_node = Mock()
        mock_relationship.start_node._properties = {"business_id": "1", "name": "公司A"}
        mock_relationship.end_node._properties = {"business_id": "2", "name": "公司B"}
        mock_relationship._properties = {}
        
        # 模拟查询结果 - 25条记录，每条包含n, r, m, n_labels, m_labels
        raw_results = []
        for i in range(25):
            raw_results.append({
                "n": {"business_id": f"{i}", "name": f"节点{i}"},
                "r": mock_relationship,
                "m": {"business_id": f"{i+100}", "name": f"节点{i+100}"},
                "n_labels": ["Organization"],
                "m_labels": ["Organization"]
            })
        
        # 测试处理时间
        start_time = time.time()
        result = self.service.process_query_result(
            raw_results, 
            "MATCH (n)-[r]-(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels LIMIT 25",
            ResponseFormat.REFERENCE
        )
        processing_time = (time.time() - start_time) * 1000
        
        # 验证结果
        assert result is not None
        assert len(result.nodes) > 0
        assert len(result.relationships) > 0
        assert processing_time < 1000  # 应该在1秒内完成
        
        # 验证性能监控信息
        assert "performance" in result.summary
        perf = result.summary["performance"]
        assert "processing_time_ms" in perf
        assert "record_count" in perf
        assert "node_count" in perf
        assert "relationship_count" in perf
        assert perf["record_count"] == 25
    
    def test_relationship_detection_logic(self):
        """测试关系检测逻辑 - 确保正确识别不同类型的数据"""
        # 1. 测试Neo4j关系对象
        mock_relationship = Mock()
        mock_relationship.type = "PARENT_OF"
        mock_relationship.start_node = Mock()
        mock_relationship.end_node = Mock()
        
        assert self.service._is_relationship(mock_relationship) == True
        
        # 2. 测试节点对象（不应该被识别为关系）
        node_data = {"business_id": "1", "name": "公司A"}
        assert self.service._is_relationship(node_data) == False
        
        # 3. 测试关系字典（有type但没有business_id）
        rel_dict = {"type": "PARENT_OF", "start_id": "1", "end_id": "2"}
        assert self.service._is_relationship(rel_dict) == True
        
        # 4. 测试tuple格式关系
        rel_tuple = (
            {"business_id": "1", "name": "公司A"},
            "PARENT_OF",
            {"business_id": "2", "name": "公司B"}
        )
        assert self.service._is_relationship(rel_tuple) == True
        
        # 5. 测试普通列表（不应该被识别为关系）
        normal_list = ["a", "b", "c"]
        assert self.service._is_relationship(normal_list) == False
        
        # 6. 测试标签列表（不应该被识别为关系）
        labels_list = ["Organization", "Company"]
        assert self.service._is_relationship(labels_list) == False
    
    def test_no_duplicate_processing(self):
        """测试不会重复处理同一个关系"""
        # 创建一个关系，确保它只被处理一次
        mock_relationship = Mock()
        mock_relationship.type = "PARENT_OF"
        mock_relationship.start_node = Mock()
        mock_relationship.end_node = Mock()
        mock_relationship.start_node._properties = {"business_id": "1"}
        mock_relationship.end_node._properties = {"business_id": "2"}
        mock_relationship._properties = {}
        
        raw_results = [{
            "r": mock_relationship,
            "n": {"business_id": "1", "name": "节点1"},
            "m": {"business_id": "2", "name": "节点2"}
        }]
        
        # 使用patch来监控日志调用
        with patch('app.controllers.graph.neo4j_browser.logger') as mock_logger:
            result = self.service.process_query_result(
                raw_results,
                "MATCH (n)-[r]-(m) RETURN n, r, m",
                ResponseFormat.REFERENCE
            )
            
            # 验证结果
            assert len(result.relationships) == 1
            
            # 验证日志调用次数 - debug级别的日志应该被调用，但不应该重复
            debug_calls = [call for call in mock_logger.debug.call_args_list 
                          if "检测到关系" in str(call)]
            assert len(debug_calls) == 1  # 只应该检测到一次关系
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        # 模拟大量数据
        mock_relationship = Mock()
        mock_relationship.type = "PARENT_OF"
        mock_relationship.start_node = Mock()
        mock_relationship.end_node = Mock()
        mock_relationship.start_node._properties = {"business_id": "1"}
        mock_relationship.end_node._properties = {"business_id": "2"}
        mock_relationship._properties = {}
        
        # 创建1000条记录
        raw_results = []
        for i in range(1000):
            raw_results.append({
                "n": {"business_id": f"{i}", "name": f"节点{i}"},
                "r": mock_relationship,
                "m": {"business_id": f"{i+1000}", "name": f"节点{i+1000}"},
                "n_labels": ["Organization"],
                "m_labels": ["Organization"]
            })
        
        start_time = time.time()
        result = self.service.process_query_result(
            raw_results,
            "MATCH (n)-[r]-(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels",
            ResponseFormat.REFERENCE
        )
        processing_time = (time.time() - start_time) * 1000
        
        # 验证性能 - 1000条记录应该在5秒内处理完成
        assert processing_time < 5000
        assert result.summary["performance"]["record_count"] == 1000
        
        print(f"处理1000条记录耗时: {processing_time:.2f}ms")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
