# Flask 配置
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_SECRET_KEY=your-secret-key-here

# 数仓数据库连接配置
IMPALA_HOST=*************
IMPALA_PORT=21050
IMPALA_DATABASE=default
IMPALA_QUERY_TIMEOUT=300

# 认证配置 (NONE, KERBEROS, LDAP)
IMPALA_AUTH_TYPE=NONE
IMPALA_USERNAME=bzn_reader
IMPALA_PASSWORD=bzn_reader

# Kerberos配置(当AUTH_TYPE=KERBEROS时)
IMPALA_PRINCIPAL=impala/<EMAIL>

# SSL配置
IMPALA_USE_SSL=False
IMPALA_SSL_TRUSTSTORE=/path/to/truststore.pem

# Neo4j 连接配置
NEO4J_URI=bolt://*************:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=bzn@1234
NEO4J_DATABASE=neo4j

# 数据加密配置
ENCRYPTION_KEY=your-encryption-key-here

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# API 配置
API_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000 